import cv2
import numpy as np
import matplotlib.pyplot as plt
from PIL import Image
import math
import json
import os
import tkinter as tk
from tkinter import filedialog, simpledialog, messagebox, Canvas
import pandas as pd
from scipy.spatial.distance import cdist
import heapq
from itertools import combinations
import time
from tqdm import tqdm  # 진행률 표시를 위한 라이브러리

# 전역 변수로 클릭된 핀과 우선순위 영역 저장
clicked_pins = []
priority_areas = []
current_priority_area = None
priority_pin_selections = {}  # {area_id: {"pins": set(), "weight": float}}

def estimate_required_iterations(grid_cells):
    """
    모든 셀이 요구하는 교차 수를 기반으로 반복 횟수를 추정합니다.
    """
    total_required_crossings = sum(cell["value"] for cell in grid_cells)
    average_crossings_per_line = 3  # 경험적으로 한 줄이 평균적으로 지나는 셀 수
    estimated_iterations = int(total_required_crossings / average_crossings_per_line)
    return estimated_iterations

def ask_save_path(title, defaultextension, filetypes):
    """
    tkinter 파일 저장 대화상자를 열어 저장 경로를 선택하도록 합니다.
    """
    root = tk.Tk()
    root.withdraw()
    path = filedialog.asksaveasfilename(title=title,
                                        defaultextension=defaultextension,
                                        filetypes=filetypes)
    root.destroy()
    return path

def ask_block_size():
    """
    tkinter를 사용하여 블록 크기를 입력받습니다.
    """
    root = tk.Tk()
    root.withdraw()
    block_size = simpledialog.askinteger("블록 크기 입력", "블록 크기를 입력하세요 (기본값: 10):", minvalue=1, initialvalue=10)
    root.destroy()
    return block_size

def ask_levels():
    """
    tkinter를 사용하여 levels 값을 입력받습니다.
    """
    root = tk.Tk()
    root.withdraw()
    levels = simpledialog.askinteger("Levels 입력", "Levels 값을 입력하세요 (기본값: 50):", minvalue=1, initialvalue=50)
    root.destroy()
    return levels

def ask_canvas_type():
    """
    tkinter를 사용하여 캔버스 타입(사각형, 정삼각형, 동그라미)을 선택합니다.
    """
    root = tk.Tk()
    root.title("캔버스 타입 선택")
    root.geometry("300x200")
    
    canvas_type = tk.StringVar(value="동그라미")
    
    tk.Radiobutton(root, text="사각형", variable=canvas_type, value="사각형").pack(pady=10)
    tk.Radiobutton(root, text="정삼각형", variable=canvas_type, value="정삼각형").pack(pady=10)
    tk.Radiobutton(root, text="동그라미", variable=canvas_type, value="동그라미").pack(pady=10)
    
    def on_confirm():
        root.quit()
    
    tk.Button(root, text="확인", command=on_confirm).pack(pady=20)
    
    root.update_idletasks()
    width = root.winfo_width()
    height = root.winfo_height()
    x = (root.winfo_screenwidth() // 2) - (width // 2)
    y = (root.winfo_screenheight() // 2) - (height // 2)
    root.geometry(f'{width}x{height}+{x}+{y}')
    
    root.mainloop()
    selected_type = canvas_type.get()
    root.destroy()
    return selected_type

def calculate_optimal_canvas_size(image, canvas_type, margin, dpi=300):
    """
    이미지가 캔버스를 벗어나지 않는 최적의 캔버스 크기를 계산합니다.
    """
    cm_to_pixel = lambda x: int(x * dpi / 2.54)
    h_image, w_image = image.shape[:2]
    margin_px = cm_to_pixel(margin)
    
    margin_factor = 1.1
    
    if canvas_type == "사각형":
        width_cm = (w_image + 2 * margin_px) * 2.54 / dpi * margin_factor
        height_cm = (h_image + 2 * margin_px) * 2.54 / dpi * margin_factor
        return (width_cm, height_cm)
    elif canvas_type == "정삼각형":
        max_dimension = max(w_image, h_image)
        side_cm = (max_dimension + 2 * margin_px) * 2.54 / dpi * margin_factor
        return side_cm
    elif canvas_type == "동그라미":
        max_dimension = max(w_image, h_image)
        radius_cm = (max_dimension/2 + margin_px) * 2.54 / dpi * margin_factor
        return radius_cm
    return None

def ask_canvas_size(canvas_type, image=None, margin=None):
    """
    tkinter를 사용하여 캔버스 크기를 입력받거나 자동 설정합니다.
    """
    root = tk.Tk()
    root.withdraw()
    
    if image is not None and margin is not None:
        optimal_size = calculate_optimal_canvas_size(image, canvas_type, margin)
        
        message = f"최적의 캔버스 크기가 계산되었습니다:\n"
        if canvas_type == "사각형":
            message += f"가로: {optimal_size[0]:.1f}cm, 세로: {optimal_size[1]:.1f}cm\n"
        else:
            message += f"크기: {optimal_size:.1f}cm\n"
        message += "\n이 크기를 사용하시겠습니까?\n"
        message += "아니오를 선택하면 수동으로 크기를 설정할 수 있습니다."
        
        choice = messagebox.askyesno("캔버스 크기 설정", message)
        
        if choice:
            return optimal_size
    
    if canvas_type == "사각형":
        width = simpledialog.askfloat("사각형 가로 길이 입력", "가로 길이(cm)를 입력하세요 (기본값: 10):", minvalue=1, initialvalue=10)
        height = simpledialog.askfloat("사각형 세로 길이 입력", "세로 길이(cm)를 입력하세요 (기본값: 10):", minvalue=1, initialvalue=10)
        size = (width, height)
    elif canvas_type == "정삼각형":
        side = simpledialog.askfloat("정삼각형 한 변의 길이 입력", "한 변의 길이(cm)를 입력하세요 (기본값: 10):", minvalue=1, initialvalue=10)
        size = side
    elif canvas_type == "동그라미":
        radius = simpledialog.askfloat("동그라미 반지름 입력", "반지름(cm)을 입력하세요 (기본값: 10):", minvalue=1, initialvalue=10)
        size = radius
    else:
        size = None
    
    root.destroy()
    return size

def ask_image_scale():
    """
    tkinter를 사용하여 이미지 크기 조정 비율(%)을 입력받습니다.
    """
    root = tk.Tk()
    root.withdraw()
    scale = simpledialog.askfloat("이미지 크기 조정", "이미지 크기 조정 비율(%)을 입력하세요 (기본값: 100):", minvalue=1, initialvalue=100)
    root.destroy()
    return scale / 100

def ask_margin():
    """
    tkinter를 사용하여 가장자리 점 간격(cm)을 입력받습니다.
    """
    root = tk.Tk()
    root.withdraw()
    margin = simpledialog.askfloat("가장자리 점 간격 입력", "가장자리 점 간격(cm)을 입력하세요 (기본값: 0.2):", minvalue=0.1, initialvalue=0.2)
    root.destroy()
    return margin

def ask_time_limit():
    """
    tkinter를 사용하여 최대 실행 시간(초)을 입력받습니다.
    """
    root = tk.Tk()
    root.withdraw()
    time_limit = simpledialog.askinteger("최대 실행 시간 입력", "최대 실행 시간(초)을 입력하세요 (기본값: 300):", minvalue=1, initialvalue=300)
    root.destroy()
    return time_limit

def ask_max_iterations():
    """
    tkinter를 사용하여 최대 반복 횟수를 입력받습니다.
    """
    root = tk.Tk()
    root.withdraw()
    max_iterations = simpledialog.askinteger("최대 반복 횟수 입력", "최대 반복 횟수를 입력하세요 (기본값: 1000):", minvalue=1, initialvalue=1000)
    root.destroy()
    return max_iterations

def ask_priority_weight(area_id):
    """
    우선순위 영역에 대한 가중치를 입력받습니다.
    """
    root = tk.Tk()
    root.withdraw()
    weight = simpledialog.askfloat(f"우선순위 영역 {area_id} 가중치 입력",
                                   f"우선순위 영역 {area_id}의 가중치를 입력하세요 (기본값: 3):",
                                   minvalue=1, initialvalue=3)
    root.destroy()
    return weight if weight is not None else 3

def ask_use_priority_areas():
    """
    우선순위 영역 사용 여부를 묻습니다.
    """
    root = tk.Tk()
    root.withdraw()
    use_priority = messagebox.askyesno("우선순위 영역", "우선순위 영역을 지정하시겠습니까?")
    root.destroy()
    return use_priority

def create_canvas(canvas_type, size, dpi=300):
    """
    선택한 캔버스 타입과 크기에 따라 캔버스를 생성합니다.
    """
    cm_to_pixel = lambda x: int(x * dpi / 2.54)
    if canvas_type == "사각형":
        width, height = size
        canvas = np.ones((cm_to_pixel(height), cm_to_pixel(width), 3), dtype=np.uint8) * 255
    elif canvas_type == "정삼각형":
        side = size
        height = side * math.sqrt(3) / 2
        canvas = np.ones((cm_to_pixel(height), cm_to_pixel(side), 3), dtype=np.uint8) * 255
    elif canvas_type == "동그라미":
        radius = size
        diameter = cm_to_pixel(radius * 2)
        canvas = np.ones((diameter, diameter, 3), dtype=np.uint8) * 255
        cv2.circle(canvas, (diameter // 2, diameter // 2), cm_to_pixel(radius), (0, 0, 0), 2)
    else:
        canvas = None
    return canvas

def resize_image(image, scale):
    """
    이미지 크기를 비율에 따라 조정합니다.
    """
    width = int(image.shape[1] * scale)
    height = int(image.shape[0] * scale)
    return cv2.resize(image, (width, height))

def place_image_on_canvas(canvas, image, canvas_type):
    """
    이미지를 캔버스 중앙에 배치합니다.
    """
    h_canvas, w_canvas = canvas.shape[:2]
    h_image, w_image = image.shape[:2]
    
    if h_image > h_canvas or w_image > w_canvas:
        message = f"이미지 크기({h_image}x{w_image})가 캔버스 크기({h_canvas}x{w_canvas})보다 큽니다.\n"
        message += "다음 중 하나를 선택하세요:\n"
        message += "1. 이미지 크기 조정 비율을 더 작게 설정\n"
        message += "2. 캔버스 크기를 더 크게 설정\n"
        message += "3. 프로그램 종료"
        
        root = tk.Tk()
        root.withdraw()
        choice = messagebox.askquestion("크기 불일치", message + "\n\n다시 설정하시겠습니까?")
        root.destroy()
        
        if choice == 'yes':
            scale = ask_image_scale()
            if scale:
                image = resize_image(image, scale)
                return place_image_on_canvas(canvas, image, canvas_type)
            else:
                canvas_size = ask_canvas_size(canvas_type)
                if canvas_size:
                    canvas = create_canvas(canvas_type, canvas_size)
                    return place_image_on_canvas(canvas, image, canvas_type)
        
        raise ValueError("사용자가 작업을 취소했습니다.")
    
    x_offset = (w_canvas - w_image) // 2
    y_offset = (h_canvas - h_image) // 2
    canvas[y_offset:y_offset+h_image, x_offset:x_offset+w_image] = image
    return canvas

def draw_numbered_points(canvas, canvas_type, size, margin, dpi=300):
    """
    캔버스 가장자리에 번호가 붙은 점을 표시하고 핀 좌표를 반환합니다.
    """
    cm_to_pixel = lambda x: int(x * dpi / 2.54)
    margin_px = cm_to_pixel(margin)
    font = cv2.FONT_HERSHEY_SIMPLEX
    font_scale = 0.5
    font_color = (0, 0, 0)
    thickness = 1
    pins = []

    if canvas_type == "사각형":
        width, height = size
        width_px, height_px = cm_to_pixel(width), cm_to_pixel(height)
        pin_number = 0
        for i in range(0, width_px, margin_px):
            if i < width_px:
                pin_number += 1
                cv2.circle(canvas, (i, margin_px), 2, (0, 0, 255), -1)
                cv2.putText(canvas, str(pin_number), (i, margin_px + 15), font, font_scale, font_color, thickness)
                pins.append({"number": pin_number, "x": i, "y": margin_px})
        for i in range(margin_px, height_px, margin_px):
            if i < height_px:
                pin_number += 1
                cv2.circle(canvas, (width_px - margin_px, i), 2, (0, 0, 255), -1)
                cv2.putText(canvas, str(pin_number), (width_px - margin_px + 15, i), font, font_scale, font_color, thickness)
                pins.append({"number": pin_number, "x": width_px - margin_px, "y": i})
        for i in range(width_px - margin_px, -1, -margin_px):
            if i >= 0:
                pin_number += 1
                cv2.circle(canvas, (i, height_px - margin_px), 2, (0, 0, 255), -1)
                cv2.putText(canvas, str(pin_number), (i, height_px - margin_px + 15), font, font_scale, font_color, thickness)
                pins.append({"number": pin_number, "x": i, "y": height_px - margin_px})
        for i in range(height_px - margin_px, margin_px, -margin_px):
            if i >= 0:
                pin_number += 1
                cv2.circle(canvas, (margin_px, i), 2, (0, 0, 255), -1)
                cv2.putText(canvas, str(pin_number), (margin_px + 15, i), font, font_scale, font_color, thickness)
                pins.append({"number": pin_number, "x": margin_px, "y": i})
    elif canvas_type == "정삼각형":
        side = size
        side_px = cm_to_pixel(side)
        height_px = int(side_px * math.sqrt(3) / 2)
        pin_number = 0
        for i in range(0, side_px, margin_px):
            if i < side_px:
                pin_number += 1
                cv2.circle(canvas, (i, height_px), 2, (0, 0, 255), -1)
                cv2.putText(canvas, str(pin_number), (i, height_px + 15), font, font_scale, font_color, thickness)
                pins.append({"number": pin_number, "x": i, "y": height_px})
        right_count = int((side_px / 2) / margin_px)
        for i in range(right_count):
            x = side_px - i * margin_px * math.cos(math.radians(30))
            y = height_px - i * margin_px * math.sin(math.radians(30))
            if 0 <= x < canvas.shape[1] and 0 <= y < canvas.shape[0]:
                pin_number += 1
                cv2.circle(canvas, (int(x), int(y)), 2, (0, 0, 255), -1)
                cv2.putText(canvas, str(pin_number), (int(x) + 15, int(y)), font, font_scale, font_color, thickness)
                pins.append({"number": pin_number, "x": int(x), "y": int(y)})
        left_count = int((side_px / 2) / margin_px)
        for i in range(left_count):
            x = i * margin_px * math.cos(math.radians(30))
            y = height_px - i * margin_px * math.sin(math.radians(30))
            if 0 <= x < canvas.shape[1] and 0 <= y < canvas.shape[0]:
                pin_number += 1
                cv2.circle(canvas, (int(x), int(y)), 2, (0, 0, 255), -1)
                cv2.putText(canvas, str(pin_number), (int(x) - 15, int(y)), font, font_scale, font_color, thickness)
                pins.append({"number": pin_number, "x": int(x), "y": int(y)})
    elif canvas_type == "동그라미":
        radius = size
        radius_px = cm_to_pixel(radius)
        center = (radius_px, radius_px)
        diameter = radius_px * 2
        pin_number = 0
        
        min_pins = 8
        circumference = 2 * math.pi * radius_px
        pins_count = max(min_pins, int(circumference / margin_px))
        
        angle_step = 360 / pins_count
        
        for angle in range(0, 360, int(angle_step)):
            pin_number += 1
            x = int(center[0] + radius_px * math.cos(math.radians(angle)))
            y = int(center[1] + radius_px * math.sin(math.radians(angle)))
            
            if 0 <= x < canvas.shape[1] and 0 <= y < canvas.shape[0]:
                cv2.circle(canvas, (x, y), 2, (0, 0, 255), -1)
                cv2.putText(canvas, str(pin_number), (x + 1, y + 1), font, font_scale, font_color, thickness)
                pins.append({"number": pin_number, "x": x, "y": y})
    
    return canvas, pins

def interactive_pin_and_priority_selection(canvas_with_pins, pins, canvas_type, size, margin, dpi=300):
    """
    사용자가 마우스로 핀을 추가하고 우선순위 영역을 지정할 수 있는 GUI를 제공합니다.
    """
    global clicked_pins, priority_areas, current_priority_area, priority_pin_selections
    
    clicked_pins = []
    priority_areas = []
    priority_pin_selections = {}
    current_priority_area = None
    
    root = tk.Tk()
    root.title("핀 추가 및 우선순위 영역 지정")
    
    # 캔버스 크기 조정 (최대 800x800으로 제한)
    h, w = canvas_with_pins.shape[:2]
    scale = min(800 / w, 800 / h, 1.0)
    display_w, display_h = int(w * scale), int(h * scale)
    display_canvas = cv2.resize(canvas_with_pins, (display_w, display_h))
    
    # tkinter 캔버스 생성
    tk_canvas = Canvas(root, width=display_w, height=display_h)
    tk_canvas.pack()
    
    # OpenCV 이미지를 PIL 이미지로 변환
    img_rgb = cv2.cvtColor(display_canvas, cv2.COLOR_BGR2RGB)
    img_pil = Image.fromarray(img_rgb)
    img_tk = tk.PhotoImage(image=img_pil)
    
    tk_canvas.create_image(0, 0, anchor=tk.NW, image=img_tk)
    
    # 상태 변수
    start_point = None
    current_rect = None
    
    def scale_to_original(x, y):
        """디스플레이 좌표를 원본 이미지 좌표로 변환"""
        return int(x / scale), int(y / scale)
    
    def scale_to_display(x, y):
        """원본 이미지 좌표를 디스플레이 좌표로 변환"""
        return int(x * scale), int(y * scale)
    
    def find_nearest_pin(x, y):
        """가장 가까운 핀 찾기"""
        distances = [math.sqrt((p["x"] - x) ** 2 + (p["y"] - y) ** 2) for p in pins]
        if not distances:
            return None
        min_idx = np.argmin(distances)
        if distances[min_idx] < 20:  # 20픽셀 이내
            return pins[min_idx]
        return None
    
    def on_left_click(event):
        """마우스 좌클릭: 핀 추가 또는 우선순위 핀 선택"""
        global clicked_pins, current_priority_area
        x, y = scale_to_original(event.x, event.y)
        
        if current_priority_area is not None:
            # 우선순위 영역 내 핀 선택
            pin = find_nearest_pin(x, y)
            if pin:
                area_id = current_priority_area
                if area_id not in priority_pin_selections:
                    priority_pin_selections[area_id] = {"pins": set(), "weight": priority_pin_selections[area_id]["weight"]}
                priority_pin_selections[area_id]["pins"].add(pin["number"])
                # 선택된 핀 표시 (녹색)
                x_display, y_display = scale_to_display(pin["x"], pin["y"])
                tk_canvas.create_oval(x_display-5, y_display-5, x_display+5, y_display+5, fill="green", tags=f"pin_{pin['number']}")
                print(f"영역 {area_id}에 핀 {pin['number']} 추가")
        else:
            # 새로운 핀 추가
            pin_number = max([p["number"] for p in pins]) + 1 if pins else 1
            pins.append({"number": pin_number, "x": x, "y": y})
            clicked_pins.append({"number": pin_number, "x": x, "y": y})
            # 핀 표시 (파란색)
            x_display, y_display = scale_to_display(x, y)
            tk_canvas.create_oval(x_display-5, y_display-5, x_display+5, y_display+5, fill="blue", tags=f"pin_{pin_number}")
            tk_canvas.create_text(x_display, y_display+15, text=str(pin_number), fill="black", tags=f"text_{pin_number}")
            print(f"새 핀 추가: 번호 {pin_number}, 위치 ({x}, {y})")
    
    def on_right_click(event):
        """마우스 우클릭: 우선순위 핀 제거"""
        global current_priority_area
        x, y = scale_to_original(event.x, event.y)
        
        if current_priority_area is not None:
            pin = find_nearest_pin(x, y)
            if pin and current_priority_area in priority_pin_selections:
                if pin["number"] in priority_pin_selections[current_priority_area]["pins"]:
                    priority_pin_selections[current_priority_area]["pins"].discard(pin["number"])
                    # 원래 색상으로 복원 (빨간색)
                    x_display, y_display = scale_to_display(pin["x"], pin["y"])
                    tk_canvas.delete(f"pin_{pin['number']}")
                    tk_canvas.create_oval(x_display-5, y_display-5, x_display+5, y_display+5, fill="red", tags=f"pin_{pin['number']}")
                    print(f"영역 {current_priority_area}에서 핀 {pin['number']} 제거")
    
    def on_left_press(event):
        """마우스 왼쪽 버튼 누름: 우선순위 영역 시작"""
        global start_point, current_rect, current_priority_area
        start_point = (event.x, event.y)
        current_rect = tk_canvas.create_rectangle(start_point[0], start_point[1], start_point[0], start_point[1], outline="red", dash=(4, 4))
        current_priority_area = len(priority_areas) + 1
        print(f"우선순위 영역 {current_priority_area} 시작")
    
    def on_mouse_move(event):
        """마우스 이동: 우선순위 영역 드래그"""
        if start_point and current_rect:
            tk_canvas.coords(current_rect, start_point[0], start_point[1], event.x, event.y)
    
    def on_left_release(event):
        """마우스 왼쪽 버튼 뗌: 우선순위 영역 확정"""
        global start_point, current_rect, current_priority_area
        if start_point and current_rect:
            end_point = (event.x, event.y)
            x1, y1 = scale_to_original(start_point[0], start_point[1])
            x2, y2 = scale_to_original(end_point[0], end_point[1])
            # 영역 정규화
            x_min, x_max = min(x1, x2), max(x1, x2)
            y_min, y_max = min(y1, y2), max(y1, y2)
            priority_areas.append({
                "id": current_priority_area,
                "x_min": x_min,
                "x_max": x_max,
                "y_min": y_min,
                "y_max": y_max
            })
            # 가중치 입력
            weight = ask_priority_weight(current_priority_area)
            priority_pin_selections[current_priority_area] = {"pins": set(), "weight": weight}
            # 사각형 고정 (파란색 실선)
            tk_canvas.itemconfig(current_rect, outline="blue", dash=())
            print(f"우선순위 영역 {current_priority_area} 확정: ({x_min}, {y_min}) to ({x_max}, {y_max}), 가중치: {weight}")
            start_point = None
            current_rect = None
            current_priority_area = None
    
    def on_finish():
        """완료 버튼: GUI 종료"""
        root.quit()
    
    # 이벤트 바인딩
    tk_canvas.bind("<Button-1>", on_left_click)
    tk_canvas.bind("<Button-3>", on_right_click)
    tk_canvas.bind("<ButtonPress-1>", on_left_press)
    tk_canvas.bind("<B1-Motion>", on_mouse_move)
    tk_canvas.bind("<ButtonRelease-1>", on_left_release)
    
    # 완료 버튼
    tk.Button(root, text="완료", command=on_finish).pack(pady=10)
    
    # GUI 실행
    root.mainloop()
    
    # 메모리 누수 방지를 위해 참조 유지
    tk_canvas.image = img_tk
    
    root.destroy()
    
    return pins, priority_areas, priority_pin_selections

def process_image(image_array, block_size=10, levels=50, save_data=False, 
                 json_output_path=None, png_output_path=None, excel_output_path=None, 
                 pins=None, time_limit=300, max_iterations=1000, priority_areas=None, 
                 priority_pin_selections=None, show_progress=True):
    """
    주어진 이미지 배열에 대해 처리 단계를 실행합니다.
    """
    print("이미지 처리 시작...")
    
    # Step 1: 원본 이미지 시각화
    if show_progress:
        fig, ax = plt.subplots(figsize=(8, 8))
        ax.imshow(image_array)
        ax.set_title("Original Image")
        plt.axis('off')
        plt.show()
    
    # Step 2: 회색조 변환
    if len(image_array.shape) == 3:
        gray = cv2.cvtColor(image_array, cv2.COLOR_RGB2GRAY)
    else:
        gray = image_array.copy()
    if show_progress:
        fig, ax = plt.subplots(figsize=(8, 8))
        ax.imshow(gray, cmap='gray')
        ax.set_title("Grayscale Image")
        plt.axis('off')
        plt.show()
    
    # Step 3: 그리드 오버레이
    h, w = gray.shape
    if show_progress:
        fig, ax = plt.subplots(figsize=(8, 8))
        ax.imshow(gray, cmap='gray')
        for i in range(0, h, block_size):
            if i < h:
                ax.axhline(i, color='yellow', linestyle='--', linewidth=0.5)
        for j in range(0, w, block_size):
            if j < w:
                ax.axvline(j, color='yellow', linestyle='--', linewidth=0.5)
        ax.set_title("Grayscale Image with Grid Overlay")
        plt.axis('off')
        plt.show()
    
    # Step 4: 그리드 셀 value 계산 (NumPy 벡터화)
    print("그리드 셀 value 계산 중...")
    rows = math.ceil(h / block_size)
    cols = math.ceil(w / block_size)
    processed_image = np.zeros((rows, cols), dtype=int)
    
    img_center_x = w // 2
    img_center_y = h // 2
    img_width = image_array.shape[1]
    img_height = image_array.shape[0]
    img_start_x = img_center_x - img_width // 2
    img_start_y = img_center_y - img_height // 2
    
    for i in tqdm(range(0, h, block_size), desc="그리드 셀 처리"):
        for j in range(0, w, block_size):
            if (img_start_x <= j < img_start_x + img_width and 
                img_start_y <= i < img_start_y + img_height):
                img_x = j - img_start_x
                img_y = i - img_start_y
                block = gray[img_y:min(img_y+block_size, img_height), 
                           img_x:min(img_x+block_size, img_width)]
                if block.size > 0:
                    avg_gray = np.mean(block)
                    level = int((255 - avg_gray) / 255 * (levels - 1))
                    processed_image[i // block_size, j // block_size] = level
    
    # Step 5: 주석 이미지 및 블록 정보
    print("주석 이미지 생성 중...")
    fig, ax = plt.subplots(figsize=(8, 8))
    ax.imshow(gray, cmap='gray')
    block_info = []
    for i in range(rows):
        for j in range(cols):
            x_center = j * block_size + block_size // 2
            y_center = i * block_size + block_size // 2
            value = int(processed_image[i, j])
            ax.text(x_center, y_center, str(value),
                    color='red', fontsize=8, ha='center', va='center')
            block_info.append({
                "row": i,
                "col": j,
                "center_x": int(x_center),
                "center_y": int(y_center),
                "value": value
            })
    for i in range(0, h, block_size):
        if i < h:
            ax.axhline(i, color='yellow', linestyle='--', linewidth=0.5)
    for j in range(0, w, block_size):
        if j < w:
            ax.axvline(j, color='yellow', linestyle='--', linewidth=0.5)
    ax.set_title("Final Annotated Image")
    plt.axis('off')
    
    grid_value_map = {(cell["center_x"], cell["center_y"]): cell["value"] for cell in block_info}
    
    # 실 경로 계산
    if pins:
        print("실 경로 계산 시작...")
        grid_cells = [block for block in block_info if block["value"] > 0]
        string_route = calculate_string_route_with_time_limit(
            pins, grid_cells, block_size, grid_value_map, time_limit, max_iterations,
            priority_areas, priority_pin_selections
        )
        
        estimated_iters = estimate_required_iterations(grid_cells)
        print(f"🔍 예상 반복 횟수: 약 {estimated_iters}회 (최대 {max_iterations}회 중)")
        
        # 실 경로 시각화
        if show_progress:
            fig, ax = plt.subplots(figsize=(10, 10))
            ax.imshow(gray, cmap='gray')
            
            for pin in pins:
                ax.plot(pin["x"], pin["y"], 'ro', markersize=5)
                ax.text(pin["x"], pin["y"], str(pin["number"]), fontsize=8, color='white')
            
            for i in range(1, len(string_route)):
                try:
                    prev_pin = next(p for p in pins if p["number"] == string_route[i-1])
                    curr_pin = next(p for p in pins if p["number"] == string_route[i])
                    ax.plot([prev_pin["x"], curr_pin["x"]], [prev_pin["y"], curr_pin["y"]], 'b-', linewidth=0.5, alpha=0.5)
                except StopIteration:
                    print(f"경고: 핀 번호 {string_route[i-1]} 또는 {string_route[i]}을(를) 찾을 수 없습니다.")
            
            ax.set_title("String Art Route with Image")
            plt.axis('off')
            plt.show()
            
            # 실 경로만 시각화
            fig_only_string, ax_only_string = plt.subplots(figsize=(10, 10))
            white_background = np.ones((h, w, 3), dtype=np.uint8) * 255
            ax_only_string.imshow(white_background)
            
            for pin in pins:
                ax_only_string.plot(pin["x"], pin["y"], 'ro', markersize=5)
                ax_only_string.text(pin["x"], pin["y"], str(pin["number"]), fontsize=8, color='black')
            
            for i in range(1, len(string_route)):
                try:
                    prev_pin = next(p for p in pins if p["number"] == string_route[i-1])
                    curr_pin = next(p for p in pins if p["number"] == string_route[i])
                    ax_only_string.plot([prev_pin["x"], curr_pin["x"]], [prev_pin["y"], curr_pin["y"]], 'k-', linewidth=0.7)
                except StopIteration:
                    print(f"경고: 핀 번호 {string_route[i-1]} 또는 {string_route[i]}을(를) 찾을 수 없습니다.")
            
            ax_only_string.set_title("String Art Route Only")
            plt.axis('off')
            plt.show()
            
            # 실 경로만 이미지 저장
            if save_data:
                only_string_png_path = ask_save_path(
                    title="실 경로만 있는 이미지(PNG) 저장 경로 선택",
                    defaultextension=".png",
                    filetypes=[("PNG Files", "*.png")]
                )
                if only_string_png_path:
                    os.makedirs(os.path.dirname(only_string_png_path), exist_ok=True)
                    fig_only_string.savefig(only_string_png_path, bbox_inches='tight', dpi=300)
                    print("String art route only image saved as:", only_string_png_path)
        
        # 엑셀 저장
        if save_data:
            if not excel_output_path:
                excel_output_path = ask_save_path(
                    title="실 경로 데이터(Excel) 저장 경로 선택",
                    defaultextension=".xlsx",
                    filetypes=[("Excel Files", "*.xlsx")]
                )
            if excel_output_path:
                os.makedirs(os.path.dirname(excel_output_path), exist_ok=True)
                
                route_data = []
                for i in range(1, len(string_route)):
                    route_data.append({
                        "단계": i,
                        "시작 핀": string_route[i-1],
                        "도착 핀": string_route[i]
                    })
                route_df = pd.DataFrame(route_data)
                
                with pd.ExcelWriter(excel_output_path) as writer:
                    route_df.to_excel(writer, sheet_name='핀 경로', index=False)
                    
                    pins_df = pd.DataFrame([{
                        "핀 번호": pin["number"],
                        "X 좌표": pin["x"],
                        "Y 좌표": pin["y"]
                    } for pin in pins])
                    pins_df.to_excel(writer, sheet_name='핀 좌표', index=False)
                    
                    grid_df = pd.DataFrame([{
                        "행": block["row"],
                        "열": block["col"],
                        "중심 X": block["center_x"],
                        "중심 Y": block["center_y"],
                        "필요 교차 수": block["value"]
                    } for block in block_info])
                    grid_df.to_excel(writer, sheet_name='그리드 정보', index=False)
                
                print("실 경로 데이터가 저장되었습니다:", excel_output_path)
    
    # 주석 이미지 저장
    if save_data:
        if not png_output_path:
            png_output_path = ask_save_path(
                title="주석 이미지(PNG) 저장 경로 선택",
                defaultextension=".png",
                filetypes=[("PNG Files", "*.png")]
            )
        if png_output_path:
            os.makedirs(os.path.dirname(png_output_path), exist_ok=True)
            fig.savefig(png_output_path, bbox_inches='tight')
            print("Final annotated image saved as:", png_output_path)
    
    plt.show()
    
    # JSON 저장
    if save_data:
        if not json_output_path:
            json_output_path = ask_save_path(
                title="블록 데이터(JSON) 저장 경로 선택",
                defaultextension=".json",
                filetypes=[("JSON Files", "*.json")]
            )
        if json_output_path:
            os.makedirs(os.path.dirname(json_output_path), exist_ok=True)
            data = {
                "block_size": block_size,
                "levels": levels,
                "image_width": w,
                "image_height": h,
                "rows": rows,
                "cols": cols,
                "blocks": block_info,
                "priority_areas": priority_areas,
                "priority_pin_selections": {k: {"pins": list(v["pins"]), "weight": v["weight"]} for k, v in priority_pin_selections.items()}
            }
            with open(json_output_path, "w", encoding="utf-8") as f:
                json.dump(data, f, indent=4, ensure_ascii=False)
            print("Block numeric data saved as:", json_output_path)
    
    # 설정 정보 저장
    if save_data:
        settings_info = {
            "블록 크기": block_size,
            "명암 단계": levels,
            "시간 제한(초)": time_limit,
            "최대 반복 횟수": max_iterations,
            "핀 개수": len(pins) if pins else 0,
            "이미지 크기": f"{image_array.shape[1]}x{image_array.shape[0]}",
            "우선순위 영역 수": len(priority_areas)
        }
        
        if excel_output_path:
            with pd.ExcelWriter(excel_output_path, engine='openpyxl', mode='a' if os.path.exists(excel_output_path) else 'w') as writer:
                settings_df = pd.DataFrame([settings_info])
                settings_df.to_excel(writer, sheet_name='설정 정보', index=False)
        
        if json_output_path:
            data["settings"] = settings_info
            with open(json_output_path, "w", encoding="utf-8") as f:
                json.dump(data, f, indent=4, ensure_ascii=False)
        
        settings_txt_path = os.path.join(os.path.dirname(png_output_path or excel_output_path), "settings.txt")
        with open(settings_txt_path, "w", encoding="utf-8") as f:
            f.write("=== 설정 정보 ===\n")
            for key, value in settings_info.items():
                f.write(f"{key}: {value}\n")
    
    fig.text(0.02, 0.02, f"설정: 블록={block_size}, 명암={levels}, 시간제한={time_limit}초, 최대반복={max_iterations}, 우선순위 영역={len(priority_areas)}", 
             fontsize=8, ha='left')

def calculate_string_route_with_time_limit(pins, grid_cells, block_size, grid_value_map, 
                                         time_limit=300, max_iterations=1000,
                                         priority_areas=None, priority_pin_selections=None):
    """
    우선순위 영역과 가중치를 고려하여 실 경로를 계산합니다.
    """
    start_time = time.time()
    
    image_cells = {pos: value for pos, value in grid_value_map.items() if value > 0}
    total_initial_value = sum(image_cells.values())
    
    print("\n작업 시작...")
    print(f"초기 총 value: {total_initial_value}")
    
    # 핀 쌍별 교차 정보 캐싱
    print("\n핀 쌍별 교차 정보 계산 중...")
    pin_pairs_info = {}
    pin_coords = np.array([[p["x"], p["y"]] for p in pins])
    
    # NumPy 벡터화를 이용한 교차 계산
    pin_indices = list(combinations(range(len(pins)), 2))
    for idx, (i, j) in enumerate(tqdm(pin_indices, desc="핀 쌍 처리")):
        if time.time() - start_time > time_limit * 0.3:
            print("\n전처리 시간 제한 도달")
            break
        
        p1, p2 = pins[i], pins[j]
        crossings = line_crosses_grid_cells(p1, p2, grid_cells, block_size)
        score = sum(image_cells.get(cell_pos, 0) for cell_pos in crossings)
        
        pin_pairs_info[(p1["number"], p2["number"])] = {
            "crossings": crossings,
            "score": score
        }
        pin_pairs_info[(p2["number"], p1["number"])] = {
            "crossings": crossings,
            "score": score
        }
    
    print("\n경로 계산 시작...")
    
    # 우선순위 영역별 핀 목록 준비
    area_pins = []
    if priority_areas and priority_pin_selections:
        for area in sorted(priority_areas, key=lambda x: x["id"]):
            area_id = area["id"]
            if area_id in priority_pin_selections and priority_pin_selections[area_id]["pins"]:
                area_pins.append({
                    "pins": priority_pin_selections[area_id]["pins"],
                    "weight": priority_pin_selections[area_id]["weight"]
                })
        # 나머지 핀 추가
        all_priority_pins = set()
        for area in priority_pin_selections.values():
            all_priority_pins.update(area["pins"])
        remaining_pins = {p["number"] for p in pins if p["number"] not in all_priority_pins}
        if remaining_pins:
            area_pins.append({"pins": remaining_pins, "weight": 1.0})
    else:
        # 우선순위 영역이 없으면 모든 핀 사용
        area_pins.append({"pins": {p["number"] for p in pins}, "weight": 1.0})
    
    string_route = []
    remaining_values = image_cells.copy()
    
    # 각 영역별로 경로 계산
    for area_idx, area in enumerate(tqdm(area_pins, desc="우선순위 영역 처리")):
        current_pins = area["pins"]
        weight = area["weight"]
        
        if not current_pins:
            continue
        
        # 영역 내 최적 시작 쌍 선택
        best_score = -float('inf')
        best_pair = None
        for p1_num in current_pins:
            for p2_num in current_pins:
                if p1_num == p2_num:
                    continue
                pair_key = (p1_num, p2_num)
                if pair_key in pin_pairs_info:
                    score = pin_pairs_info[pair_key]["score"] * weight
                    if score > best_score:
                        best_score = score
                        best_pair = (p1_num, p2_num)
        
        if best_pair is None:
            print(f"\n영역 {area_idx+1}: 유효한 시작 쌍 없음")
            continue
        
        if not string_route:
            string_route.extend(best_pair)
        else:
            string_route.append(best_pair[1])
        
        for cell_pos in pin_pairs_info[best_pair]["crossings"]:
            if cell_pos in remaining_values:
                remaining_values[cell_pos] = max(0, remaining_values[cell_pos] - 1)
        
        # 우선순위 큐를 사용한 경로 탐색
        iteration = 0
        last_update_time = time.time()
        update_interval = 1.0
        
        while iteration < max_iterations:
            current_time = time.time()
            if current_time - start_time > time_limit:
                print(f"\n영역 {area_idx+1}: 시간 제한 도달")
                break
            
            iteration += 1
            current_pin = string_route[-1]
            
            if current_time - last_update_time >= update_interval:
                current_total = sum(remaining_values.values())
                progress = ((total_initial_value - current_total) / total_initial_value) * 100
                if progress > 0:
                    estimated_total_time = (current_time - start_time) / progress * 100
                    remaining_time = estimated_total_time - (current_time - start_time)
                    print(f"\r영역 {area_idx+1} 진행률: {progress:.1f}% | 반복: {iteration}/{max_iterations} | "
                          f"경과: {int(current_time - start_time)}초 | 예상 남은 시간: {int(remaining_time)}초", end="")
                last_update_time = current_time
            
            # 우선순위 큐로 다음 핀 선택
            pq = []
            for next_pin in current_pins:
                if next_pin in string_route[-2:]:
                    continue
                pair_key = (current_pin, next_pin)
                if pair_key in pin_pairs_info:
                    crossings = pin_pairs_info[pair_key]["crossings"]
                    score = sum(remaining_values.get(cell_pos, 0) for cell_pos in crossings) * weight
                    heapq.heappush(pq, (-score, next_pin))
            
            if not pq:
                print(f"\n영역 {area_idx+1}: 더 이상 개선 가능한 경로 없음")
                break
            
            score, best_next_pin = heapq.heappop(pq)
            if -score == 0:
                print(f"\n영역 {area_idx+1}: 추가 점수 0")
                break
            
            string_route.append(best_next_pin)
            
            pair_key = (current_pin, best_next_pin)
            for cell_pos in pin_pairs_info[pair_key]["crossings"]:
                if cell_pos in remaining_values:
                    remaining_values[cell_pos] = max(0, remaining_values[cell_pos] - 1)
            
            if all(value == 0 for value in remaining_values.values()):
                print("\n모든 셀의 value가 0에 도달")
                return string_route
    
    final_total = sum(remaining_values.values())
    final_progress = ((total_initial_value - final_total) / total_initial_value) * 100
    print(f"\n\n작업 완료:")
    print(f"최종 진행률: {final_progress:.1f}%")
    print(f"총 소요 시간: {int(time.time() - start_time)}초")
    print(f"총 반복 횟수: {len(string_route)-1}")
    
    return string_route

def line_crosses_grid_cells(p1, p2, grid_cells, block_size):
    """
    두 핀 사이의 선이 교차하는 그리드 셀을 계산합니다.
    """
    x1, y1 = p1["x"], p1["y"]
    x2, y2 = p2["x"], p2["y"]
    
    distance = math.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2)
    step_size = block_size / 4
    steps = max(1, int(distance / step_size))
    
    crossing_cells = set()
    
    # NumPy 벡터화를 이용한 교차 계산
    t = np.linspace(0, 1, steps + 1)
    x = x1 + t * (x2 - x1)
    y = y1 + t * (y2 - y1)
    
    cell_centers = np.array([[cell["center_x"], cell["center_y"]] for cell in grid_cells])
    half_block = block_size / 2
    
    for i in range(len(x)):
        distances = np.abs(cell_centers - np.array([x[i], y[i]]))
        within_block = (distances[:, 0] <= half_block) & (distances[:, 1] <= half_block)
        for idx in np.where(within_block)[0]:
            crossing_cells.add((cell_centers[idx, 0], cell_centers[idx, 1]))
    
    return crossing_cells

def main():
    """
    메인 함수: 사용자로부터 이미지를 선택받고 처리합니다.
    """
    print("프로그램 시작...")
    
    root = tk.Tk()
    root.withdraw()
    file_path = filedialog.askopenfilename(title="이미지 파일 선택",
                                          filetypes=[("이미지 파일", "*.jpg;*.jpeg;*.png;*.bmp")])
    if not file_path:
        print("파일이 선택되지 않았습니다.")
        return
    
    block_size = ask_block_size() or 10
    levels = ask_levels() or 50
    canvas_type = ask_canvas_type() or "동그라미"
    image = cv2.imread(file_path)
    if image is None:
        print("이미지를 읽을 수 없습니다.")
        return
    
    image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    margin = ask_margin() or 0.2
    canvas_size = ask_canvas_size(canvas_type, image, margin) or (10, 10) if canvas_type == "사각형" else 10
    scale = ask_image_scale() or 1.0
    time_limit = ask_time_limit() or 300
    max_iterations = ask_max_iterations() or 1000
    
    # 시각화 옵션
    show_progress = messagebox.askyesno("시각화", "처리 과정을 시각화하시겠습니까?")
    
    image = resize_image(image, scale)
    canvas = create_canvas(canvas_type, canvas_size)
    if canvas is None:
        print("캔버스를 생성할 수 없습니다.")
        return
    
    canvas_with_pins, pins = draw_numbered_points(canvas, canvas_type, canvas_size, margin)
    
    # 인터랙티브 핀 추가 및 우선순위 영역 지정
    use_priority_areas = ask_use_priority_areas()
    if use_priority_areas:
        pins, priority_areas, priority_pin_selections = interactive_pin_and_priority_selection(
            canvas_with_pins.copy(), pins, canvas_type, canvas_size, margin
        )
    else:
        priority_areas = []
        priority_pin_selections = {}
    
    canvas_with_image = place_image_on_canvas(canvas_with_pins.copy(), image, canvas_type)
    
    save_data = messagebox.askyesno("데이터 저장", "처리 결과를 파일로 저장하시겠습니까?")
    process_image(
        canvas_with_image, block_size, levels, save_data, pins=pins, 
        time_limit=time_limit, max_iterations=max_iterations,
        priority_areas=priority_areas, priority_pin_selections=priority_pin_selections,
        show_progress=show_progress
    )

if __name__ == "__main__":
    main()