import cv2
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.widgets import LassoSelector
from matplotlib.path import Path
import tkinter as tk
from tkinter import filedialog, simpledialog, messagebox, Scale
from PIL import Image, ImageTk
import math
import json
import os
import pandas as pd
from scipy.spatial.distance import cdist
import heapq
from itertools import combinations
import time
from tqdm import tqdm
import uuid

# 전역 변수 정의
selected_regions = []
last_pin_number = 0  # 마지막 핀 번호 추적

def normalize_grid_by_lightest_area(grid_values):
    max_val = np.max(grid_values)
    if max_val == 0:
        return grid_values
    return max_val - grid_values

def show_grid_visualization(grid_values, block_size, block_info, image_shape, title="Grid Cell Values"):
    try:
        h, w = image_shape[:2]
        rows, cols = grid_values.shape
        if rows == 0 or cols == 0:
            print("Warning: Grid values are empty. Skipping visualization.")
            return
        
        fig, ax = plt.subplots(figsize=(8, 8))
        # Create a pixel-aligned grid for visualization
        display_grid = np.zeros((h, w))
        for cell in block_info:
            row, col = cell["row"], cell["col"]
            value = cell["value"]
            # Fill the block area with the cell value
            r_start = row * block_size
            r_end = min((row + 1) * block_size, h)
            c_start = col * block_size
            c_end = min((col + 1) * block_size, w)
            display_grid[r_start:r_end, c_start:c_end] = value
        
        # Display the grid with proper extent to align with image pixels
        im = ax.imshow(display_grid, cmap='viridis', extent=(0, w, h, 0))
        plt.colorbar(im, ax=ax, label='Cell Value')
        
        # Add text annotations for cell values
        for cell in block_info:
            x_center = cell["center_x"]
            y_center = cell["center_y"]
            value = cell["value"]
            ax.text(x_center, y_center, str(value), color='red', fontsize=8, ha='center', va='center')
        
        # Add grid lines
        for i in range(0, h, block_size):
            ax.axhline(i, color='yellow', linestyle='--', linewidth=0.5)
        for j in range(0, w, block_size):
            ax.axvline(j, color='yellow', linestyle='--', linewidth=0.5)
        
        plt.title(title)
        ax.set_xlim(0, w)
        ax.set_ylim(h, 0)
        plt.axis('on')  # Keep axis for clarity
        plt.show()
    except Exception as e:
        print(f"Error in show_grid_visualization: {e}")

def confirm_proceed():
    root = tk.Tk()
    root.withdraw()
    try:
        return messagebox.askyesno("확인", "진행하시겠습니까?")
    finally:
        root.destroy()

def ask_negative_weight():
    root = tk.Tk()
    root.title("제외 구역 음의 가중치 설정")
    root.geometry("300x150")

    negative_weight = tk.DoubleVar(value=50.0)
    
    tk.Label(root, text="제외 구역 음의 가중치 (0~100):").pack(pady=10)
    slider = Scale(root, from_=0, to=100, orient=tk.HORIZONTAL, variable=negative_weight, length=200)
    slider.pack(pady=10)
    
    def on_confirm():
        root.quit()
    
    tk.Button(root, text="확인", command=on_confirm).pack(pady=10)
    
    root.update_idletasks()
    width = root.winfo_width()
    height = root.winfo_height()
    x = (root.winfo_screenwidth() // 2) - (width // 2)
    y = (root.winfo_screenheight() // 2) - (height // 2)
    root.geometry(f'{width}x{height}+{x}+{y}')
    
    try:
        root.mainloop()
        weight = negative_weight.get() / 100.0  # 0.0 to 1.0
    finally:
        root.destroy()
    return weight

# ------------------------------ 사용자 제외 영역 설정 ------------------------------
class FreeDrawExclusion:
    def __init__(self, image, grid_shape, block_size, block_info):
        self.image = image
        self.grid_shape = grid_shape
        self.block_size = block_size
        self.block_info = block_info
        self.brush_size = 10
        self.done = False
        self.undo_stack = []
        self.fig, self.ax = plt.subplots(figsize=(10, 10))
        self.exclusion_mask = np.zeros((grid_shape[0], grid_shape[1]), dtype=np.uint8)
        self.display_image = self.image.copy()
        self.update_display()
        self.fig.canvas.mpl_connect("motion_notify_event", self.on_mouse_move)
        self.fig.canvas.mpl_connect("button_press_event", self.on_click)
        self.fig.canvas.mpl_connect("key_press_event", self.on_key)

    def update_display(self):
        self.display_image = self.image.copy()
        mask_colored = np.zeros_like(self.image)
        mask_colored[:, :, 2] = self.exclusion_mask * 255  # Red channel
        self.display_image = cv2.addWeighted(self.display_image, 1.0, mask_colored, 0.5, 0)
        # 그리드와 셀 값 추가
        for cell in self.block_info:
            x_center = cell["center_x"]
            y_center = cell["center_y"]
            value = cell["value"]
            cv2.putText(self.display_image, str(value), (x_center - 10, y_center), 
                        cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 1)
        for i in range(0, self.grid_shape[0], self.block_size):
            cv2.line(self.display_image, (0, i), (self.grid_shape[1], i), (0, 255, 255), 1)
        for j in range(0, self.grid_shape[1], self.block_size):
            cv2.line(self.display_image, (j, 0), (j, self.grid_shape[0]), (0, 255, 255), 1)
        self.ax.clear()
        self.ax.imshow(cv2.cvtColor(self.display_image, cv2.COLOR_BGR2RGB))
        self.ax.set_title("제외 영역을 그리세요 (Enter: 완료, Ctrl+Z: 실행 취소)")
        self.ax.axis('off')
        self.fig.canvas.draw()

    def on_mouse_move(self, event):
        if event.button == 1 and event.inaxes:
            self.draw_at(event.xdata, event.ydata)

    def on_click(self, event):
        if event.button == 1 and event.inaxes:
            self.draw_at(event.xdata, event.ydata)

    def draw_at(self, x, y):
        if x is None or y is None:
            return
        ix, iy = int(x), int(y)
        # 이전 상태 저장 (실행 취소용)
        self.undo_stack.append(self.exclusion_mask.copy())
        if len(self.undo_stack) > 10:  # 최대 10개까지 저장
            self.undo_stack.pop(0)
        cv2.circle(self.exclusion_mask, (ix, iy), self.brush_size, 255, -1)
        self.update_display()

    def on_key(self, event):
        if event.key == 'enter':
            self.done = True
            plt.close()
        elif event.key == 'ctrl+z' and self.undo_stack:
            self.exclusion_mask = self.undo_stack.pop()
            self.update_display()

    def run(self):
        plt.show()
        return self.exclusion_mask.astype(bool)

def select_priority_regions(canvas_with_image, pins):
    global selected_regions, last_pin_number
    selected_regions = []
    
    def onselect_lasso(vertices, pins, ax, fig, canvas_with_image):
        path = Path(vertices)
        region_pins = []
        
        for pin in pins:
            if path.contains_point((pin["x"], pin["y"])):
                region_pins.append(pin["number"])
        
        selected_regions.append((vertices, region_pins))
        print(f"[영역 선택 완료] 우선순위 {len(selected_regions)}: 선택된 핀: {region_pins}")
        
        ax.plot([v[0] for v in vertices] + [vertices[0][0]], 
                [v[1] for v in vertices] + [vertices[0][1]], 
                'r-', linewidth=2, alpha=0.3)
        for pn in region_pins:
            p = next(p for p in pins if p["number"] == pn)
            ax.plot(p["x"], p["y"], 'go', markersize=10, alpha=0.5)
        
        print("\n=== 핀 선택 모드 ===")
        print("🖱️ 좌클릭으로 핀 추가/영역 내 핀 선택, 우클릭으로 핀 선택 해제. (다음 영역 선택은 드래그, 종료는 창 닫기)")
        plt.title("핀 선택: 좌클릭으로 추가/선택, 우클릭으로 해제")
        fig.canvas.draw()

    def onclick(event, pins, ax, fig):
        if event.xdata is None or event.ydata is None:
            return
        x, y = int(event.xdata), int(event.ydata)
        
        # 근처 핀 찾기
        for pin in pins:
            dist = math.sqrt((pin["x"] - x) ** 2 + (pin["y"] - y) ** 2)
            if dist < 10:
                for region_idx, (vertices, region_pins) in enumerate(selected_regions):
                    path = Path(vertices)
                    if path.contains_point((pin["x"], pin["y"])):
                        if event.button == 1:  # 좌클릭: 핀 추가
                            if pin["number"] not in region_pins:
                                region_pins.append(pin["number"])
                                selected_regions[region_idx] = (vertices, region_pins)
                                print(f"[핀 추가] 우선순위 {region_idx + 1}에 핀 {pin['number']} 추가")
                                ax.plot(pin["x"], pin["y"], 'go', markersize=10, alpha=0.5)
                                fig.canvas.draw()
                        elif event.button == 3:  # 우클릭: 핀 제거
                            if pin["number"] in region_pins:
                                region_pins.remove(pin["number"])
                                selected_regions[region_idx] = (vertices, region_pins)
                                print(f"[핀 제거] 우선순위 {region_idx + 1}에서 핀 {pin['number']} 제거")
                                redraw_canvas(ax, fig, canvas_with_image, pins)
                return
        
        # 핀 추가 (좌클릭, 근처에 핀이 없음)
        if event.button == 1:
            global last_pin_number
            last_pin_number += 1
            new_pin = {"number": last_pin_number, "x": x, "y": y}
            pins.append(new_pin)
            ax.plot(x, y, 'ro', markersize=5)
            ax.text(x, y, str(last_pin_number), fontsize=8, color='white')
            print(f"[새 핀 추가] 핀 {last_pin_number} at ({x}, {y})")
            
            # 새 핀이 기존 우선순위 영역 내에 있는지 확인
            for region_idx, (vertices, region_pins) in enumerate(selected_regions):
                path = Path(vertices)
                if path.contains_point((x, y)):
                    region_pins.append(last_pin_number)
                    selected_regions[region_idx] = (vertices, region_pins)
                    print(f"[핀 추가] 우선순위 {region_idx + 1}에 새 핀 {last_pin_number} 자동 추가")
                    ax.plot(x, y, 'go', markersize=10, alpha=0.5)
            
            fig.canvas.draw()

    def redraw_canvas(ax, fig, canvas_with_image, pins):
        ax.clear()
        ax.imshow(canvas_with_image)
        for pin in pins:
            ax.plot(pin["x"], pin["y"], 'ro', markersize=5)
            ax.text(pin["x"], pin["y"], str(pin["number"]), fontsize=8, color='white')
        for vertices, r_pins in selected_regions:
            ax.plot([v[0] for v in vertices] + [vertices[0][0]], 
                    [v[1] for v in vertices] + [vertices[0][1]], 
                    'r-', linewidth=2, alpha=0.3)
            for pn in r_pins:
                p = next(p for p in pins if p["number"] == pn)
                ax.plot(p["x"], p["y"], 'go', markersize=10, alpha=0.5)
        fig.canvas.draw()

    print("\n=== 우선순위 영역 선택 모드 ===")
    print("🖱️ 마우스를 드래그하여 영역 그리기, 좌클릭으로 핀 추가, 우클릭으로 핀 선택 해제. (종료하려면 창을 닫으세요)")
    fig, ax = plt.subplots(figsize=(10, 10))
    ax.imshow(canvas_with_image)
    
    for pin in pins:
        ax.plot(pin["x"], pin["y"], 'ro', markersize=5)
        ax.text(pin["x"], pin["y"], str(pin["number"]), fontsize=8, color='white')
    
    lasso = LassoSelector(ax, lambda verts: onselect_lasso(verts, pins, ax, fig, canvas_with_image),
                         props=dict(color='red', linewidth=2, alpha=0.2))
    
    fig.canvas.mpl_connect('button_press_event', lambda event: onclick(event, pins, ax, fig))
    
    plt.title("우선순위 영역 선택: 마우스로 영역 그리기 또는 핀 추가/선택")
    plt.show()
    
    priority_areas = [{"id": idx + 1, "points": vertices} for idx, (vertices, _) in enumerate(selected_regions)]
    priority_pin_selections = {idx + 1: {"pins": set(pins), "weight": 3.0} for idx, (_, pins) in enumerate(selected_regions)}
    
    return pins, priority_areas, priority_pin_selections

def assign_priority_to_cells(grid_cells, priority_areas):
    try:
        paths = [Path(area["points"]) for area in priority_areas]
        for cell in grid_cells:
            cx, cy = cell["center_x"], cell["center_y"]
            assigned = False
            for idx, path in enumerate(paths):
                if path.contains_point((cx, cy)):
                    cell["priority"] = idx
                    assigned = True
                    break
            if not assigned:
                cell["priority"] = len(paths)
        return grid_cells
    except Exception as e:
        print(f"assign_priority_to_cells 실행 중 오류 발생: {e}")
        return grid_cells

def estimate_required_iterations(grid_cells):
    total_required_crossings = sum(cell["value"] for cell in grid_cells)
    average_crossings_per_line = 3
    estimated_iterations = int(total_required_crossings / average_crossings_per_line)
    return estimated_iterations

def ask_save_path(title, defaultextension, filetypes):
    root = tk.Tk()
    root.withdraw()
    try:
        path = filedialog.asksaveasfilename(title=title, defaultextension=defaultextension, filetypes=filetypes)
    finally:
        root.destroy()
    return path

def ask_block_size():
    root = tk.Tk()
    root.withdraw()
    try:
        block_size = simpledialog.askinteger("블록 크기 입력", "블록 크기를 입력하세요 (기본값: 10):", minvalue=1, initialvalue=10)
    finally:
        root.destroy()
    return block_size

def ask_levels():
    root = tk.Tk()
    root.withdraw()
    try:
        levels = simpledialog.askinteger("Levels 입력", "Levels 값을 입력하세요 (기본값: 50):", minvalue=1, initialvalue=50)
    finally:
        root.destroy()
    return levels

def ask_canvas_type():
    root = tk.Tk()
    root.title("캔버스 타입 선택")
    root.geometry("300x200")
    
    canvas_type = tk.StringVar(value="동그라미")
    
    tk.Radiobutton(root, text="사각형", variable=canvas_type, value="사각형").pack(pady=10)
    tk.Radiobutton(root, text="정삼각형", variable=canvas_type, value="정삼각형").pack(pady=10)
    tk.Radiobutton(root, text="동그라미", variable=canvas_type, value="동그라미").pack(pady=10)
    
    def on_confirm():
        root.quit()
    
    tk.Button(root, text="확인", command=on_confirm).pack(pady=20)
    
    root.update_idletasks()
    width = root.winfo_width()
    height = root.winfo_height()
    x = (root.winfo_screenwidth() // 2) - (width // 2)
    y = (root.winfo_screenheight() // 2) - (height // 2)
    root.geometry(f'{width}x{height}+{x}+{y}')
    
    try:
        root.mainloop()
        selected_type = canvas_type.get()
    finally:
        root.destroy()
    return selected_type

def calculate_optimal_canvas_size(image, canvas_type, margin, dpi=300):
    cm_to_pixel = lambda x: int(x * dpi / 2.54)
    h_image, w_image = image.shape[:2]
    margin_px = cm_to_pixel(margin)
    margin_factor = 1.1
    
    if canvas_type == "사각형":
        width_cm = (w_image + 2 * margin_px) * 2.54 / dpi * margin_factor
        height_cm = (h_image + 2 * margin_px) * 2.54 / dpi * margin_factor
        return (width_cm, height_cm)
    elif canvas_type == "정삼각형":
        max_dimension = max(w_image, h_image)
        side_cm = (max_dimension + 2 * margin_px) * 2.54 / dpi * margin_factor
        return side_cm
    elif canvas_type == "동그라미":
        max_dimension = max(w_image, h_image)
        radius_cm = (max_dimension/2 + margin_px) * 2.54 / dpi * margin_factor
        return radius_cm
    return None

def ask_canvas_size(canvas_type, image=None, margin=None):
    root = tk.Tk()
    root.withdraw()
    try:
        if image is not None and margin is not None:
            optimal_size = calculate_optimal_canvas_size(image, canvas_type, margin)
            message = f"최적의 캔버스 크기가 계산되었습니다:\n"
            if canvas_type == "사각형":
                message += f"가로: {optimal_size[0]:.1f}cm, 세로: {optimal_size[1]:.1f}cm\n"
            else:
                message += f"크기: {optimal_size:.1f}cm\n"
            message += "\n이 크기를 사용하시겠습니까?\n아니오를 선택하면 수동으로 크기를 설정할 수 있습니다."
            
            choice = messagebox.askyesno("캔버스 크기 설정", message)
            if choice:
                return optimal_size
        
        if canvas_type == "사각형":
            width = simpledialog.askfloat("사각형 가로 길이 입력", "가로 길이(cm)를 입력하세요 (기본값: 10):", minvalue=1, initialvalue=10)
            height = simpledialog.askfloat("사각형 세로 길이 입력", "세로 길이(cm)를 입력하세요 (기본값: 10):", minvalue=1, initialvalue=10)
            size = (width, height)
        elif canvas_type == "정삼각형":
            side = simpledialog.askfloat("정삼각형 한 변의 길이 입력", "한 변의 길이(cm)를 입력하세요 (기본값: 10):", minvalue=1, initialvalue=10)
            size = side
        elif canvas_type == "동그라미":
            radius = simpledialog.askfloat("동그라미 반지름 입력", "반지름(cm)를 입력하세요 (기본값: 10):", minvalue=1, initialvalue=10)
            size = radius
        else:
            size = None
    finally:
        root.destroy()
    return size

def ask_image_scale():
    root = tk.Tk()
    root.withdraw()
    try:
        scale = simpledialog.askfloat("이미지 크기 조정", "이미지 크기 조정 비율(%)을 입력하세요 (기본값: 100):", minvalue=1, initialvalue=100)
    finally:
        root.destroy()
    return scale / 100 if scale else 1.0

def ask_margin():
    root = tk.Tk()
    root.withdraw()
    try:
        margin = simpledialog.askfloat("가장자리 점 간격 입력", "가장자리 점 간격(cm)을 입력하세요 (기본값: 0.2):", minvalue=0.1, initialvalue=0.2)
    finally:
        root.destroy()
    return margin if margin else 0.2

def ask_time_limit():
    root = tk.Tk()
    root.withdraw()
    try:
        time_limit = simpledialog.askinteger("최대 실행 시간 입력", "최대 실행 시간(초)을 입력하세요 (기본값: 300):", minvalue=1, initialvalue=300)
    finally:
        root.destroy()
    return time_limit if time_limit else 300

def ask_max_iterations():
    root = tk.Tk()
    root.withdraw()
    try:
        max_iterations = simpledialog.askinteger("최대 반복 횟수 입력", "최대 반복 횟수를 입력하세요 (기본값: 1000):", minvalue=1, initialvalue=1000)
    finally:
        root.destroy()
    return max_iterations if max_iterations else 1000

def ask_use_priority_areas():
    root = tk.Tk()
    root.withdraw()
    try:
        use_priority = messagebox.askyesno("우선순위 영역", "우선순위 영역을 지정하시겠습니까?")
    finally:
        root.destroy()
    return use_priority

def ask_exclude_areas():
    root = tk.Tk()
    root.withdraw()
    try:
        exclude_areas = messagebox.askyesno("제외 영역", "제외 영역을 지정하시겠습니까?")
    finally:
        root.destroy()
    return exclude_areas

def create_canvas(canvas_type, size, dpi=300):
    cm_to_pixel = lambda x: int(x * dpi / 2.54)
    if canvas_type == "사각형":
        width, height = size
        canvas = np.ones((cm_to_pixel(height), cm_to_pixel(width), 3), dtype=np.uint8) * 255
    elif canvas_type == "정삼각형":
        side = size
        height = side * math.sqrt(3) / 2
        canvas = np.ones((cm_to_pixel(height), cm_to_pixel(side), 3), dtype=np.uint8) * 255
    elif canvas_type == "동그라미":
        radius = size
        diameter = cm_to_pixel(radius * 2)
        canvas = np.ones((diameter, diameter, 3), dtype=np.uint8) * 255
        cv2.circle(canvas, (diameter // 2, diameter // 2), cm_to_pixel(radius), (0, 0, 0), 2)
    else:
        canvas = None
    return canvas

def resize_image(image, scale):
    width = int(image.shape[1] * scale)
    height = int(image.shape[0] * scale)
    return cv2.resize(image, (width, height))

def place_image_on_canvas(canvas, image, canvas_type):
    h_canvas, w_canvas = canvas.shape[:2]
    h_image, w_image = image.shape[:2]
    
    if h_image > h_canvas or w_image > w_canvas:
        message = f"이미지 크기({h_image}x{w_image})가 캔버스 크기({h_canvas}x{w_canvas})보다 큽니다.\n"
        message += "다음 중 하나를 선택하세요:\n1. 이미지 크기 조정 비율을 더 작게 설정\n2. 캔버스 크기를 더 크게 설정\n3. 프로그램 종료"
        
        root = tk.Tk()
        root.withdraw()
        try:
            choice = messagebox.askquestion("크기 불일치", message + "\n\n다시 설정하시겠습니까?")
            if choice == 'yes':
                scale = ask_image_scale()
                if scale:
                    image = resize_image(image, scale)
                    return place_image_on_canvas(canvas, image, canvas_type)
                else:
                    canvas_size = ask_canvas_size(canvas_type)
                    if canvas_size:
                        canvas = create_canvas(canvas_type, canvas_size)
                        return place_image_on_canvas(canvas, image, canvas_type)
            raise ValueError("사용자가 작업을 취소했습니다.")
        finally:
            root.destroy()
    
    x_offset = (w_canvas - w_image) // 2
    y_offset = (h_canvas - h_image) // 2
    canvas[y_offset:y_offset+h_image, x_offset:x_offset+w_image] = image
    return canvas

def draw_numbered_points(canvas, canvas_type, size, margin, dpi=300):
    cm_to_pixel = lambda x: int(x * dpi / 2.54)
    margin_px = cm_to_pixel(margin)
    font = cv2.FONT_HERSHEY_SIMPLEX
    font_scale = 0.5
    font_color = (0, 0, 0)
    thickness = 1
    pins = []
    global last_pin_number
    last_pin_number = 0

    if canvas_type == "사각형":
        width, height = size
        width_px, height_px = cm_to_pixel(width), cm_to_pixel(height)
        for i in range(0, width_px, margin_px):
            if i < width_px:
                last_pin_number += 1
                cv2.circle(canvas, (i, margin_px), 2, (0, 0, 255), -1)
                cv2.putText(canvas, str(last_pin_number), (i, margin_px + 15), font, font_scale, font_color, thickness)
                pins.append({"number": last_pin_number, "x": i, "y": margin_px})
        for i in range(margin_px, height_px, margin_px):
            if i < height_px:
                last_pin_number += 1
                cv2.circle(canvas, (width_px - margin_px, i), 2, (0, 0, 255), -1)
                cv2.putText(canvas, str(last_pin_number), (width_px - margin_px + 15, i), font, font_scale, font_color, thickness)
                pins.append({"number": last_pin_number, "x": width_px - margin_px, "y": i})
        for i in range(width_px - margin_px, -1, -margin_px):
            if i >= 0:
                last_pin_number += 1
                cv2.circle(canvas, (i, height_px - margin_px), 2, (0, 0, 255), -1)
                cv2.putText(canvas, str(last_pin_number), (i, height_px - margin_px + 15), font, font_scale, font_color, thickness)
                pins.append({"number": last_pin_number, "x": i, "y": height_px - margin_px})
        for i in range(height_px - margin_px, margin_px, -margin_px):
            if i >= 0:
                last_pin_number += 1
                cv2.circle(canvas, (margin_px, i), 2, (0, 0, 255), -1)
                cv2.putText(canvas, str(last_pin_number), (margin_px + 15, i), font, font_scale, font_color, thickness)
                pins.append({"number": last_pin_number, "x": margin_px, "y": i})
    elif canvas_type == "정삼각형":
        side = size
        side_px = cm_to_pixel(side)
        height_px = int(side_px * math.sqrt(3) / 2)
        for i in range(0, side_px, margin_px):
            if i < side_px:
                last_pin_number += 1
                cv2.circle(canvas, (i, height_px), 2, (0, 0, 255), -1)
                cv2.putText(canvas, str(last_pin_number), (i, height_px + 15), font, font_scale, font_color, thickness)
                pins.append({"number": last_pin_number, "x": i, "y": height_px})
        right_count = int((side_px / 2) / margin_px)
        for i in range(right_count):
            x = side_px - i * margin_px * math.cos(math.radians(30))
            y = height_px - i * margin_px * math.sin(math.radians(30))
            if 0 <= x < canvas.shape[1] and 0 <= y < canvas.shape[0]:
                last_pin_number += 1
                cv2.circle(canvas, (int(x), int(y)), 2, (0, 0, 255), -1)
                cv2.putText(canvas, str(last_pin_number), (int(x) + 15, int(y)), font, font_scale, font_color, thickness)
                pins.append({"number": last_pin_number, "x": int(x), "y": int(y)})
        left_count = int((side_px / 2) / margin_px)
        for i in range(left_count):
            x = i * margin_px * math.cos(math.radians(30))
            y = height_px - i * margin_px * math.sin(math.radians(30))
            if 0 <= x < canvas.shape[1] and 0 <= y < canvas.shape[0]:
                last_pin_number += 1
                cv2.circle(canvas, (int(x), int(y)), 2, (0, 0, 255), -1)
                cv2.putText(canvas, str(last_pin_number), (int(x) - 15, int(y)), font, font_scale, font_color, thickness)
                pins.append({"number": last_pin_number, "x": int(x), "y": int(y)})
    elif canvas_type == "동그라미":
        radius = size
        radius_px = cm_to_pixel(radius)
        center = (radius_px, radius_px)
        diameter = radius_px * 2
        
        min_pins = 8
        circumference = 2 * math.pi * radius_px
        pins_count = max(min_pins, int(circumference / margin_px))
        angle_step = 360 / pins_count
        
        for angle in range(0, 360, int(angle_step)):
            last_pin_number += 1
            x = int(center[0] + radius_px * math.cos(math.radians(angle)))
            y = int(center[1] + radius_px * math.sin(math.radians(angle)))
            if 0 <= x < canvas.shape[1] and 0 <= y < canvas.shape[0]:
                cv2.circle(canvas, (x, y), 2, (0, 0, 255), -1)
                cv2.putText(canvas, str(last_pin_number), (x + 1, y + 1), font, font_scale, font_color, thickness)
                pins.append({"number": last_pin_number, "x": x, "y": y})
    
    return canvas, pins

def visualize_string_route(gray, pins, string_route, title, linewidth=0.5, base_alpha=0.5, save_path=None):
    root = tk.Tk()
    root.title("실 경로 시각화 설정")
    root.geometry("400x300")

    linewidth_var = tk.DoubleVar(value=linewidth)
    alpha_var = tk.DoubleVar(value=base_alpha)

    tk.Label(root, text="선 굵기 (0.1~2.0):").pack(pady=5)
    linewidth_slider = Scale(root, from_=0.1, to=2.0, resolution=0.1, orient=tk.HORIZONTAL, variable=linewidth_var, length=200)
    linewidth_slider.pack(pady=5)

    tk.Label(root, text="기본 투명도 (0.1~1.0):").pack(pady=5)
    alpha_slider = Scale(root, from_=0.1, to=1.0, resolution=0.1, orient=tk.HORIZONTAL, variable=alpha_var, length=200)
    alpha_slider.pack(pady=5)

    def update_visualization(save=False):
        fig, ax = plt.subplots(figsize=(10, 10))
        ax.imshow(gray, cmap='gray')
        
        for pin in pins:
            ax.plot(pin["x"], pin["y"], 'ro', markersize=5)
            ax.text(pin["x"], pin["y"], str(pin["number"]), fontsize=8, color='white')
        
        # 선의 겹침을 표현하기 위해 각 선의 횟수를 계산
        line_counts = {}
        for i in range(1, len(string_route)):
            try:
                prev_pin = next(p for p in pins if p["number"] == string_route[i-1])
                curr_pin = next(p for p in pins if p["number"] == string_route[i])
                line_key = tuple(sorted([(prev_pin["x"], prev_pin["y"]), (curr_pin["x"], curr_pin["y"])]))
                line_counts[line_key] = line_counts.get(line_key, 0) + 1
            except StopIteration:
                print(f"경고: 핀 번호 {string_route[i-1]} 또는 {string_route[i]}을(를) 찾을 수 없습니다.")
        
        for line_key, count in line_counts.items():
            (x1, y1), (x2, y2) = line_key
            # 겹침에 따라 투명도 증가 (최대 1.0)
            alpha = min(1.0, alpha_var.get() * (1 + 0.1 * (count - 1)))
            ax.plot([x1, x2], [y1, y2], 'b-', linewidth=linewidth_var.get(), alpha=alpha)
        
        ax.set_title(title)
        plt.axis('off')
        
        if save and save_path:
            os.makedirs(os.path.dirname(save_path), exist_ok=True)
            plt.savefig(save_path, bbox_inches='tight', dpi=300)
            print(f"String art image saved as: {save_path}")
        
        plt.show()

    def on_update():
        plt.close('all')
        update_visualization()

    def on_save():
        save_path = ask_save_path(
            title="실 경로 이미지(PNG) 저장 경로 선택",
            defaultextension=".png",
            filetypes=[("PNG Files", "*.png")]
        )
        if save_path:
            plt.close('all')
            update_visualization(save=True)

    tk.Button(root, text="업데이트", command=on_update).pack(pady=10)
    tk.Button(root, text="저장", command=on_save).pack(pady=10)
    tk.Button(root, text="닫기", command=root.quit).pack(pady=10)

    root.update_idletasks()
    width = root.winfo_width()
    height = root.winfo_height()
    x = (root.winfo_screenwidth() // 2) - (width // 2)
    y = (root.winfo_screenheight() // 2) - (height // 2)
    root.geometry(f'{width}x{height}+{x}+{y}')

    try:
        update_visualization()
        root.mainloop()
    finally:
        root.destroy()
        plt.close('all')

def process_image(image_array, block_size=10, levels=50, save_data=False, 
                 json_output_path=None, png_output_path=None, excel_output_path=None, 
                 pins=None, time_limit=300, max_iterations=1000, priority_areas=None, 
                 priority_pin_selections=None, show_progress=True, exclusion_mask=None, negative_weight=0.5):
    print("이미지 처리 시작...")
    
    if show_progress:
        fig, ax = plt.subplots(figsize=(8, 8))
        ax.imshow(image_array)
        ax.set_title("Original Image")
        plt.axis('off')
        plt.show()
    
    if len(image_array.shape) == 3:
        gray = cv2.cvtColor(image_array, cv2.COLOR_RGB2GRAY)
    else:
        gray = image_array.copy()
    if show_progress:
        fig, ax = plt.subplots(figsize=(8, 8))
        ax.imshow(gray, cmap='gray')
        ax.set_title("Grayscale Image")
        plt.axis('off')
        plt.show()
    
    h, w = gray.shape
    if show_progress:
        fig, ax = plt.subplots(figsize=(8, 8))
        ax.imshow(gray, cmap='gray')
        for i in range(0, h, block_size):
            if i < h:
                ax.axhline(i, color='yellow', linestyle='--', linewidth=0.5)
        for j in range(0, w, block_size):
            if j < w:
                ax.axvline(j, color='yellow', linestyle='--', linewidth=0.5)
        ax.set_title("Grayscale Image with Grid Overlay")
        plt.axis('off')
        plt.show()
    
    print("그리드 셀 value 계산 중...")
    rows = math.ceil(h / block_size)
    cols = math.ceil(w / block_size)
    processed_image = np.zeros((rows, cols), dtype=int)
    
    img_center_x = w // 2
    img_center_y = h // 2
    img_width = image_array.shape[1]
    img_height = image_array.shape[0]
    img_start_x = img_center_x - img_width // 2
    img_start_y = img_center_y - img_height // 2
    
    block_info = []
    for i in tqdm(range(0, h, block_size), desc="그리드 셀 처리"):
        for j in range(0, w, block_size):
            if (img_start_x <= j < img_start_x + img_width and 
                img_start_y <= i < img_start_y + img_height):
                img_x = j - img_start_x
                img_y = i - img_start_y
                block = gray[img_y:min(img_y+block_size, img_height), 
                           img_x:min(img_x+block_size, img_width)]
                if block.size > 0:
                    avg_gray = np.mean(block)
                    level = int((255 - avg_gray) / 255 * (levels - 1))
                    processed_image[i // block_size, j // block_size] = level
                    x_center = j + block_size // 2
                    y_center = i + block_size // 2
                    block_info.append({
                        "row": i // block_size,
                        "col": j // block_size,
                        "center_x": int(x_center),
                        "center_y": int(y_center),
                        "value": level
                    })
    
    # 가장 밝은 부분을 0으로 정규화
    processed_image = normalize_grid_by_lightest_area(processed_image)
    for cell in block_info:
        cell["value"] = int(processed_image[cell["row"], cell["col"]])
    
    # 그리드 셀 value 시각화
    show_grid_visualization(processed_image, block_size, block_info, image_array.shape, title="Normalized Grid Cell Values")
    
    # 사용자에게 진행 여부 확인
    if not confirm_proceed():
        print("사용자가 진행을 취소했습니다. 옵션 설정으로 돌아갑니다.")
        return False, None, None, None, block_info
    
    print("주석 이미지 생성 중...")
    fig, ax = plt.subplots(figsize=(8, 8))
    ax.imshow(gray, cmap='gray')
    for cell in block_info:
        x_center = cell["center_x"]
        y_center = cell["center_y"]
        value = cell["value"]
        ax.text(x_center, y_center, str(value),
                color='red', fontsize=8, ha='center', va='center')
    for i in range(0, h, block_size):
        if i < h:
            ax.axhline(i, color='yellow', linestyle='--', linewidth=0.5)
    for j in range(0, w, block_size):
        if j < w:
            ax.axvline(j, color='yellow', linestyle='--', linewidth=0.5)
    ax.set_title("Final Annotated Image")
    plt.axis('off')
    
    grid_value_map = {(cell["center_x"], cell["center_y"]): cell["value"] for cell in block_info}
    
    if priority_areas:
        block_info = assign_priority_to_cells(block_info, priority_areas)
    
    # 제외 영역에 음의 가중치 적용
    if exclusion_mask is not None:
        for cell in block_info:
            cx, cy = cell["center_x"], cell["center_y"]
            if cx < exclusion_mask.shape[1] and cy < exclusion_mask.shape[0]:
                if exclusion_mask[cy, cx]:
                    cell["value"] = int(cell["value"] * (1 - negative_weight))
    
    # Initialize string_route to ensure it's always defined
    string_route = []
    
    if pins:
        print("실 경로 계산 시작...")
        grid_cells = [block for block in block_info if block["value"] > 0]
        string_route = calculate_string_route_with_time_limit(
            pins, grid_cells, block_size, grid_value_map, time_limit, max_iterations,
            priority_areas, priority_pin_selections, exclusion_mask, negative_weight
        )
        
        estimated_iters = estimate_required_iterations(grid_cells)
        print(f"🔍 예상 반복 횟수: 약 {estimated_iters}회 (최대 {max_iterations}회 중)")
        
        if show_progress and string_route:
            visualize_string_route(
                gray, pins, string_route, 
                title="String Art Route with Image",
                linewidth=0.5, base_alpha=0.5
            )
            
            only_string_png_path = None
            if save_data:
                only_string_png_path = ask_save_path(
                    title="실 경로만 있는 이미지(PNG) 저장 경로 선택",
                    defaultextension=".png",
                    filetypes=[("PNG Files", "*.png")]
                )
            
            if string_route:
                fig_only_string, ax_only_string = plt.subplots(figsize=(10, 10))
                white_background = np.ones((h, w, 3), dtype=np.uint8) * 255
                ax_only_string.imshow(white_background)
                
                for pin in pins:
                    ax_only_string.plot(pin["x"], pin["y"], 'ro', markersize=5)
                    ax_only_string.text(pin["x"], pin["y"], str(pin["number"]), fontsize=8, color='black')
                
                line_counts = {}
                for i in range(1, len(string_route)):
                    try:
                        prev_pin = next(p for p in pins if p["number"] == string_route[i-1])
                        curr_pin = next(p for p in pins if p["number"] == string_route[i])
                        line_key = tuple(sorted([(prev_pin["x"], prev_pin["y"]), (curr_pin["x"], curr_pin["y"])]))
                        line_counts[line_key] = line_counts.get(line_key, 0) + 1
                    except StopIteration:
                        print(f"경고: 핀 번호 {string_route[i-1]} 또는 {string_route[i]}을(를) 찾을 수 없습니다.")
                
                for line_key, count in line_counts.items():
                    (x1, y1), (x2, y2) = line_key
                    alpha = min(1.0, 0.5 * (1 + 0.1 * (count - 1)))
                    ax_only_string.plot([x1, x2], [y1, y2], 'k-', linewidth=0.7, alpha=alpha)
                
                ax_only_string.set_title("String Art Route Only")
                plt.axis('off')
                
                if only_string_png_path:
                    os.makedirs(os.path.dirname(only_string_png_path), exist_ok=True)
                    fig_only_string.savefig(only_string_png_path, bbox_inches='tight', dpi=300)
                    print("String art route only image saved as:", only_string_png_path)
                
                plt.show()
        
        if save_data and string_route:
            if not excel_output_path:
                excel_output_path = ask_save_path(
                    title="실 경로 데이터(Excel) 저장 경로 선택",
                    defaultextension=".xlsx",
                    filetypes=[("Excel Files", "*.xlsx")]
                )
            if excel_output_path:
                os.makedirs(os.path.dirname(excel_output_path), exist_ok=True)
                
                route_data = []
                for i in range(1, len(string_route)):
                    route_data.append({
                        "단계": i,
                        "시작 핀": string_route[i-1],
                        "도착 핀": string_route[i]
                    })
                route_df = pd.DataFrame(route_data)
                
                with pd.ExcelWriter(excel_output_path) as writer:
                    route_df.to_excel(writer, sheet_name='핀 경로', index=False)
                    
                    pins_df = pd.DataFrame([{
                        "핀 번호": pin["number"],
                        "X 좌표": pin["x"],
                        "Y 좌표": pin["y"]
                    } for pin in pins])
                    pins_df.to_excel(writer, sheet_name='핀 좌표', index=False)
                    
                    grid_df = pd.DataFrame([{
                        "행": block["row"],
                        "열": block["col"],
                        "중심 X": block["center_x"],
                        "중심 Y": block["center_y"],
                        "필요 교차 수": block["value"],
                        "우선순위": block.get("priority", len(priority_areas) if priority_areas else 0)
                    } for block in block_info])
                    grid_df.to_excel(writer, sheet_name='그리드 정보', index=False)
                
                print("실 경로 데이터가 저장되었습니다:", excel_output_path)
    
    if save_data:
        if not png_output_path:
            png_output_path = ask_save_path(
                title="주석 이미지(PNG) 저장 경로 선택",
                defaultextension=".png",
                filetypes=[("PNG Files", "*.png")]
            )
        if png_output_path:
            os.makedirs(os.path.dirname(png_output_path), exist_ok=True)
            fig.savefig(png_output_path, bbox_inches='tight')
            print("Final annotated image saved as:", png_output_path)
    
    plt.show()
    
    if save_data:
        if not json_output_path:
            json_output_path = ask_save_path(
                title="블록 데이터(JSON) 저장 경로 선택",
                defaultextension=".json",
                filetypes=[("JSON Files", "*.json")]
            )
        if json_output_path:
            os.makedirs(os.path.dirname(json_output_path), exist_ok=True)
            data = {
                "block_size": block_size,
                "levels": levels,
                "image_width": w,
                "image_height": h,
                "rows": rows,
                "cols": cols,
                "blocks": block_info,
                "priority_areas": priority_areas,
                "priority_pin_selections": {k: {"pins": list(v["pins"]), "weight": v["weight"]} for k, v in (priority_pin_selections or {}).items()}
            }
            with open(json_output_path, "w", encoding="utf-8") as f:
                json.dump(data, f, indent=4, ensure_ascii=False)
            print("Block numeric data saved as:", json_output_path)
    
    settings_info = {
        "블록 크기": block_size,
        "명암 단계": levels,
        "시간 제한(초)": time_limit,
        "최대 반복 횟수": max_iterations,
        "핀 개수": len(pins) if pins else 0,
        "이미지 크기": f"{image_array.shape[1]}x{image_array.shape[0]}",
        "우선순위 영역 수": len(priority_areas) if priority_areas else 0,
        "제외 영역 음의 가중치": negative_weight
    }
    
    if save_data:
        if excel_output_path:
            with pd.ExcelWriter(excel_output_path, engine='openpyxl', mode='a' if os.path.exists(excel_output_path) else 'w') as writer:
                settings_df = pd.DataFrame([settings_info])
                settings_df.to_excel(writer, sheet_name='설정 정보', index=False)
        
        if json_output_path:
            data["settings"] = settings_info
            with open(json_output_path, "w", encoding="utf-8") as f:
                json.dump(data, f, indent=4, ensure_ascii=False)
        
        settings_txt_path = os.path.join(os.path.dirname(png_output_path or excel_output_path or json_output_path), "settings.txt")
        with open(settings_txt_path, "w", encoding="utf-8") as f:
            f.write("=== 설정 정보 ===\n")
            for key, value in settings_info.items():
                f.write(f"{key}: {value}\n")
    
    fig.text(0.02, 0.02, f"설정: 블록={block_size}, 명암={levels}, 시간제한={time_limit}초, 최대반복={max_iterations}, 우선순위 영역={len(priority_areas) if priority_areas else 0}, 제외 가중치={negative_weight}", 
             fontsize=8, ha='left')
    
    return True, block_info, string_route, grid_value_map, block_info

def calculate_string_route_with_time_limit(pins, grid_cells, block_size, grid_value_map, 
                                         time_limit=300, max_iterations=1000,
                                         priority_areas=None, priority_pin_selections=None,
                                         exclusion_mask=None, negative_weight=0.5):
    start_time = time.time()
    
    image_cells = {pos: value for pos, value in grid_value_map.items() if value > 0}
    total_initial_value = sum(image_cells.values())
    
    print("\n작업 시작...")
    print(f"초기 총 value: {total_initial_value}")
    
    print("\n핀 쌍별 교차 정보 계산 중...")
    pin_pairs_info = {}
    pin_coords = np.array([[p["x"], p["y"]] for p in pins])
    
    pin_indices = list(combinations(range(len(pins)), 2))
    for idx, (i, j) in enumerate(tqdm(pin_indices, desc="핀 쌍 처리")):
        if time.time() - start_time > time_limit * 0.3:
            print("\n전처리 시간 제한 도달")
            break
        
        p1, p2 = pins[i], pins[j]
        crossings = line_crosses_grid_cells(p1, p2, grid_cells, block_size)
        score = sum(image_cells.get(cell_pos, 0) for cell_pos in crossings)
        
        # 제외 영역에 음의 가중치 적용
        if exclusion_mask is not None:
            for cell_pos in crossings:
                cx, cy = cell_pos
                if cx < exclusion_mask.shape[1] and cy < exclusion_mask.shape[0]:
                    if exclusion_mask[cy, cx]:
                        score *= (1 - negative_weight)
        
        pin_pairs_info[(p1["number"], p2["number"])] = {
            "crossings": crossings,
            "score": score
        }
        pin_pairs_info[(p2["number"], p1["number"])] = {
            "crossings": crossings,
            "score": score
        }
    
    print("\n경로 계산 시작...")
    
    priority_levels = sorted(set(cell.get("priority", 0) for cell in grid_cells)) if any("priority" in cell for cell in grid_cells) else [0]
    
    string_route = []
    remaining_values = image_cells.copy()
    
    for priority in priority_levels:
        print(f"\n우선순위 {priority} 영역 처리 중...")
        preferred_pins = set()
        if priority_pin_selections and priority < len(priority_pin_selections):
            preferred_pins = priority_pin_selections.get(priority + 1, {}).get("pins", set())
        priority_weight = priority_pin_selections.get(priority + 1, {}).get("weight", 1.0) if priority_pin_selections else 1.0
        
        best_score = -float('inf')
        best_pair = None
        for p1_num in (preferred_pins or {p["number"] for p in pins}):
            for p2_num in (preferred_pins or {p["number"] for p in pins}):
                if p1_num == p2_num:
                    continue
                pair_key = (p1_num, p2_num)
                if pair_key in pin_pairs_info:
                    score = pin_pairs_info[pair_key]["score"] * priority_weight
                    if score > best_score:
                        best_score = score
                        best_pair = (p1_num, p2_num)
        
        if best_pair is None:
            print(f"우선순위 {priority}: 유효한 시작 쌍 없음")
            continue
        
        if not string_route:
            string_route.extend(best_pair)
        else:
            string_route.append(best_pair[1])
        
        for cell_pos in pin_pairs_info[best_pair]["crossings"]:
            if cell_pos in remaining_values:
                remaining_values[cell_pos] = max(0, remaining_values[cell_pos] - 1)
        
        iteration = 0
        last_update_time = time.time()
        update_interval = 1.0
        
        while iteration < max_iterations:
            current_time = time.time()
            if current_time - start_time > time_limit:
                print(f"우선순위 {priority}: 시간 제한 도달")
                break
            
            iteration += 1
            current_pin = string_route[-1]
            
            if current_time - last_update_time >= update_interval:
                current_total = sum(remaining_values.values())
                progress = ((total_initial_value - current_total) / total_initial_value) * 100
                if progress > 0:
                    estimated_total_time = (current_time - start_time) / progress * 100
                    remaining_time = estimated_total_time - (current_time - start_time)
                    print(f"\r우선순위 {priority} 진행률: {progress:.1f}% | 반복: {iteration}/{max_iterations} | "
                          f"경과: {int(current_time - start_time)}초 | 예상 남은 시간: {int(remaining_time)}초", end="")
                last_update_time = current_time
            
            pq = []
            for next_pin in (preferred_pins or {p["number"] for p in pins}):
                if next_pin in string_route[-2:]:
                    continue
                pair_key = (current_pin, next_pin)
                if pair_key in pin_pairs_info:
                    crossings = pin_pairs_info[pair_key]["crossings"]
                    score = sum(remaining_values.get(cell_pos, 0) for cell_pos in crossings)
                    if next_pin in preferred_pins:
                        score *= priority_weight
                    # 제외 영역에 음의 가중치 적용
                    if exclusion_mask is not None:
                        for cell_pos in crossings:
                            cx, cy = cell_pos
                            if cx < exclusion_mask.shape[1] and cy < exclusion_mask.shape[0]:
                                if exclusion_mask[cy, cx]:
                                    score *= (1 - negative_weight)
                    heapq.heappush(pq, (-score, next_pin))
            
            if not pq:
                print(f"\n우선순위 {priority}: 더 이상 개선 가능한 경로 없음")
                break
            
            score, best_next_pin = heapq.heappop(pq)
            if -score == 0:
                print(f"\n우선순위 {priority}: 추가 점수 0")
                break
            
            string_route.append(best_next_pin)
            
            pair_key = (current_pin, best_next_pin)
            for cell_pos in pin_pairs_info[pair_key]["crossings"]:
                if cell_pos in remaining_values:
                    remaining_values[cell_pos] = max(0, remaining_values[cell_pos] - 1)
            
            if all(value == 0 for value in remaining_values.values()):
                print("\n모든 셀의 value가 0에 도달")
                return string_route
    
    final_total = sum(remaining_values.values())
    final_progress = ((total_initial_value - final_total) / total_initial_value) * 100
    print(f"\n\n작업 완료:")
    print(f"최종 진행률: {final_progress:.1f}%")
    print(f"총 소요 시간: {int(time.time() - start_time)}초")
    print(f"총 반복 횟수: {len(string_route)-1}")
    
    return string_route

def line_crosses_grid_cells(p1, p2, grid_cells, block_size):
    x1, y1 = p1["x"], p1["y"]
    x2, y2 = p2["x"], p2["y"]
    
    distance = math.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2)
    step_size = block_size / 4
    steps = max(1, int(distance / step_size))
    
    crossing_cells = set()
    
    t = np.linspace(0, 1, steps + 1)
    x = x1 + t * (x2 - x1)
    y = y1 + t * (y2 - y1)
    
    cell_centers = np.array([[cell["center_x"], cell["center_y"]] for cell in grid_cells])
    half_block = block_size / 2
    
    for i in range(len(x)):
        distances = np.abs(cell_centers - np.array([x[i], y[i]]))
        within_block = (distances[:, 0] <= half_block) & (distances[:, 1] <= half_block)
        for idx in np.where(within_block)[0]:
            crossing_cells.add((cell_centers[idx, 0], cell_centers[idx, 1]))
    
    return crossing_cells

def main():
    print("프로그램 시작...")
    
    while True:
        root = tk.Tk()
        root.withdraw()
        try:
            file_path = filedialog.askopenfilename(title="이미지 파일 선택",
                                                  filetypes=[("이미지 파일", "*.jpg;*.jpeg;*.png;*.bmp")])
            if not file_path:
                print("파일이 선택되지 않았습니다.")
                return
            
            block_size = ask_block_size() or 10
            levels = ask_levels() or 50
            canvas_type = ask_canvas_type() or "동그라미"
            image = cv2.imread(file_path)
            if image is None:
                print("이미지를 읽을 수 없습니다.")
                return
            
            image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            margin = ask_margin() or 0.2
            canvas_size = ask_canvas_size(canvas_type, image, margin) or (10, 10) if canvas_type == "사각형" else 10
            scale = ask_image_scale() or 1.0
            time_limit = ask_time_limit() or 300
            max_iterations = ask_max_iterations() or 1000
            show_progress = messagebox.askyesno("시각화", "처리 과정을 시각화하시겠습니까?")
            
            image = resize_image(image, scale)
            canvas = create_canvas(canvas_type, canvas_size)
            if canvas is None:
                print("캔버스를 생성할 수 없습니다.")
                return
            
            canvas_with_pins, pins = draw_numbered_points(canvas, canvas_type, canvas_size, margin)
            use_priority_areas = ask_use_priority_areas()
            
            priority_areas = []
            priority_pin_selections = {}
            exclusion_mask = None
            negative_weight = 0.5
            
            canvas_with_image = place_image_on_canvas(canvas_with_pins.copy(), image, canvas_type)
            
            # 그리드 셀 계산을 먼저 수행
            success, _, _, _, block_info = process_image(
                canvas_with_image, block_size, levels, save_data=False, 
                show_progress=False, pins=None
            )
            if not success:
                continue  # 옵션 설정으로 돌아감
            
            if use_priority_areas:
                try:
                    pins, priority_areas, priority_pin_selections = select_priority_regions(canvas_with_image, pins)
                    if not pins:
                        print("핀이 없습니다. 프로그램을 종료합니다.")
                        return
                    
                    # 우선순위 영역 지정 후 제외 영역 지정
                    use_exclude_areas = ask_exclude_areas()
                    if use_exclude_areas:
                        free_draw = FreeDrawExclusion(canvas_with_image, (canvas_with_image.shape[0], canvas_with_image.shape[1]), block_size, block_info)
                        exclusion_mask = free_draw.run()
                        negative_weight = ask_negative_weight()
                except Exception as e:
                    print(f"우선순위 영역 지정 중 오류 발생: {e}")
                    priority_areas = []
                    priority_pin_selections = {}
            
            save_data = messagebox.askyesno("데이터 저장", "처리 결과를 파일로 저장하시겠습니까?")
            
            success, block_info, string_route, grid_value_map, _ = process_image(
                canvas_with_image, block_size, levels, save_data, pins=pins, 
                time_limit=time_limit, max_iterations=max_iterations,
                priority_areas=priority_areas, priority_pin_selections=priority_pin_selections,
                show_progress=show_progress, exclusion_mask=exclusion_mask, negative_weight=negative_weight
            )
            
            if not success:
                continue  # 옵션 설정으로 돌아감
            
            break  # 성공적으로 처리 완료
        except Exception as e:
            print(f"이미지 처리 중 오류 발생: {e}")
            break
        finally:
            root.destroy()

if __name__ == "__main__":
    main()