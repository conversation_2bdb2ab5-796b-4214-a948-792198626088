import cv2
import numpy as np
import matplotlib.pyplot as plt
from PIL import Image
import math
import json
import os
import tkinter as tk
from tkinter import filedialog, simpledialog, messagebox
import pandas as pd
from scipy.spatial.distance import cdist
import heapq
from itertools import combinations
import time  # Import time module for timing limits


def estimate_required_iterations(grid_cells):
    """
    모든 셀이 요구하는 교차 수를 기반으로 반복 횟수를 추정합니다.
    """
    total_required_crossings = sum(cell["value"] for cell in grid_cells)
    average_crossings_per_line = 3  # 경험적으로 한 줄이 평균적으로 지나는 셀 수
    estimated_iterations = int(total_required_crossings / average_crossings_per_line)
    return estimated_iterations
    
def ask_save_path(title, defaultextension, filetypes):
    """
    tkinter 파일 저장 대화상자를 열어 저장 경로를 선택하도록 합니다.
    """
    root = tk.Tk()
    root.withdraw()  # 루트 창 숨김
    path = filedialog.asksaveasfilename(title=title,
                                        defaultextension=defaultextension,
                                        filetypes=filetypes)
    root.destroy()
    return path

def ask_block_size():
    """
    tkinter를 사용하여 블록 크기를 입력받습니다.
    """
    root = tk.Tk()
    root.withdraw()  # 루트 창 숨김
    block_size = simpledialog.askinteger("블록 크기 입력", "블록 크기를 입력하세요 (기본값: 10):", minvalue=1, initialvalue=10)
    root.destroy()
    return block_size

def ask_levels():
    """
    tkinter를 사용하여 levels 값을 입력받습니다.
    """
    root = tk.Tk()
    root.withdraw()  # 루트 창 숨김
    levels = simpledialog.askinteger("Levels 입력", "Levels 값을 입력하세요 (기본값: 50):", minvalue=1, initialvalue=50)
    root.destroy()
    return levels

def ask_canvas_type():
    """
    tkinter를 사용하여 캔버스 타입(사각형, 정삼각형, 동그라미)을 선택합니다.
    """
    root = tk.Tk()
    root.withdraw()  # 루트 창 숨김
    canvas_type = simpledialog.askstring("캔버스 타입 선택", "캔버스 타입을 선택하세요 (사각형, 정삼각형, 동그라미):")
    root.destroy()
    return canvas_type

def ask_canvas_size(canvas_type):
    """
    tkinter를 사용하여 캔버스 크기를 입력받습니다.
    """
    root = tk.Tk()
    root.withdraw()  # 루트 창 숨김
    if canvas_type == "사각형":
        width = simpledialog.askfloat("사각형 가로 길이 입력", "가로 길이(cm)를 입력하세요 (기본값: 10):", minvalue=1, initialvalue=10)
        height = simpledialog.askfloat("사각형 세로 길이 입력", "세로 길이(cm)를 입력하세요 (기본값: 10):", minvalue=1, initialvalue=10)
        size = (width, height)
    elif canvas_type == "정삼각형":
        side = simpledialog.askfloat("정삼각형 한 변의 길이 입력", "한 변의 길이(cm)를 입력하세요 (기본값: 10):", minvalue=1, initialvalue=10)
        size = side
    elif canvas_type == "동그라미":
        radius = simpledialog.askfloat("동그라미 반지름 입력", "반지름(cm)을 입력하세요 (기본값: 10):", minvalue=1, initialvalue=10)
        size = radius
    else:
        size = None
    root.destroy()
    return size

def ask_image_scale():
    """
    tkinter를 사용하여 이미지 크기 조정 비율(%)을 입력받습니다.
    """
    root = tk.Tk()
    root.withdraw()  # 루트 창 숨김
    scale = simpledialog.askfloat("이미지 크기 조정", "이미지 크기 조정 비율(%)을 입력하세요 (기본값: 100):", minvalue=1, initialvalue=100)
    root.destroy()
    return scale / 100  # 비율로 변환

def ask_margin():
    """
    tkinter를 사용하여 가장자리 점 간격(cm)을 입력받습니다.
    """
    root = tk.Tk()
    root.withdraw()  # 루트 창 숨김
    margin = simpledialog.askfloat("가장자리 점 간격 입력", "가장자리 점 간격(cm)을 입력하세요 (기본값: 0.2):", minvalue=0.1, initialvalue=0.2)
    root.destroy()
    return margin

def ask_time_limit():
    """
    tkinter를 사용하여 최대 실행 시간(초)을 입력받습니다.
    """
    root = tk.Tk()
    root.withdraw()  # 루트 창 숨김
    time_limit = simpledialog.askinteger("최대 실행 시간 입력", "최대 실행 시간(초)을 입력하세요 (기본값: 300):", minvalue=1, initialvalue=300)
    root.destroy()
    return time_limit

def ask_max_iterations():
    """
    tkinter를 사용하여 최대 반복 횟수를 입력받습니다.
    """
    root = tk.Tk()
    root.withdraw()  # 루트 창 숨김
    max_iterations = simpledialog.askinteger("최대 반복 횟수 입력", "최대 반복 횟수를 입력하세요 (기본값: 1000):", minvalue=1, initialvalue=1000)
    root.destroy()
    return max_iterations

def create_canvas(canvas_type, size, dpi=300):
    """
    선택한 캔버스 타입과 크기에 따라 캔버스를 생성합니다.
    """
    cm_to_pixel = lambda x: int(x * dpi / 2.54)  # cm를 픽셀로 변환
    if canvas_type == "사각형":
        width, height = size
        canvas = np.ones((cm_to_pixel(height), cm_to_pixel(width), 3), dtype=np.uint8) * 255
    elif canvas_type == "정삼각형":
        side = size
        height = side * math.sqrt(3) / 2  # 정삼각형 높이 계산
        canvas = np.ones((cm_to_pixel(height), cm_to_pixel(side), 3), dtype=np.uint8) * 255
    elif canvas_type == "동그라미":
        radius = size
        diameter = cm_to_pixel(radius * 2)
        canvas = np.ones((diameter, diameter, 3), dtype=np.uint8) * 255
        cv2.circle(canvas, (diameter // 2, diameter // 2), cm_to_pixel(radius), (0, 0, 0), 2)
    else:
        canvas = None
    return canvas

def resize_image(image, scale):
    """
    이미지 크기를 비율에 따라 조정합니다.
    """
    width = int(image.shape[1] * scale)
    height = int(image.shape[0] * scale)
    return cv2.resize(image, (width, height))

def place_image_on_canvas(canvas, image, canvas_type):
    """
    이미지를 캔버스 중앙에 배치합니다.
    """
    h_canvas, w_canvas = canvas.shape[:2]
    h_image, w_image = image.shape[:2]
    x_offset = (w_canvas - w_image) // 2
    y_offset = (h_canvas - h_image) // 2
    canvas[y_offset:y_offset+h_image, x_offset:x_offset+w_image] = image
    return canvas

def draw_numbered_points(canvas, canvas_type, size, margin, dpi=300):
    """
    캔버스 가장자리에 번호가 붙은 점을 표시하고 핀 좌표를 반환합니다.
    """
    cm_to_pixel = lambda x: int(x * dpi / 2.54)  # cm를 픽셀로 변환
    margin_px = cm_to_pixel(margin)
    font = cv2.FONT_HERSHEY_SIMPLEX
    font_scale = 0.5
    font_color = (0, 0, 0)
    thickness = 1
    pins = []  # 핀 좌표와 번호를 저장할 리스트

    if canvas_type == "사각형":
        width, height = size
        width_px, height_px = cm_to_pixel(width), cm_to_pixel(height)
        pin_number = 0
        # 상단 가장자리
        for i in range(0, width_px, margin_px):
            if i < width_px:  # 캔버스 경계를 넘지 않도록
                pin_number += 1
                cv2.circle(canvas, (i, margin_px), 2, (0, 0, 255), -1)
                cv2.putText(canvas, str(pin_number), (i, margin_px + 15), font, font_scale, font_color, thickness)
                pins.append({"number": pin_number, "x": i, "y": margin_px})
        # 우측 가장자리
        for i in range(margin_px, height_px, margin_px):
            if i < height_px:  # 캔버스 경계를 넘지 않도록
                pin_number += 1
                cv2.circle(canvas, (width_px - margin_px, i), 2, (0, 0, 255), -1)
                cv2.putText(canvas, str(pin_number), (width_px - margin_px + 15, i), font, font_scale, font_color, thickness)
                pins.append({"number": pin_number, "x": width_px - margin_px, "y": i})
        # 하단 가장자리 (역순)
        for i in range(width_px - margin_px, -1, -margin_px):
            if i >= 0:  # 캔버스 경계를 넘지 않도록
                pin_number += 1
                cv2.circle(canvas, (i, height_px - margin_px), 2, (0, 0, 255), -1)
                cv2.putText(canvas, str(pin_number), (i, height_px - margin_px + 15), font, font_scale, font_color, thickness)
                pins.append({"number": pin_number, "x": i, "y": height_px - margin_px})
        # 좌측 가장자리 (역순)
        for i in range(height_px - margin_px, margin_px, -margin_px):
            if i >= 0:  # 캔버스 경계를 넘지 않도록
                pin_number += 1
                cv2.circle(canvas, (margin_px, i), 2, (0, 0, 255), -1)
                cv2.putText(canvas, str(pin_number), (margin_px + 15, i), font, font_scale, font_color, thickness)
                pins.append({"number": pin_number, "x": margin_px, "y": i})
    elif canvas_type == "정삼각형":
        side = size
        side_px = cm_to_pixel(side)
        height_px = int(side_px * math.sqrt(3) / 2)
        pin_number = 0
        # 각 변에 점 표시
        # 하단 변
        for i in range(0, side_px, margin_px):
            if i < side_px:  # 캔버스 경계를 넘지 않도록
                pin_number += 1
                cv2.circle(canvas, (i, height_px), 2, (0, 0, 255), -1)
                cv2.putText(canvas, str(pin_number), (i, height_px + 15), font, font_scale, font_color, thickness)
                pins.append({"number": pin_number, "x": i, "y": height_px})
        # 우측 변
        right_count = int((side_px / 2) / margin_px)
        for i in range(right_count):
            x = side_px - i * margin_px * math.cos(math.radians(30))
            y = height_px - i * margin_px * math.sin(math.radians(30))
            if 0 <= x < canvas.shape[1] and 0 <= y < canvas.shape[0]:  # 캔버스 경계를 넘지 않도록
                pin_number += 1
                cv2.circle(canvas, (int(x), int(y)), 2, (0, 0, 255), -1)
                cv2.putText(canvas, str(pin_number), (int(x) + 15, int(y)), font, font_scale, font_color, thickness)
                pins.append({"number": pin_number, "x": int(x), "y": int(y)})
        # 좌측 변
        left_count = int((side_px / 2) / margin_px)
        for i in range(left_count):
            x = i * margin_px * math.cos(math.radians(30))
            y = height_px - i * margin_px * math.sin(math.radians(30))
            if 0 <= x < canvas.shape[1] and 0 <= y < canvas.shape[0]:  # 캔버스 경계를 넘지 않도록
                pin_number += 1
                cv2.circle(canvas, (int(x), int(y)), 2, (0, 0, 255), -1)
                cv2.putText(canvas, str(pin_number), (int(x) - 15, int(y)), font, font_scale, font_color, thickness)
                pins.append({"number": pin_number, "x": int(x), "y": int(y)})
    elif canvas_type == "동그라미":
        radius = size
        radius_px = cm_to_pixel(radius)
        center = (radius_px, radius_px)
        diameter = radius_px * 2
        pin_number = 0
        # 원주에 점 표시
        pins_count = int(2 * math.pi * radius_px / margin_px)  # 마진에 따라 핀 수 계산
        angle_step = 360 / pins_count
        for angle in range(0, 360, int(angle_step)):  # 간격에 따라 핀 생성
            pin_number += 1
            x = int(center[0] + radius_px * math.cos(math.radians(angle)))
            y = int(center[1] + radius_px * math.sin(math.radians(angle)))
            if 0 <= x < canvas.shape[1] and 0 <= y < canvas.shape[0]:  # 캔버스 경계를 넘지 않도록
                cv2.circle(canvas, (x, y), 2, (0, 0, 255), -1)
                cv2.putText(canvas, str(pin_number), (x + 1, y + 1), font, font_scale, font_color, thickness)
                pins.append({"number": pin_number, "x": x, "y": y})
    return canvas, pins

def process_image(image_array, block_size=10, levels=50,
                  save_data=False, json_output_path=None, png_output_path=None, excel_output_path=None, pins=None,
                  time_limit=300, max_iterations=1000):  # 시간 제한과 반복 횟수 제한 매개변수 추가
    """
    주어진 이미지 배열에 대해 처리 단계를 실행합니다.
    """
    # Step 1: 원본 이미지 시각화 (RGB)
    fig, ax = plt.subplots(figsize=(8, 8))
    ax.imshow(image_array)
    ax.set_title("Original Image")
    plt.axis('off')
    plt.show()
    
    # Step 2: 회색조 변환 및 시각화
    if len(image_array.shape) == 3:
        gray = cv2.cvtColor(image_array, cv2.COLOR_RGB2GRAY)
    else:
        gray = image_array.copy()
    fig, ax = plt.subplots(figsize=(8, 8))
    ax.imshow(gray, cmap='gray')
    ax.set_title("Grayscale Image")
    plt.axis('off')
    plt.show()
    
    # Step 3: 그리드 오버레이가 있는 회색조 이미지 시각화
    h, w = gray.shape
    fig, ax = plt.subplots(figsize=(8, 8))
    ax.imshow(gray, cmap='gray')
    for i in range(0, h, block_size):
        if i < h:  # 캔버스 경계를 넘지 않도록
            ax.axhline(i, color='yellow', linestyle='--', linewidth=0.5)
    for j in range(0, w, block_size):
        if j < w:  # 캔버스 경계를 넘지 않도록
            ax.axvline(j, color='yellow', linestyle='--', linewidth=0.5)
    ax.set_title("Grayscale Image with Grid Overlay")
    plt.axis('off')
    plt.show()
    
    # Step 4: 블록별 평균 회색 농도 계산 및 1~levels 단계로 매핑
    rows = math.ceil(h / block_size)
    cols = math.ceil(w / block_size)
    processed_image = np.zeros((rows, cols), dtype=int)
    
    for i in range(0, h, block_size):
        for j in range(0, w, block_size):
            # 이미지 경계를 초과하지 않도록 처리
            block = gray[i:min(i+block_size, h), j:min(j+block_size, w)]
            avg_gray = np.mean(block)
            # 가장 밝은 부분을 0, 가장 어두운 부분을 levels-1로 매핑
            level = int((255 - avg_gray) / 255 * (levels - 1))
            processed_image[i // block_size, j // block_size] = level
            
    # Step 5: 최종 주석(숫자) 이미지 시각화 및 블록 정보 수집
    fig, ax = plt.subplots(figsize=(8, 8))
    ax.imshow(gray, cmap='gray')
    block_info = []  # 각 블록의 정보 저장 리스트
    for i in range(rows):
        for j in range(cols):
            # 각 블록의 중앙 좌표 계산 (마지막 블록은 실제 크기가 작을 수 있음)
            x_center = j * block_size + block_size // 2
            y_center = i * block_size + block_size // 2
            value = int(processed_image[i, j])
            ax.text(x_center, y_center, str(value),
                    color='red', fontsize=8, ha='center', va='center')
            block_info.append({
                "row": i,
                "col": j,
                "center_x": int(x_center),
                "center_y": int(y_center),
                "value": value
            })
    # 그리드 선 추가
    for i in range(0, h, block_size):
        if i < h:  # 캔버스 경계를 넘지 않도록
            ax.axhline(i, color='yellow', linestyle='--', linewidth=0.5)
    for j in range(0, w, block_size):
        if j < w:  # 캔버스 경계를 넘지 않도록
            ax.axvline(j, color='yellow', linestyle='--', linewidth=0.5)
    ax.set_title("Final Annotated Image")
    plt.axis('off')
    
    # 실의 경로 계산 (핀이 제공된 경우)
    if pins:
        print("실 경로 계산을 시작합니다...")
        grid_cells = []
        for block in block_info:
            if block["value"] > 0:  # 값이 있는 셀만 처리
                grid_cells.append({
                    "center_x": block["center_x"],
                    "center_y": block["center_y"],
                    "value": block["value"],
                    "crossings": 0  # 실이 통과한 횟수 카운트
                })
        
        string_route = calculate_string_route_with_time_limit(pins, grid_cells, block_size, time_limit, max_iterations)
        

        # 사용자에게 예측 반복 횟수 출력
        estimated_iters = estimate_required_iterations(grid_cells)
        print(f"🔍 예상 반복 횟수: 약 {estimated_iters}회 (최대 {max_iterations}회 중)")

        # 실 경로 시각화
        fig, ax = plt.subplots(figsize=(10, 10))
        ax.imshow(gray, cmap='gray')
        
        # 핀 위치 표시
        for pin in pins:
            ax.plot(pin["x"], pin["y"], 'ro', markersize=5)
            ax.text(pin["x"], pin["y"], str(pin["number"]), fontsize=8, color='white')
        
        # 실 경로 그리기
        for i in range(1, len(string_route)):
            # 오류 수정: StopIteration 방지를 위한 예외 처리 추가
            try:
                prev_pin = next((p for p in pins if p["number"] == string_route[i-1]), None)
                curr_pin = next((p for p in pins if p["number"] == string_route[i]), None)
                
                # 두 핀이 모두 존재하는 경우에만 선 그리기
                if prev_pin and curr_pin:
                    ax.plot([prev_pin["x"], curr_pin["x"]], [prev_pin["y"], curr_pin["y"]], 'b-', linewidth=0.5, alpha=0.5)
                else:
                    print(f"경고: 핀 번호 {string_route[i-1]} 또는 {string_route[i]}을(를) 찾을 수 없습니다.")
            except Exception as e:
                print(f"실 경로 그리기 중 오류 발생: {e}")
                print(f"현재 i: {i}, 경로 길이: {len(string_route)}")
                print(f"현재 핀 번호: {string_route[i-1]} -> {string_route[i]}")
                continue
        
        ax.set_title("String Art Route")
        plt.axis('off')
        plt.show()
        
        # 엑셀 파일로 경로 저장
        if save_data:
            if not excel_output_path:
                excel_output_path = ask_save_path(
                    title="실 경로 데이터(Excel) 저장 경로 선택",
                    defaultextension=".xlsx",
                    filetypes=[("Excel Files", "*.xlsx")]
                )
            if excel_output_path:
                dir_excel = os.path.dirname(excel_output_path)
                if dir_excel:
                    os.makedirs(dir_excel, exist_ok=True)
                    
                # 핀-핀 이동 경로를 저장할 데이터프레임 생성
                route_data = []
                for i in range(1, len(string_route)):
                    route_data.append({
                        "단계": i,
                        "시작 핀": string_route[i-1],
                        "도착 핀": string_route[i]
                    })
                route_df = pd.DataFrame(route_data)
                
                # 엑셀 파일로 저장
                with pd.ExcelWriter(excel_output_path) as writer:
                    route_df.to_excel(writer, sheet_name='핀 경로', index=False)
                    
                    # 추가 시트: 각 핀의 좌표 정보
                    pins_df = pd.DataFrame([{
                        "핀 번호": pin["number"],
                        "X 좌표": pin["x"],
                        "Y 좌표": pin["y"]
                    } for pin in pins])
                    pins_df.to_excel(writer, sheet_name='핀 좌표', index=False)
                    
                    # 추가 시트: 그리드 셀 정보
                    grid_df = pd.DataFrame([{
                        "행": block["row"],
                        "열": block["col"],
                        "중심 X": block["center_x"],
                        "중심 Y": block["center_y"],
                        "필요 교차 수": block["value"]
                    } for block in block_info])
                    grid_df.to_excel(writer, sheet_name='그리드 정보', index=False)
                
                print("실 경로 데이터가 저장되었습니다:", excel_output_path)
            else:
                print("Excel 파일 저장 경로가 선택되지 않았습니다. 저장하지 않습니다.")
    
    # 만약 저장 옵션이 활성화되었다면, 사용자가 직접 저장 경로를 선택합니다.
    if save_data:
        if not png_output_path:
            png_output_path = ask_save_path(
                title="주석 이미지(PNG) 저장 경로 선택",
                defaultextension=".png",
                filetypes=[("PNG Files", "*.png")]
            )
        if png_output_path:
            # 출력 디렉토리가 있다면 생성
            dir_png = os.path.dirname(png_output_path)
            if dir_png:
                os.makedirs(dir_png, exist_ok=True)
            fig.savefig(png_output_path, bbox_inches='tight')
            print("Final annotated image saved as:", png_output_path)
        else:
            print("PNG 파일 저장 경로가 선택되지 않았습니다. 저장하지 않습니다.")
    
    plt.show()
    
    if save_data:
        if not json_output_path:
            json_output_path = ask_save_path(
                title="블록 데이터(JSON) 저장 경로 선택",
                defaultextension=".json",
                filetypes=[("JSON Files", "*.json")]
            )
        if json_output_path:
            dir_json = os.path.dirname(json_output_path)
            if dir_json:
                os.makedirs(dir_json, exist_ok=True)
            data = {
                "block_size": block_size,
                "levels": levels,
                "image_width": w,
                "image_height": h,
                "rows": rows,
                "cols": cols,
                "blocks": block_info
            }
            with open(json_output_path, "w", encoding="utf-8") as f:
                json.dump(data, f, indent=4)
            print("Block numeric data saved as:", json_output_path)
        else:
            print("JSON 파일 저장 경로가 선택되지 않았습니다. 저장하지 않습니다.")

# 시간 제한 및 반복 횟수 제한이 추가된 새 함수
def calculate_string_route_with_time_limit(pins, grid_cells, block_size, time_limit=300, max_iterations=1000):
    """
    그리드 셀이 요구하는 교차 횟수를 만족시키면서 최적의 실 경로를 계산합니다.
    시간 제한과 반복 횟수 제한이 적용됩니다.
    
    Args:
        pins: 핀 좌표 및 번호 정보 리스트
        grid_cells: 그리드 셀 정보 리스트
        block_size: 그리드 셀 크기
        time_limit: 최대 실행 시간(초)
        max_iterations: 최대 반복 횟수
    
    Returns:
        최적의 실 경로를 나타내는 핀 번호 리스트
    """
    start_time = time.time()
    
    # 각 핀 쌍이 교차하는 그리드 셀 계산
    print("핀 쌍별 교차 그리드 셀 계산 중...")
    pin_pairs_crossing = {}
    
    # 핀 쌍 수가 많을 경우 진행률 표시
    total_pairs = len(list(combinations(pins, 2)))
    progress_step = max(1, total_pairs // 20)  # 5% 단위로 진행률 표시
    
    for i, (p1, p2) in enumerate(combinations(pins, 2)):
        # 시간 제한 확인
        if time.time() - start_time > time_limit * 0.3:  # 전체 시간의 30%만 교차 계산에 사용
            print(f"경고: 핀 쌍 교차 계산에 시간 제한이 초과되었습니다. 지금까지 계산된 {i}/{total_pairs} 핀 쌍으로 계속합니다.")
            break
            
        # 진행률 표시
        if (i + 1) % progress_step == 0:
            print(f"진행 중: {i + 1}/{total_pairs} 핀 쌍 처리 완료 ({(i + 1) / total_pairs * 100:.1f}%)")
            
        # 두 핀 사이의 선이 교차하는 그리드 셀 계산
        crossings = line_crosses_grid_cells(p1, p2, grid_cells, block_size)
        
        # 핀 쌍과 교차 그리드 셀 관계 저장
        pair_key = (p1["number"], p2["number"])
        pin_pairs_crossing[pair_key] = crossings
    
    # 초기 핀 선택 (첫 번째 핀에서 시작)
    current_pin = pins[0]["number"]
    string_route = [current_pin]
    
    # 각 그리드 셀의 현재 교차 횟수를 추적
    current_crossings = {(cell["center_x"], cell["center_y"]): 0 for cell in grid_cells}
    
    # 그리드 셀의 목표 교차 횟수 (value 값)
    target_crossings = {(cell["center_x"], cell["center_y"]): cell["value"] for cell in grid_cells}
    
    print("실 경로 탐색 시작...")
    
    # 경로 탐색 시작
    iteration = 0
    while iteration < max_iterations:
        # 시간 제한 확인
        if time.time() - start_time > time_limit:
            print(f"경고: 시간 제한({time_limit}초)이 초과되었습니다. 지금까지의 경로로 결과를 반환합니다.")
            break
            
        iteration += 1
        if iteration % 100 == 0:
            print(f"진행 중: {iteration} 반복 완료, 경과 시간: {time.time() - start_time:.1f}초")
            
        # 목표 달성 여부 확인
        satisfied_cells = 0
        total_cells_with_targets = 0
        
        for cell_pos, target in target_crossings.items():
            if target > 0:
                total_cells_with_targets += 1
                if current_crossings[cell_pos] >= target:
                    satisfied_cells += 1
        
        # 목표 달성률 계산 및 표시
        if total_cells_with_targets > 0:
            satisfaction_rate = satisfied_cells / total_cells_with_targets * 100
            if iteration % 100 == 0:
                print(f"목표 달성률: {satisfaction_rate:.1f}% ({satisfied_cells}/{total_cells_with_targets} 셀)")
            
            # 모든 셀이 목표에 도달했으면 종료
            if satisfied_cells == total_cells_with_targets:
                print("모든 셀이 목표 교차 횟수에 도달했습니다!")
                break
        
        # 다음 최적의 핀 선택
        best_score = -float('inf')
        best_next_pin = None
        
        for pin in pins:
            next_pin = pin["number"]
            if next_pin == current_pin:
                continue  # 같은 핀으로는 이동하지 않음
                
            # 두 핀 사이의 선이 교차하는 그리드 셀 가져오기
            pair_key = (current_pin, next_pin) if (current_pin, next_pin) in pin_pairs_crossing else (next_pin, current_pin)
            
            if pair_key not in pin_pairs_crossing:
                continue  # 계산되지 않은 핀 쌍은 건너뛰기
                
            crossings = pin_pairs_crossing[pair_key]
            
            # 점수 계산: 목표에 가까운 셀에 더 높은 가중치 부여
            score = 0
            for cell_pos in crossings:
                if cell_pos in target_crossings:
                    # 목표와 현재 교차 횟수의 차이가 클수록 점수를 높게 책정
                    diff = target_crossings[cell_pos] - current_crossings[cell_pos]
                    if diff > 0:  # 아직 목표에 도달하지 않은 셀만 점수 계산
                        score += diff
                    else:  # 이미 목표에 도달했거나 초과한 셀은 페널티 부여
                        score -= abs(diff) * 0.5  # 페널티는 절반의 가중치
            
            # 최적의 다음 핀 업데이트
            if score > best_score:
                best_score = score
                best_next_pin = next_pin
        
        # 다음 핀이 없으면 종료
        if best_next_pin is None:
            print("더 이상 개선할 수 있는 경로가 없습니다.")
            break
            
        # 경로에 다음 핀 추가 및 교차 횟수 업데이트
        string_route.append(best_next_pin)
        current_pin = best_next_pin
        
        # 교차 그리드 셀 업데이트
        prev_pin_num = string_route[-2]
        curr_pin_num = string_route[-1]
        pair_key = (prev_pin_num, curr_pin_num) if (prev_pin_num, curr_pin_num) in pin_pairs_crossing else (curr_pin_num, prev_pin_num)
        
        if pair_key in pin_pairs_crossing:
            crossings = pin_pairs_crossing[pair_key]
            for cell_pos in crossings:
                if cell_pos in current_crossings:
                    current_crossings[cell_pos] += 1
    
    # 반복 종료 원인 표시
    if iteration >= max_iterations:
        print(f"최대 반복 횟수({max_iterations})에 도달했습니다.")
    elif time.time() - start_time > time_limit:
        print(f"시간 제한({time_limit}초)이 초과되었습니다.")
    
    print(f"총 실행 시간: {time.time() - start_time:.2f}초, 총 반복 횟수: {iteration}")
    print(f"최종 실 경로 길이: {len(string_route)} 핀")
    
    return string_route

def line_crosses_grid_cells(p1, p2, grid_cells, block_size):
    """
    두 핀 사이의 선이 교차하는 그리드 셀을 계산합니다.
    
    Args:
        p1: 첫 번째 핀 정보
        p2: 두 번째 핀 정보
        grid_cells: 그리드 셀 정보 리스트
        block_size: 그리드 셀 크기
    
    Returns:
        교차하는 그리드 셀 좌표 집합 (center_x, center_y)
    """
    x1, y1 = p1["x"], p1["y"]
    x2, y2 = p2["x"], p2["y"]
    
    # 두 점 사이의 거리
    distance = math.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2)
    
    # 직선을 따라 충분히 많은 점을 샘플링하여 교차하는 셀 확인
    # block_size의 1/4 간격으로 샘플링 (정밀도 향상)
    step_size = block_size / 4
    steps = max(1, int(distance / step_size))
    
    crossing_cells = set()
    
    for i in range(steps + 1):
        # 선형 보간을 통해 직선 위의 점 계산
        t = i / steps if steps > 0 else 0
        x = x1 + t * (x2 - x1)
        y = y1 + t * (y2 - y1)
        
        # 이 점이 속한 그리드 셀 찾기
        for cell in grid_cells:
            # 셀의 중심 좌표
            cx, cy = cell["center_x"], cell["center_y"]
            
            # 점이 셀 내부에 있는지 확인 (셀을 block_size x block_size 정사각형으로 가정)
            half_block = block_size / 2
            if cx - half_block <= x <= cx + half_block and cy - half_block <= y <= cy + half_block:
                # 셀 중심 좌표를 키로 사용하여 세트에 추가
                crossing_cells.add((cx, cy))
                break
    
    return crossing_cells

def main():
    """
    메인 함수: 사용자로부터 이미지를 선택받고 처리합니다.
    """
    root = tk.Tk()
    root.withdraw()  # 루트 창 숨김
    file_path = filedialog.askopenfilename(title="이미지 파일 선택",
                                          filetypes=[("이미지 파일", "*.jpg;*.jpeg;*.png;*.bmp")])
    if not file_path:
        print("파일이 선택되지 않았습니다.")
        return
    
    # 처리 옵션 선택
    block_size = ask_block_size()
    if not block_size:
        block_size = 10  # 기본값
    
    levels = ask_levels()
    if not levels:
        levels = 50  # 기본값
    
    # 캔버스 타입 선택
    canvas_type = ask_canvas_type()
    if not canvas_type or canvas_type not in ["사각형", "정삼각형", "동그라미"]:
        print("유효하지 않은 캔버스 타입입니다. 기본값인 '사각형'으로 설정합니다.")
        canvas_type = "사각형"
    
    # 캔버스 크기 선택
    canvas_size = ask_canvas_size(canvas_type)
    if not canvas_size:
        if canvas_type == "사각형":
            canvas_size = (10, 10)  # 기본값 10cm x 10cm
        else:
            canvas_size = 10  # 기본 반지름 또는 변 길이 10cm
    
    # 이미지 크기 조정 비율 선택
    scale = ask_image_scale()
    if not scale:
        scale = 1.0  # 기본값 100%
    
    # 핀 간격 선택
    margin = ask_margin()
    if not margin:
        margin = 0.2  # 기본값 0.2cm
    
    # 시간 제한 선택
    time_limit = ask_time_limit()
    if not time_limit:
        time_limit = 300  # 기본값 300초(5분)
    
    # 최대 반복 횟수 선택
    max_iterations = ask_max_iterations()
    if not max_iterations:
        max_iterations = 1000  # 기본값 1000회
    
    # 이미지 읽기
    image = cv2.imread(file_path)
    if image is None:
        print("이미지를 읽을 수 없습니다.")
        return
    
    # BGR에서 RGB로 변환
    image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    
    # 이미지 크기 조정
    image = resize_image(image, scale)
    
    # 캔버스 생성
    canvas = create_canvas(canvas_type, canvas_size)
    if canvas is None:
        print("캔버스를 생성할 수 없습니다.")
        return
    
    # 캔버스에 번호가 붙은 점 추가 및 핀 좌표 얻기
    canvas_with_pins, pins = draw_numbered_points(canvas, canvas_type, canvas_size, margin)
    
    # 이미지를 캔버스 중앙에 배치
    canvas_with_image = place_image_on_canvas(canvas_with_pins.copy(), image, canvas_type)
    
    # 이미지 처리 및 결과 저장
    save_data = messagebox.askyesno("데이터 저장", "처리 결과를 파일로 저장하시겠습니까?")
    process_image(canvas_with_image, block_size, levels, save_data, pins=pins, time_limit=time_limit, max_iterations=max_iterations)

if __name__ == "__main__":
    main()