import cv2
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.widgets import RectangleSelector
from PIL import Image
import math
import json
import os
import tkinter as tk
from tkinter import filedialog, simpledialog, messagebox
import pandas as pd
from scipy.spatial.distance import cdist
import heapq
from itertools import combinations
import time  # Import time module for timing limits
import concurrent.futures
from multiprocessing import cpu_count
from scipy import ndimage



# 추가: 우선순위 영역 선택 기능
selected_regions = []

def onselect(eclick, erelease):
    x1, y1 = int(eclick.xdata), int(eclick.ydata)
    x2, y2 = int(erelease.xdata), int(erelease.ydata)
    selected_regions.append(((min(x1, x2), min(y1, y2)), (max(x1, x2), max(y1, y2))))
    print(f"[선택된 우선 영역] 좌상단: ({min(x1,x2)}, {min(y1,y2)}), 우하단: ({max(x1,x2)}, {max(y1,y2)})")

def select_priority_regions(image_array):
    """
    이미지에서 우선순위 영역을 선택하는 함수
    
    Args:
        image_array: 입력 이미지 배열
    
    Returns:
        selected_regions: 선택된 영역의 좌표 리스트
    """
    global selected_regions
    selected_regions = []  # 선택 영역 초기화
    
    print("\n🖱️ 마우스로 드래그하여 우선순위 영역을 지정하세요. (종료하려면 창을 닫으세요)")
    fig, ax = plt.subplots(figsize=(10, 10))
    ax.imshow(image_array)
    
    # 최신 버전의 RectangleSelector 매개변수로 수정
    rs = RectangleSelector(ax, onselect, 
                          props=dict(facecolor='red', edgecolor='red', alpha=0.2),
                          interactive=True,
                          button=[1],  # 왼쪽 마우스 버튼만 사용
                          minspanx=5, 
                          minspany=5,
                          spancoords='pixels',
                          useblit=True)
    
    plt.title("마우스로 드래그하여 우선 영역 지정 후 창을 닫으세요")
    plt.show()
    
    return selected_regions
def estimate_required_iterations(grid_cells):
    """
    모든 셀이 요구하는 교차 수를 기반으로 반복 횟수를 추정합니다.
    """
    total_required_crossings = sum(cell["value"] for cell in grid_cells)
    average_crossings_per_line = 3  # 경험적으로 한 줄이 평균적으로 지나는 셀 수
    estimated_iterations = int(total_required_crossings / average_crossings_per_line)
    return estimated_iterations
    
def ask_save_path(title, defaultextension, filetypes):
    """
    tkinter 파일 저장 대화상자를 열어 저장 경로를 선택하도록 합니다.
    """
    root = tk.Tk()
    root.withdraw()  # 루트 창 숨김
    path = filedialog.asksaveasfilename(title=title,
                                        defaultextension=defaultextension,
                                        filetypes=filetypes)
    root.destroy()
    return path

def ask_block_size():
    """
    tkinter를 사용하여 블록 크기를 입력받습니다.
    """
    root = tk.Tk()
    root.withdraw()  # 루트 창 숨김
    block_size = simpledialog.askinteger("블록 크기 입력", "블록 크기를 입력하세요 (기본값: 10):", minvalue=1, initialvalue=10)
    root.destroy()
    return block_size

def ask_levels():
    """
    tkinter를 사용하여 levels 값을 입력받습니다.
    """
    root = tk.Tk()
    root.withdraw()  # 루트 창 숨김
    levels = simpledialog.askinteger("Levels 입력", "Levels 값을 입력하세요 (기본값: 50):", minvalue=1, initialvalue=50)
    root.destroy()
    return levels

def ask_canvas_type():
    """
    tkinter를 사용하여 캔버스 타입(사각형, 정삼각형, 동그라미)을 선택합니다.
    """
    root = tk.Tk()
    root.title("캔버스 타입 선택")
    root.geometry("300x200")
    
    # 변수 초기화 (기본값: 동그라미)
    canvas_type = tk.StringVar(value="동그라미")
    
    # 라디오 버튼 생성
    tk.Radiobutton(root, text="사각형", variable=canvas_type, value="사각형").pack(pady=10)
    tk.Radiobutton(root, text="정삼각형", variable=canvas_type, value="정삼각형").pack(pady=10)
    tk.Radiobutton(root, text="동그라미", variable=canvas_type, value="동그라미").pack(pady=10)
    
    # 확인 버튼
    def on_confirm():
        root.quit()
    
    tk.Button(root, text="확인", command=on_confirm).pack(pady=20)
    
    # 창을 화면 중앙에 위치
    root.update_idletasks()
    width = root.winfo_width()
    height = root.winfo_height()
    x = (root.winfo_screenwidth() // 2) - (width // 2)
    y = (root.winfo_screenheight() // 2) - (height // 2)
    root.geometry(f'{width}x{height}+{x}+{y}')
    
    root.mainloop()
    selected_type = canvas_type.get()
    root.destroy()
    return selected_type

def calculate_optimal_canvas_size(image, canvas_type, margin, dpi=300):
    """
    이미지가 캔버스를 벗어나지 않는 최적의 캔버스 크기를 계산합니다.
    기본 크기에서 10%의 여유 공간을 추가합니다.
    """
    cm_to_pixel = lambda x: int(x * dpi / 2.54)
    h_image, w_image = image.shape[:2]
    margin_px = cm_to_pixel(margin)
    
    # 10% 여유 공간을 위한 계수
    margin_factor = 1.1
    
    if canvas_type == "사각형":
        # 이미지 크기에 여유 공간 추가 (10% 추가)
        width_cm = (w_image + 2 * margin_px) * 2.54 / dpi * margin_factor
        height_cm = (h_image + 2 * margin_px) * 2.54 / dpi * margin_factor
        return (width_cm, height_cm)
    elif canvas_type == "정삼각형":
        # 정삼각형의 경우 가장 긴 변을 기준으로 계산 (10% 추가)
        max_dimension = max(w_image, h_image)
        side_cm = (max_dimension + 2 * margin_px) * 2.54 / dpi * margin_factor
        return side_cm
    elif canvas_type == "동그라미":
        # 원의 경우 지름을 기준으로 계산 (10% 추가)
        max_dimension = max(w_image, h_image)
        radius_cm = (max_dimension/2 + margin_px) * 2.54 / dpi * margin_factor
        return radius_cm
    return None

def ask_canvas_size(canvas_type, image=None, margin=None):
    """
    tkinter를 사용하여 캔버스 크기를 입력받거나 자동 설정합니다.
    """
    root = tk.Tk()
    root.withdraw()
    
    if image is not None and margin is not None:
        # 최적의 캔버스 크기 계산
        optimal_size = calculate_optimal_canvas_size(image, canvas_type, margin)
        
        # 사용자에게 선택지 제공
        message = f"최적의 캔버스 크기가 계산되었습니다:\n"
        if canvas_type == "사각형":
            message += f"가로: {optimal_size[0]:.1f}cm, 세로: {optimal_size[1]:.1f}cm\n"
        else:
            message += f"크기: {optimal_size:.1f}cm\n"
        message += "\n이 크기를 사용하시겠습니까?\n"
        message += "아니오를 선택하면 수동으로 크기를 설정할 수 있습니다."
        
        choice = messagebox.askyesno("캔버스 크기 설정", message)
        
        if choice:
            return optimal_size
    
    # 수동 설정 또는 최적 크기 계산이 불가능한 경우
    if canvas_type == "사각형":
        width = simpledialog.askfloat("사각형 가로 길이 입력", "가로 길이(cm)를 입력하세요 (기본값: 10):", minvalue=1, initialvalue=10)
        height = simpledialog.askfloat("사각형 세로 길이 입력", "세로 길이(cm)를 입력하세요 (기본값: 10):", minvalue=1, initialvalue=10)
        size = (width, height)
    elif canvas_type == "정삼각형":
        side = simpledialog.askfloat("정삼각형 한 변의 길이 입력", "한 변의 길이(cm)를 입력하세요 (기본값: 10):", minvalue=1, initialvalue=10)
        size = side
    elif canvas_type == "동그라미":
        radius = simpledialog.askfloat("동그라미 반지름 입력", "반지름(cm)을 입력하세요 (기본값: 10):", minvalue=1, initialvalue=10)
        size = radius
    else:
        size = None
    
    root.destroy()
    return size

def ask_image_scale():
    """
    tkinter를 사용하여 이미지 크기 조정 비율(%)을 입력받습니다.
    """
    root = tk.Tk()
    root.withdraw()  # 루트 창 숨김
    scale = simpledialog.askfloat("이미지 크기 조정", "이미지 크기 조정 비율(%)을 입력하세요 (기본값: 100):", minvalue=1, initialvalue=100)
    root.destroy()
    return scale / 100  # 비율로 변환

def ask_margin():
    """
    tkinter를 사용하여 가장자리 점 간격(cm)을 입력받습니다.
    """
    root = tk.Tk()
    root.withdraw()  # 루트 창 숨김
    margin = simpledialog.askfloat("가장자리 점 간격 입력", "가장자리 점 간격(cm)을 입력하세요 (기본값: 0.2):", minvalue=0.1, initialvalue=0.2)
    root.destroy()
    return margin

def ask_time_limit():
    """
    tkinter를 사용하여 최대 실행 시간(초)을 입력받습니다.
    """
    root = tk.Tk()
    root.withdraw()  # 루트 창 숨김
    time_limit = simpledialog.askinteger("최대 실행 시간 입력", "최대 실행 시간(초)을 입력하세요 (기본값: 300):", minvalue=1, initialvalue=300)
    root.destroy()
    return time_limit

def ask_max_iterations():
    """
    tkinter를 사용하여 최대 반복 횟수를 입력받습니다.
    """
    root = tk.Tk()
    root.withdraw()  # 루트 창 숨김
    max_iterations = simpledialog.askinteger("최대 반복 횟수 입력", "최대 반복 횟수를 입력하세요 (기본값: 1000):", minvalue=1, initialvalue=1000)
    root.destroy()
    return max_iterations

def create_canvas(canvas_type, size, dpi=300):
    """
    선택한 캔버스 타입과 크기에 따라 캔버스를 생성합니다.
    캔버스 형태는 value 값에 영향을 주지 않습니다.
    """
    cm_to_pixel = lambda x: int(x * dpi / 2.54)  # cm를 픽셀로 변환
    if canvas_type == "사각형":
        width, height = size
        canvas = np.ones((cm_to_pixel(height), cm_to_pixel(width), 3), dtype=np.uint8) * 255
    elif canvas_type == "정삼각형":
        side = size
        height = side * math.sqrt(3) / 2  # 정삼각형 높이 계산
        canvas = np.ones((cm_to_pixel(height), cm_to_pixel(side), 3), dtype=np.uint8) * 255
    elif canvas_type == "동그라미":
        radius = size
        diameter = cm_to_pixel(radius * 2)
        canvas = np.ones((diameter, diameter, 3), dtype=np.uint8) * 255
        cv2.circle(canvas, (diameter // 2, diameter // 2), cm_to_pixel(radius), (0, 0, 0), 2)
    else:
        canvas = None
    return canvas

def resize_image(image, scale):
    """
    이미지 크기를 비율에 따라 조정합니다.
    """
    width = int(image.shape[1] * scale)
    height = int(image.shape[0] * scale)
    return cv2.resize(image, (width, height))

def place_image_on_canvas(canvas, image, canvas_type):
    """
    이미지를 캔버스 중앙에 배치합니다.
    """
    h_canvas, w_canvas = canvas.shape[:2]
    h_image, w_image = image.shape[:2]
    
    # 이미지가 캔버스보다 큰 경우 처리
    if h_image > h_canvas or w_image > w_canvas:
        message = f"이미지 크기({h_image}x{w_image})가 캔버스 크기({h_canvas}x{w_canvas})보다 큽니다.\n"
        message += "다음 중 하나를 선택하세요:\n"
        message += "1. 이미지 크기 조정 비율을 더 작게 설정\n"
        message += "2. 캔버스 크기를 더 크게 설정\n"
        message += "3. 프로그램 종료"
        
        root = tk.Tk()
        root.withdraw()
        choice = messagebox.askquestion("크기 불일치", message + "\n\n다시 설정하시겠습니까?")
        root.destroy()
        
        if choice == 'yes':
            # 이미지 크기 조정 비율 재설정
            scale = ask_image_scale()
            if scale:
                image = resize_image(image, scale)
                return place_image_on_canvas(canvas, image, canvas_type)
            else:
                # 캔버스 크기 재설정
                canvas_size = ask_canvas_size(canvas_type)
                if canvas_size:
                    canvas = create_canvas(canvas_type, canvas_size)
                    return place_image_on_canvas(canvas, image, canvas_type)
        
        # 사용자가 취소한 경우
        raise ValueError("사용자가 작업을 취소했습니다.")
    
    # 이미지가 캔버스보다 작은 경우 정상적으로 배치
    x_offset = (w_canvas - w_image) // 2
    y_offset = (h_canvas - h_image) // 2
    canvas[y_offset:y_offset+h_image, x_offset:x_offset+w_image] = image
    return canvas

def draw_numbered_points(canvas, canvas_type, size, margin, dpi=300):
    """
    캔버스 가장자리에 번호가 붙은 점을 표시하고 핀 좌표를 반환합니다.
    핀과 핀 번호는 value 값에 영향을 주지 않습니다.
    """
    cm_to_pixel = lambda x: int(x * dpi / 2.54)  # cm를 픽셀로 변환
    margin_px = cm_to_pixel(margin)
    font = cv2.FONT_HERSHEY_SIMPLEX
    font_scale = 0.5
    font_color = (0, 0, 0)
    thickness = 1
    pins = []  # 핀 좌표와 번호를 저장할 리스트

    if canvas_type == "사각형":
        width, height = size
        width_px, height_px = cm_to_pixel(width), cm_to_pixel(height)
        pin_number = 0
        # 상단 가장자리
        for i in range(0, width_px, margin_px):
            if i < width_px:  # 캔버스 경계를 넘지 않도록
                pin_number += 1
                cv2.circle(canvas, (i, margin_px), 2, (0, 0, 255), -1)
                cv2.putText(canvas, str(pin_number), (i, margin_px + 15), font, font_scale, font_color, thickness)
                pins.append({"number": pin_number, "x": i, "y": margin_px})
        # 우측 가장자리
        for i in range(margin_px, height_px, margin_px):
            if i < height_px:  # 캔버스 경계를 넘지 않도록
                pin_number += 1
                cv2.circle(canvas, (width_px - margin_px, i), 2, (0, 0, 255), -1)
                cv2.putText(canvas, str(pin_number), (width_px - margin_px + 15, i), font, font_scale, font_color, thickness)
                pins.append({"number": pin_number, "x": width_px - margin_px, "y": i})
        # 하단 가장자리 (역순)
        for i in range(width_px - margin_px, -1, -margin_px):
            if i >= 0:  # 캔버스 경계를 넘지 않도록
                pin_number += 1
                cv2.circle(canvas, (i, height_px - margin_px), 2, (0, 0, 255), -1)
                cv2.putText(canvas, str(pin_number), (i, height_px - margin_px + 15), font, font_scale, font_color, thickness)
                pins.append({"number": pin_number, "x": i, "y": height_px - margin_px})
        # 좌측 가장자리 (역순)
        for i in range(height_px - margin_px, margin_px, -margin_px):
            if i >= 0:  # 캔버스 경계를 넘지 않도록
                pin_number += 1
                cv2.circle(canvas, (margin_px, i), 2, (0, 0, 255), -1)
                cv2.putText(canvas, str(pin_number), (margin_px + 15, i), font, font_scale, font_color, thickness)
                pins.append({"number": pin_number, "x": margin_px, "y": i})
    elif canvas_type == "정삼각형":
        side = size
        side_px = cm_to_pixel(side)
        height_px = int(side_px * math.sqrt(3) / 2)
        pin_number = 0
        # 각 변에 점 표시
        # 하단 변
        for i in range(0, side_px, margin_px):
            if i < side_px:  # 캔버스 경계를 넘지 않도록
                pin_number += 1
                cv2.circle(canvas, (i, height_px), 2, (0, 0, 255), -1)
                cv2.putText(canvas, str(pin_number), (i, height_px + 15), font, font_scale, font_color, thickness)
                pins.append({"number": pin_number, "x": i, "y": height_px})
        # 우측 변
        right_count = int((side_px / 2) / margin_px)
        for i in range(right_count):
            x = side_px - i * margin_px * math.cos(math.radians(30))
            y = height_px - i * margin_px * math.sin(math.radians(30))
            if 0 <= x < canvas.shape[1] and 0 <= y < canvas.shape[0]:  # 캔버스 경계를 넘지 않도록
                pin_number += 1
                cv2.circle(canvas, (int(x), int(y)), 2, (0, 0, 255), -1)
                cv2.putText(canvas, str(pin_number), (int(x) + 15, int(y)), font, font_scale, font_color, thickness)
                pins.append({"number": pin_number, "x": int(x), "y": int(y)})
        # 좌측 변
        left_count = int((side_px / 2) / margin_px)
        for i in range(left_count):
            x = i * margin_px * math.cos(math.radians(30))
            y = height_px - i * margin_px * math.sin(math.radians(30))
            if 0 <= x < canvas.shape[1] and 0 <= y < canvas.shape[0]:  # 캔버스 경계를 넘지 않도록
                pin_number += 1
                cv2.circle(canvas, (int(x), int(y)), 2, (0, 0, 255), -1)
                cv2.putText(canvas, str(pin_number), (int(x) - 15, int(y)), font, font_scale, font_color, thickness)
                pins.append({"number": pin_number, "x": int(x), "y": int(y)})
    elif canvas_type == "동그라미":
        radius = size
        radius_px = cm_to_pixel(radius)
        center = (radius_px, radius_px)
        diameter = radius_px * 2
        pin_number = 0
        
        # 최소 핀 수 보장 (최소 8개)
        min_pins = 8
        circumference = 2 * math.pi * radius_px
        pins_count = max(min_pins, int(circumference / margin_px))
        
        # 각도 간격 계산 (0이 되지 않도록 보장)
        angle_step = 360 / pins_count
        
        # 원주에 점 표시
        for angle in range(0, 360, int(angle_step)):
            pin_number += 1
            x = int(center[0] + radius_px * math.cos(math.radians(angle)))
            y = int(center[1] + radius_px * math.sin(math.radians(angle)))
            
            # 캔버스 경계 내에 있는지 확인
            if 0 <= x < canvas.shape[1] and 0 <= y < canvas.shape[0]:
                cv2.circle(canvas, (x, y), 2, (0, 0, 255), -1)
                cv2.putText(canvas, str(pin_number), (x + 1, y + 1), font, font_scale, font_color, thickness)
                pins.append({"number": pin_number, "x": x, "y": y})
    
    return canvas, pins

def process_image(image_array, block_size=10, levels=50, save_data=False, 
                 json_output_path=None, png_output_path=None, excel_output_path=None, 
                 pins=None, time_limit=300, max_iterations=1000, priority_regions=None):
    """
    주어진 이미지 배열에 대해 처리 단계를 실행합니다.
    """
    # Step 1: 원본 이미지 시각화 (RGB)
    fig, ax = plt.subplots(figsize=(8, 8))
    ax.imshow(image_array)
    ax.set_title("Original Image")
    plt.axis('off')
    plt.show()
    
    # Step 2: 엣지 강화 및 회색조 변환
    if len(image_array.shape) == 3:
        gray = cv2.cvtColor(image_array, cv2.COLOR_RGB2GRAY)
    else:
        gray = image_array.copy()
    
    # 엣지 강화 적용
    enhanced_gray = enhance_edges(gray, sigma=1.5, alpha=2.0)  # 더 강한 엣지 효과
    
    # 엣지 강화된 이미지 시각화
    fig, ax = plt.subplots(figsize=(8, 8))
    ax.imshow(enhanced_gray, cmap='gray')
    ax.set_title("Edge Enhanced Image")
    plt.axis('off')
    plt.show()
    
    # 이후의 처리는 enhanced_gray를 사용
    gray = enhanced_gray
    
    # Step 3: 그리드 오버레이가 있는 회색조 이미지 시각화
    h, w = gray.shape
    fig, ax = plt.subplots(figsize=(8, 8))
    ax.imshow(gray, cmap='gray')
    for i in range(0, h, block_size):
        if i < h:  # 캔버스 경계를 넘지 않도록
            ax.axhline(i, color='yellow', linestyle='--', linewidth=0.5)
    for j in range(0, w, block_size):
        if j < w:  # 캔버스 경계를 넘지 않도록
            ax.axvline(j, color='yellow', linestyle='--', linewidth=0.5)
    ax.set_title("Grayscale Image with Grid Overlay")
    plt.axis('off')
    plt.show()
    
    # Step 4: 수정된 부분 - 이미지 영역에만 value 값 계산
    rows = math.ceil(h / block_size)
    cols = math.ceil(w / block_size)
    processed_image = np.zeros((rows, cols), dtype=int)
    
    # 이미지 중심 좌표 계산
    img_center_x = w // 2
    img_center_y = h // 2
    img_width = image_array.shape[1]
    img_height = image_array.shape[0]
    img_start_x = img_center_x - img_width // 2
    img_start_y = img_center_y - img_height // 2
    
    # 이미지 영역만 value 값 계산
    for i in range(0, h, block_size):
        for j in range(0, w, block_size):
            # 이미지 영역 내부인지 확인
            if (img_start_x <= j < img_start_x + img_width and 
                img_start_y <= i < img_start_y + img_height):
                # 이미지 좌표계로 변환
                img_x = j - img_start_x
                img_y = i - img_start_y
                block = gray[img_y:min(img_y+block_size, img_height), 
                           img_x:min(img_x+block_size, img_width)]
                if block.size > 0:  # 유효한 블록인 경우만
                    avg_gray = np.mean(block)
                    # 가장 밝은 부분을 0, 가장 어두운 부분을 levels-1로 매핑
                    level = int((255 - avg_gray) / 255 * (levels - 1))
                    processed_image[i // block_size, j // block_size] = level
            
    # Step 5: 최종 주석(숫자) 이미지 시각화 및 블록 정보 수집
    fig, ax = plt.subplots(figsize=(8, 8))
    ax.imshow(gray, cmap='gray')
    block_info = []  # 각 블록의 정보 저장 리스트
    for i in range(rows):
        for j in range(cols):
            # 각 블록의 중앙 좌표 계산 (마지막 블록은 실제 크기가 작을 수 있음)
            x_center = j * block_size + block_size // 2
            y_center = i * block_size + block_size // 2
            value = int(processed_image[i, j])
            ax.text(x_center, y_center, str(value),
                    color='red', fontsize=8, ha='center', va='center')
            block_info.append({
                "row": i,
                "col": j,
                "center_x": int(x_center),
                "center_y": int(y_center),
                "value": value
            })
    # 그리드 선 추가
    for i in range(0, h, block_size):
        if i < h:  # 캔버스 경계를 넘지 않도록
            ax.axhline(i, color='yellow', linestyle='--', linewidth=0.5)
    for j in range(0, w, block_size):
        if j < w:  # 캔버스 경계를 넘지 않도록
            ax.axvline(j, color='yellow', linestyle='--', linewidth=0.5)
    ax.set_title("Final Annotated Image")
    plt.axis('off')
    
    # 그리드 셀별 value 맵 생성 (center_x, center_y) -> value
    grid_value_map = {(cell["center_x"], cell["center_y"]): cell["value"] for cell in block_info}
    
    # 실의 경로 계산 (핀이 제공된 경우)
    if pins:
        print("실 경로 계산을 시작합니다...")
        grid_cells = []
        for block in block_info:
            if block["value"] > 0:  # 값이 있는 셀만 처리
                grid_cells.append({
                    "center_x": block["center_x"],
                    "center_y": block["center_y"],
                    "value": block["value"],
                    "crossings": 0  # 실이 통과한 횟수 카운트
                })
        
        # priority_regions가 제공된 경우에만 우선순위 할당
        if priority_regions:
            grid_cells = assign_priority_to_cells(grid_cells, priority_regions)
        string_route = calculate_string_route_with_time_limit(pins, grid_cells, block_size, grid_value_map, time_limit, max_iterations)
        

        # 사용자에게 예측 반복 횟수 출력
        estimated_iters = estimate_required_iterations(grid_cells)
        print(f"🔍 예상 반복 횟수: 약 {estimated_iters}회 (최대 {max_iterations}회 중)")

        # 실 경로 시각화 (원본 이미지와 함께)
        fig, ax = plt.subplots(figsize=(10, 10))
        ax.imshow(gray, cmap='gray')
        
        # 핀 위치 표시 (value 값에 영향을 주지 않음)
        for pin in pins:
            ax.plot(pin["x"], pin["y"], 'ro', markersize=5)
            ax.text(pin["x"], pin["y"], str(pin["number"]), fontsize=8, color='white')
        
        # 실 경로 그리기
        for i in range(1, len(string_route)):
            # 오류 수정: StopIteration 방지를 위한 예외 처리 추가
            try:
                prev_pin = next((p for p in pins if p["number"] == string_route[i-1]), None)
                curr_pin = next((p for p in pins if p["number"] == string_route[i]), None)
                
                # 두 핀이 모두 존재하는 경우에만 선 그리기
                if prev_pin and curr_pin:
                    ax.plot([prev_pin["x"], curr_pin["x"]], [prev_pin["y"], curr_pin["y"]], 'b-', linewidth=0.5, alpha=0.5)
                else:
                    print(f"경고: 핀 번호 {string_route[i-1]} 또는 {string_route[i]}을(를) 찾을 수 없습니다.")
            except Exception as e:
                print(f"실 경로 그리기 중 오류 발생: {e}")
                print(f"현재 i: {i}, 경로 길이: {len(string_route)}")
                print(f"현재 핀 번호: {string_route[i-1]} -> {string_route[i]}")
                continue
        
        ax.set_title("String Art Route with Image")
        plt.axis('off')
        plt.show()
        
        # 새로 추가: 실의 경로만 시각화 (원본 이미지 없이)
        fig_only_string, ax_only_string = plt.subplots(figsize=(10, 10))
        # 흰색 배경 생성
        h, w = gray.shape
        white_background = np.ones((h, w, 3), dtype=np.uint8) * 255
        ax_only_string.imshow(white_background)
        
        # 핀 위치 표시 (value 값에 영향을 주지 않음)
        for pin in pins:
            ax_only_string.plot(pin["x"], pin["y"], 'ro', markersize=5)
            ax_only_string.text(pin["x"], pin["y"], str(pin["number"]), fontsize=8, color='black')
        
        # 실 경로 그리기 (더 진한 색상과 두께로)
        for i in range(1, len(string_route)):
            try:
                prev_pin = next((p for p in pins if p["number"] == string_route[i-1]), None)
                curr_pin = next((p for p in pins if p["number"] == string_route[i]), None)
                
                if prev_pin and curr_pin:
                    ax_only_string.plot([prev_pin["x"], curr_pin["x"]], [prev_pin["y"], curr_pin["y"]], 'k-', linewidth=0.7)
                else:
                    print(f"경고: 핀 번호 {string_route[i-1]} 또는 {string_route[i]}을(를) 찾을 수 없습니다.")
            except Exception as e:
                print(f"실 경로 그리기 중 오류 발생: {e}")
                continue
        
        ax_only_string.set_title("String Art Route Only")
        plt.axis('off')
        plt.show()
        
        # 실의 경로만 있는 이미지 저장
        if save_data:
            only_string_png_path = ask_save_path(
                title="실 경로만 있는 이미지(PNG) 저장 경로 선택",
                defaultextension=".png",
                filetypes=[("PNG Files", "*.png")]
            )
            if only_string_png_path:
                # 출력 디렉토리가 있다면 생성
                dir_only_string = os.path.dirname(only_string_png_path)
                if dir_only_string:
                    os.makedirs(dir_only_string, exist_ok=True)
                fig_only_string.savefig(only_string_png_path, bbox_inches='tight', dpi=300)
                print("String art route only image saved as:", only_string_png_path)
            else:
                print("실 경로만 있는 이미지 저장 경로가 선택되지 않았습니다. 저장하지 않습니다.")
        
        # 엑셀 파일로 경로 저장
        if save_data:
            if not excel_output_path:
                excel_output_path = ask_save_path(
                    title="실 경로 데이터(Excel) 저장 경로 선택",
                    defaultextension=".xlsx",
                    filetypes=[("Excel Files", "*.xlsx")]
                )
            if excel_output_path:
                dir_excel = os.path.dirname(excel_output_path)
                if dir_excel:
                    os.makedirs(dir_excel, exist_ok=True)
                    
                # 핀-핀 이동 경로를 저장할 데이터프레임 생성
                route_data = []
                for i in range(1, len(string_route)):
                    route_data.append({
                        "단계": i,
                        "시작 핀": string_route[i-1],
                        "도착 핀": string_route[i]
                    })
                route_df = pd.DataFrame(route_data)
                
                # 엑셀 파일로 저장
                with pd.ExcelWriter(excel_output_path) as writer:
                    route_df.to_excel(writer, sheet_name='핀 경로', index=False)
                    
                    # 추가 시트: 각 핀의 좌표 정보
                    pins_df = pd.DataFrame([{
                        "핀 번호": pin["number"],
                        "X 좌표": pin["x"],
                        "Y 좌표": pin["y"]
                    } for pin in pins])
                    pins_df.to_excel(writer, sheet_name='핀 좌표', index=False)
                    
                    # 추가 시트: 그리드 셀 정보
                    grid_df = pd.DataFrame([{
                        "행": block["row"],
                        "열": block["col"],
                        "중심 X": block["center_x"],
                        "중심 Y": block["center_y"],
                        "필요 교차 수": block["value"]
                    } for block in block_info])
                    grid_df.to_excel(writer, sheet_name='그리드 정보', index=False)
                
                print("실 경로 데이터가 저장되었습니다:", excel_output_path)
            else:
                print("Excel 파일 저장 경로가 선택되지 않았습니다. 저장하지 않습니다.")
    
    # 만약 저장 옵션이 활성화되었다면, 사용자가 직접 저장 경로를 선택합니다.
    if save_data:
        if not png_output_path:
            png_output_path = ask_save_path(
                title="주석 이미지(PNG) 저장 경로 선택",
                defaultextension=".png",
                filetypes=[("PNG Files", "*.png")]
            )
        if png_output_path:
            # 출력 디렉토리가 있다면 생성
            dir_png = os.path.dirname(png_output_path)
            if dir_png:
                os.makedirs(dir_png, exist_ok=True)
            fig.savefig(png_output_path, bbox_inches='tight')
            print("Final annotated image saved as:", png_output_path)
        else:
            print("PNG 파일 저장 경로가 선택되지 않았습니다. 저장하지 않습니다.")
    
    plt.show()
    
    if save_data:
        if not json_output_path:
            json_output_path = ask_save_path(
                title="블록 데이터(JSON) 저장 경로 선택",
                defaultextension=".json",
                filetypes=[("JSON Files", "*.json")]
            )
        if json_output_path:
            dir_json = os.path.dirname(json_output_path)
            if dir_json:
                os.makedirs(dir_json, exist_ok=True)
            data = {
                "block_size": block_size,
                "levels": levels,
                "image_width": w,
                "image_height": h,
                "rows": rows,
                "cols": cols,
                "blocks": block_info
            }
            with open(json_output_path, "w", encoding="utf-8") as f:
                json.dump(data, f, indent=4)
            print("Block numeric data saved as:", json_output_path)
        else:
            print("JSON 파일 저장 경로가 선택되지 않았습니다. 저장하지 않습니다.")

    if save_data:
        # 설정 정보를 저장할 데이터 구조
        settings_info = {
            "블록 크기": block_size,
            "명암 단계": levels,
            "시간 제한(초)": time_limit,
            "최대 반복 횟수": max_iterations,
            "핀 개수": len(pins) if pins else 0,
            "이미지 크기": f"{image_array.shape[1]}x{image_array.shape[0]}"
        }

        # Excel 파일에 설정 정보 시트 추가
        if excel_output_path:
            with pd.ExcelWriter(excel_output_path, engine='openpyxl', mode='a' if os.path.exists(excel_output_path) else 'w') as writer:
                settings_df = pd.DataFrame([settings_info])
                settings_df.to_excel(writer, sheet_name='설정 정보', index=False)

        # JSON 파일에 설정 정보 추가
        if json_output_path:
            data["settings"] = settings_info
            with open(json_output_path, "w", encoding="utf-8") as f:
                json.dump(data, f, indent=4, ensure_ascii=False)

        # 설정 정보를 별도의 텍스트 파일로 저장
        settings_txt_path = os.path.join(os.path.dirname(png_output_path if png_output_path else excel_output_path), "settings.txt")
        with open(settings_txt_path, "w", encoding="utf-8") as f:
            f.write("=== 설정 정보 ===\n")
            for key, value in settings_info.items():
                f.write(f"{key}: {value}\n")

    # 설정 정보를 그래프에 표시
    fig.text(0.02, 0.02, f"설정: 블록={block_size}, 명암={levels}, 시간제한={time_limit}초, 최대반복={max_iterations}", 
             fontsize=8, ha='left')

def calculate_string_route_with_time_limit(pins, grid_cells, block_size, grid_value_map, 
                                         time_limit=300, max_iterations=1000):
    start_time = time.time()
    
    # 이미지 영역의 셀만 필터링
    image_cells = {pos: value for pos, value in grid_value_map.items() if value > 0}
    total_initial_value = sum(image_cells.values())
    
    print("\n작업 시작...")
    print(f"초기 총 value: {total_initial_value}")
    
    # 핀 쌍별 교차 그리드 셀과 점수 계산
    print("\n핀 쌍별 교차 그리드 셀과 점수 계산 중...")
    pin_pairs_info = {}
    
    # 모든 가능한 핀 쌍의 점수 계산
    total_pairs = len(list(combinations(pins, 2)))
    for idx, (p1, p2) in enumerate(combinations(pins, 2), 1):
        if time.time() - start_time > time_limit * 0.3:
            break
            
        # 진행률 표시
        progress = (idx / total_pairs) * 100
        print(f"\r전처리 진행률: {progress:.1f}% ({idx}/{total_pairs})", end="")
        
        crossings = line_crosses_grid_cells(p1, p2, grid_cells, block_size)
        score = sum(image_cells.get(cell_pos, 0) 
                   for cell_pos in crossings if cell_pos in image_cells)

        pin_pairs_info[(p1["number"], p2["number"])] = {
            "crossings": crossings,
            "score": score
        }
        pin_pairs_info[(p2["number"], p1["number"])] = {
            "crossings": crossings,
            "score": score
        }
    
    print("\n\n경로 계산 시작...")
    
    best_start_pair = max(pin_pairs_info.items(), key=lambda x: x[1]["score"])
    string_route = list(best_start_pair[0])
    
    remaining_values = image_cells.copy()
    
    # 첫 경로의 교차점 처리
    for cell_pos in best_start_pair[1]["crossings"]:
        if cell_pos in remaining_values:
            remaining_values[cell_pos] = max(0, remaining_values[cell_pos] - 1)
    
    # 현재 진행 상황 계산
    current_total = sum(remaining_values.values())
    progress = ((total_initial_value - current_total) / total_initial_value) * 100
    
    # 반복적으로 다음 경로 선택
    iteration = 0
    last_update_time = time.time()
    update_interval = 1.0  # 1초마다 진행률 업데이트
    
    while iteration < max_iterations:
        current_time = time.time()
        elapsed_time = current_time - start_time
        
        if elapsed_time > time_limit:
            print("\n시간 제한 도달")
            break
        
        iteration += 1
        current_pin = string_route[-1]
        
        # 주기적으로 진행률 업데이트
        if current_time - last_update_time >= update_interval:
            current_total = sum(remaining_values.values())
            progress = ((total_initial_value - current_total) / total_initial_value) * 100
            
            # 예상 남은 시간 계산
            if progress > 0:
                estimated_total_time = (elapsed_time / progress) * 100
                remaining_time = estimated_total_time - elapsed_time
                print(f"\r진행률: {progress:.1f}% | 반복: {iteration}/{max_iterations} | "
                      f"경과: {int(elapsed_time)}초 | 예상 남은 시간: {int(remaining_time)}초", end="")
            
            last_update_time = current_time
        
        # 다음 최적 핀 선택 로직 수정
        best_score = -float('inf')
        best_next_pin = None

        for pin in pins:
            next_pin = pin["number"]
            if next_pin in string_route[-2:]:  # 직전 핀으로는 돌아가지 않음
                continue
                
            pair_key = (current_pin, next_pin)
            if pair_key not in pin_pairs_info:
                continue
                
            crossings = pin_pairs_info[pair_key]["crossings"]
            current_score = sum(remaining_values.get(cell_pos, 0) 
                              for cell_pos in crossings if cell_pos in remaining_values)
            
            # 점수가 0이더라도 다른 조건을 고려
            if current_score >= 0:  # 0이상이면 일단 후보로 고려
                # 이전에 방문하지 않은 핀을 선호
                if next_pin not in string_route:
                    current_score += 0.1  # 약간의 보너스 점수
            
            if current_score > best_score:
                best_score = current_score
                best_next_pin = next_pin

        # 모든 핀을 사용했거나 정말로 개선이 불가능한 경우만 종료
        if best_next_pin is None:
            print("\n더 이상 개선할 수 없는 경로 없음")
            break
            
        string_route.append(best_next_pin)
        
        pair_key = (current_pin, best_next_pin)
        crossings = pin_pairs_info[pair_key]["crossings"]
        for cell_pos in crossings:
            if cell_pos in remaining_values:
                remaining_values[cell_pos] = max(0, remaining_values[cell_pos] - 1)
        
        if all(value == 0 for value in remaining_values.values()):
            print("\n모든 셀의 value가 0에 도달")
            break
    
    # 최종 결과 출력
    final_total = sum(remaining_values.values())
    final_progress = ((total_initial_value - final_total) / total_initial_value) * 100
    print(f"\n\n작업 완료:")
    print(f"최종 진행률: {final_progress:.1f}%")
    print(f"총 소요 시간: {int(time.time() - start_time)}초")
    print(f"총 반복 횟수: {iteration}")
    
    return string_route

def line_crosses_grid_cells(p1, p2, grid_cells, block_size):
    """
    두 핀 사이의 선이 교차하는 그리드 셀을 계산합니다.
    
    Args:
        p1: 첫 번째 핀 정보
        p2: 두 번째 핀 정보
        grid_cells: 그리드 셀 정보 리스트
        block_size: 그리드 셀 크기
    
    Returns:
        교차하는 그리드 셀 좌표 집합 (center_x, center_y)
    """
    x1, y1 = p1["x"], p1["y"]
    x2, y2 = p2["x"], p2["y"]
    
    # 두 점 사이의 거리
    distance = math.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2)
    
    # 직선을 따라 충분히 많은 점을 샘플링하여 교차하는 셀 확인
    # block_size의 1/4 간격으로 샘플링 (정밀도 향상)
    step_size = block_size / 4
    steps = max(1, int(distance / step_size))
    
    crossing_cells = set()
    
    for i in range(steps + 1):
        # 선형 보간을 통해 직선 위의 점 계산
        t = i / steps if steps > 0 else 0
        x = x1 + t * (x2 - x1)
        y = y1 + t * (y2 - y1)
        
        # 이 점이 속한 그리드 셀 찾기
        for cell in grid_cells:
            # 셀의 중심 좌표
            cx, cy = cell["center_x"], cell["center_y"]
            
            # 점이 셀 내부에 있는지 확인 (셀을 block_size x block_size 정사각형으로 가정)
            half_block = block_size / 2
            if cx - half_block <= x <= cx + half_block and cy - half_block <= y <= cy + half_block:
                # 셀 중심 좌표를 키로 사용하여 세트에 추가
                crossing_cells.add((cx, cy))
                break
    
    return crossing_cells


def assign_priority_to_cells(grid_cells, priority_regions):
    """
    grid_cells에 각 셀이 우선순위 영역 안에 있는지 여부를 기준으로 priority 값을 할당합니다.
    가장 먼저 지정한 영역이 우선순위 0 (가장 높음), 그다음은 1, 2...
    영역에 포함되지 않은 셀은 가장 낮은 우선순위로 간주합니다.
    """
    for cell in grid_cells:
        cx, cy = cell["center_x"], cell["center_y"]
        assigned = False
        for idx, ((x1, y1), (x2, y2)) in enumerate(priority_regions):
            if x1 <= cx <= x2 and y1 <= cy <= y2:
                cell["priority"] = idx  # 낮은 idx가 높은 우선순위
                assigned = True
                break
        if not assigned:
            cell["priority"] = len(priority_regions)  # 마지막 순위
    return grid_cells

def enhance_edges(image, sigma=1.0, alpha=1.5):
    """
    이미지의 엣지를 강화하는 함수
    
    Args:
        image: 입력 이미지 (grayscale)
        sigma: 가우시안 블러 강도 (기본값: 1.0)
        alpha: 엣지 강화 강도 (기본값: 1.5)
    
    Returns:
        엣지가 강화된 이미지
    """
    # 가우시안 블러 적용
    blurred = cv2.GaussianBlur(image, (0, 0), sigma)
    
    # Sobel 엣지 검출
    grad_x = cv2.Sobel(blurred, cv2.CV_64F, 1, 0, ksize=3)
    grad_y = cv2.Sobel(blurred, cv2.CV_64F, 0, 1, ksize=3)
    
    # 그래디언트 강도 계산
    gradient = np.sqrt(grad_x**2 + grad_y**2)
    
    # 그래디언트 정규화 (0-255 범위로)
    gradient = cv2.normalize(gradient, None, 0, 255, cv2.NORM_MINMAX)
    
    # 원본 이미지와 엣지를 결합
    enhanced = cv2.addWeighted(image.astype(float), 1.0, gradient, alpha, 0)
    
    # 픽셀값을 0-255 범위로 클리핑
    enhanced = np.clip(enhanced, 0, 255).astype(np.uint8)
    
    return enhanced

def main():
    """
    메인 함수: 사용자로부터 이미지를 선택받고 처리합니다.
    """
    root = tk.Tk()
    root.withdraw()  # 루트 창 숨김
    file_path = filedialog.askopenfilename(title="이미지 파일 선택",
                                          filetypes=[("이미지 파일", "*.jpg;*.jpeg;*.png;*.bmp")])
    if not file_path:
        print("파일이 선택되지 않았습니다.")
        return
    
    # 처리 옵션 선택
    block_size = ask_block_size()
    if not block_size:
        block_size = 10  # 기본값
    
    levels = ask_levels()
    if not levels:
        levels = 50  # 기본값
    
    # 캔버스 타입 선택
    canvas_type = ask_canvas_type()
    if not canvas_type or canvas_type not in ["사각형", "정삼각형", "동그라미"]:
        print("유효하지 않은 캔버스 타입입니다. 기본값인 '동그라미'로 설정합니다.")
        canvas_type = "동그라미"
    
    # 이미지 읽기
    image = cv2.imread(file_path)
    if image is None:
        print("이미지를 읽을 수 없습니다.")
        return
    
    # BGR에서 RGB로 변환
    image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    
    priority_regions = select_priority_regions(image)
    print(f"선택된 우선순위 영역 수: {len(priority_regions)}")
    # 핀 간격 선택
    margin = ask_margin()
    if not margin:
        margin = 0.2  # 기본값 0.2cm
    
    # 캔버스 크기 선택 (이미지와 마진 정보 전달)
    canvas_size = ask_canvas_size(canvas_type, image, margin)
    if not canvas_size:
        if canvas_type == "사각형":
            canvas_size = (10, 10)  # 기본값 10cm x 10cm
        else:
            canvas_size = 10  # 기본 반지름 또는 변 길이 10cm
    
    # 이미지 크기 조정 비율 선택
    scale = ask_image_scale()
    if not scale:
        scale = 1.0  # 기본값 100%
    
    # 시간 제한 선택
    time_limit = ask_time_limit()
    if not time_limit:
        time_limit = 300  # 기본값 300초(5분)
    
    # 최대 반복 횟수 선택
    max_iterations = ask_max_iterations()
    if not max_iterations:
        max_iterations = 1000  # 기본값 1000회
    
    # 이미지 크기 조정
    image = resize_image(image, scale)
    
    # 캔버스 생성
    canvas = create_canvas(canvas_type, canvas_size)
    if canvas is None:
        print("캔버스를 생성할 수 없습니다.")
        return
    
    # 캔버스에 번호가 붙은 점 추가 및 핀 좌표 얻기
    canvas_with_pins, pins = draw_numbered_points(canvas, canvas_type, canvas_size, margin, dpi=300)
    
    # 이미지를 캔버스 중앙에 배치
    canvas_with_image = place_image_on_canvas(canvas_with_pins.copy(), image, canvas_type)
    
    # 이미지 처리 및 결과 저장
    save_data = messagebox.askyesno("데이터 저장", "처리 결과를 파일로 저장하시겠습니까?")
    process_image(canvas_with_image, block_size, levels, save_data, 
                 pins=pins, time_limit=time_limit, max_iterations=max_iterations,
                 priority_regions=priority_regions)  # priority_regions 추가

if __name__ == "__main__":
    main()