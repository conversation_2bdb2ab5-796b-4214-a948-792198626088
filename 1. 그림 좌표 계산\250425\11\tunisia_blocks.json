{"final_blocks": [{"row": 0, "col": 14, "center_x": 145, "center_y": 5, "value": 8, "remaining": 8, "crossings": 0}, {"row": 0, "col": 15, "center_x": 155, "center_y": 5, "value": 8, "remaining": 8, "crossings": 0}, {"row": 0, "col": 16, "center_x": 165, "center_y": 5, "value": 6, "remaining": 6, "crossings": 0}, {"row": 0, "col": 17, "center_x": 175, "center_y": 5, "value": 6, "remaining": 6, "crossings": 0}, {"row": 0, "col": 18, "center_x": 185, "center_y": 5, "value": 1, "remaining": 1, "crossings": 0}, {"row": 0, "col": 19, "center_x": 195, "center_y": 5, "value": 3, "remaining": 3, "crossings": 0}, {"row": 0, "col": 20, "center_x": 205, "center_y": 5, "value": 1, "remaining": 1, "crossings": 0}, {"row": 0, "col": 21, "center_x": 215, "center_y": 5, "value": 4, "remaining": 4, "crossings": 0}, {"row": 0, "col": 23, "center_x": 235, "center_y": 5, "value": 4, "remaining": 4, "crossings": 0}, {"row": 0, "col": 25, "center_x": 255, "center_y": 5, "value": 3, "remaining": 3, "crossings": 0}, {"row": 0, "col": 26, "center_x": 265, "center_y": 5, "value": 1, "remaining": 1, "crossings": 0}, {"row": 0, "col": 28, "center_x": 285, "center_y": 5, "value": 3, "remaining": 3, "crossings": 0}, {"row": 0, "col": 30, "center_x": 305, "center_y": 5, "value": 1, "remaining": 1, "crossings": 0}, {"row": 0, "col": 31, "center_x": 315, "center_y": 5, "value": 6, "remaining": 6, "crossings": 0}, {"row": 0, "col": 33, "center_x": 335, "center_y": 5, "value": 8, "remaining": 8, "crossings": 0}, {"row": 1, "col": 12, "center_x": 125, "center_y": 15, "value": 9, "remaining": 9, "crossings": 0}, {"row": 1, "col": 13, "center_x": 135, "center_y": 15, "value": 5, "remaining": 5, "crossings": 0}, {"row": 1, "col": 14, "center_x": 145, "center_y": 15, "value": 6, "remaining": 6, "crossings": 0}, {"row": 1, "col": 15, "center_x": 155, "center_y": 15, "value": 6, "remaining": 6, "crossings": 0}, {"row": 1, "col": 16, "center_x": 165, "center_y": 15, "value": 4, "remaining": 4, "crossings": 0}, {"row": 1, "col": 19, "center_x": 195, "center_y": 15, "value": 1, "remaining": 1, "crossings": 0}, {"row": 1, "col": 28, "center_x": 285, "center_y": 15, "value": 1, "remaining": 1, "crossings": 0}, {"row": 1, "col": 30, "center_x": 305, "center_y": 15, "value": 4, "remaining": 4, "crossings": 0}, {"row": 1, "col": 33, "center_x": 335, "center_y": 15, "value": 5, "remaining": 5, "crossings": 0}, {"row": 1, "col": 35, "center_x": 355, "center_y": 15, "value": 6, "remaining": 6, "crossings": 0}, {"row": 2, "col": 10, "center_x": 105, "center_y": 25, "value": 4, "remaining": 4, "crossings": 0}, {"row": 2, "col": 11, "center_x": 115, "center_y": 25, "value": 4, "remaining": 4, "crossings": 0}, {"row": 2, "col": 12, "center_x": 125, "center_y": 25, "value": 6, "remaining": 6, "crossings": 0}, {"row": 2, "col": 13, "center_x": 135, "center_y": 25, "value": 7, "remaining": 7, "crossings": 0}, {"row": 2, "col": 14, "center_x": 145, "center_y": 25, "value": 4, "remaining": 4, "crossings": 0}, {"row": 2, "col": 32, "center_x": 325, "center_y": 25, "value": 4, "remaining": 4, "crossings": 0}, {"row": 2, "col": 35, "center_x": 355, "center_y": 25, "value": 8, "remaining": 8, "crossings": 0}, {"row": 2, "col": 37, "center_x": 375, "center_y": 25, "value": 5, "remaining": 5, "crossings": 0}, {"row": 3, "col": 10, "center_x": 105, "center_y": 35, "value": 11, "remaining": 11, "crossings": 0}, {"row": 3, "col": 11, "center_x": 115, "center_y": 35, "value": 10, "remaining": 10, "crossings": 0}, {"row": 3, "col": 12, "center_x": 125, "center_y": 35, "value": 4, "remaining": 4, "crossings": 0}, {"row": 3, "col": 34, "center_x": 345, "center_y": 35, "value": 4, "remaining": 4, "crossings": 0}, {"row": 3, "col": 37, "center_x": 375, "center_y": 35, "value": 4, "remaining": 4, "crossings": 0}, {"row": 4, "col": 8, "center_x": 85, "center_y": 45, "value": 10, "remaining": 10, "crossings": 0}, {"row": 4, "col": 9, "center_x": 95, "center_y": 45, "value": 8, "remaining": 8, "crossings": 0}, {"row": 4, "col": 10, "center_x": 105, "center_y": 45, "value": 4, "remaining": 4, "crossings": 0}, {"row": 4, "col": 36, "center_x": 365, "center_y": 45, "value": 4, "remaining": 4, "crossings": 0}, {"row": 4, "col": 39, "center_x": 395, "center_y": 45, "value": 12, "remaining": 12, "crossings": 0}, {"row": 5, "col": 6, "center_x": 65, "center_y": 55, "value": 3, "remaining": 3, "crossings": 0}, {"row": 5, "col": 7, "center_x": 75, "center_y": 55, "value": 2, "remaining": 2, "crossings": 0}, {"row": 5, "col": 8, "center_x": 85, "center_y": 55, "value": 6, "remaining": 6, "crossings": 0}, {"row": 5, "col": 9, "center_x": 95, "center_y": 55, "value": 1, "remaining": 1, "crossings": 0}, {"row": 5, "col": 38, "center_x": 385, "center_y": 55, "value": 3, "remaining": 3, "crossings": 0}, {"row": 5, "col": 39, "center_x": 395, "center_y": 55, "value": 4, "remaining": 4, "crossings": 0}, {"row": 5, "col": 41, "center_x": 415, "center_y": 55, "value": 2, "remaining": 2, "crossings": 0}, {"row": 6, "col": 6, "center_x": 65, "center_y": 65, "value": 9, "remaining": 9, "crossings": 0}, {"row": 6, "col": 7, "center_x": 75, "center_y": 65, "value": 14, "remaining": 14, "crossings": 0}, {"row": 6, "col": 8, "center_x": 85, "center_y": 65, "value": 1, "remaining": 1, "crossings": 0}, {"row": 6, "col": 38, "center_x": 385, "center_y": 65, "value": 1, "remaining": 1, "crossings": 0}, {"row": 6, "col": 40, "center_x": 405, "center_y": 65, "value": 1, "remaining": 1, "crossings": 0}, {"row": 6, "col": 41, "center_x": 415, "center_y": 65, "value": 11, "remaining": 11, "crossings": 0}, {"row": 7, "col": 4, "center_x": 45, "center_y": 75, "value": 2, "remaining": 2, "crossings": 0}, {"row": 7, "col": 5, "center_x": 55, "center_y": 75, "value": 8, "remaining": 8, "crossings": 0}, {"row": 7, "col": 6, "center_x": 65, "center_y": 75, "value": 2, "remaining": 2, "crossings": 0}, {"row": 7, "col": 7, "center_x": 75, "center_y": 75, "value": 3, "remaining": 3, "crossings": 0}, {"row": 7, "col": 40, "center_x": 405, "center_y": 75, "value": 4, "remaining": 4, "crossings": 0}, {"row": 7, "col": 42, "center_x": 425, "center_y": 75, "value": 3, "remaining": 3, "crossings": 0}, {"row": 7, "col": 43, "center_x": 435, "center_y": 75, "value": 5, "remaining": 5, "crossings": 0}, {"row": 8, "col": 4, "center_x": 45, "center_y": 85, "value": 2, "remaining": 2, "crossings": 0}, {"row": 8, "col": 5, "center_x": 55, "center_y": 85, "value": 10, "remaining": 10, "crossings": 0}, {"row": 8, "col": 6, "center_x": 65, "center_y": 85, "value": 4, "remaining": 4, "crossings": 0}, {"row": 8, "col": 10, "center_x": 105, "center_y": 85, "value": 16, "remaining": 16, "crossings": 0}, {"row": 8, "col": 11, "center_x": 115, "center_y": 85, "value": 25, "remaining": 25, "crossings": 0}, {"row": 8, "col": 12, "center_x": 125, "center_y": 85, "value": 24, "remaining": 24, "crossings": 0}, {"row": 8, "col": 13, "center_x": 135, "center_y": 85, "value": 25, "remaining": 25, "crossings": 0}, {"row": 8, "col": 14, "center_x": 145, "center_y": 85, "value": 24, "remaining": 24, "crossings": 0}, {"row": 8, "col": 15, "center_x": 155, "center_y": 85, "value": 24, "remaining": 24, "crossings": 0}, {"row": 8, "col": 16, "center_x": 165, "center_y": 85, "value": 24, "remaining": 24, "crossings": 0}, {"row": 8, "col": 17, "center_x": 175, "center_y": 85, "value": 25, "remaining": 25, "crossings": 0}, {"row": 8, "col": 18, "center_x": 185, "center_y": 85, "value": 24, "remaining": 24, "crossings": 0}, {"row": 8, "col": 19, "center_x": 195, "center_y": 85, "value": 23, "remaining": 23, "crossings": 0}, {"row": 8, "col": 20, "center_x": 205, "center_y": 85, "value": 23, "remaining": 23, "crossings": 0}, {"row": 8, "col": 21, "center_x": 215, "center_y": 85, "value": 22, "remaining": 22, "crossings": 0}, {"row": 8, "col": 22, "center_x": 225, "center_y": 85, "value": 22, "remaining": 22, "crossings": 0}, {"row": 8, "col": 23, "center_x": 235, "center_y": 85, "value": 24, "remaining": 24, "crossings": 0}, {"row": 8, "col": 24, "center_x": 245, "center_y": 85, "value": 25, "remaining": 25, "crossings": 0}, {"row": 8, "col": 25, "center_x": 255, "center_y": 85, "value": 25, "remaining": 25, "crossings": 0}, {"row": 8, "col": 26, "center_x": 265, "center_y": 85, "value": 24, "remaining": 24, "crossings": 0}, {"row": 8, "col": 27, "center_x": 275, "center_y": 85, "value": 24, "remaining": 24, "crossings": 0}, {"row": 8, "col": 28, "center_x": 285, "center_y": 85, "value": 24, "remaining": 24, "crossings": 0}, {"row": 8, "col": 29, "center_x": 295, "center_y": 85, "value": 26, "remaining": 26, "crossings": 0}, {"row": 8, "col": 30, "center_x": 305, "center_y": 85, "value": 22, "remaining": 22, "crossings": 0}, {"row": 8, "col": 31, "center_x": 315, "center_y": 85, "value": 19, "remaining": 19, "crossings": 0}, {"row": 8, "col": 32, "center_x": 325, "center_y": 85, "value": 19, "remaining": 19, "crossings": 0}, {"row": 8, "col": 33, "center_x": 335, "center_y": 85, "value": 24, "remaining": 24, "crossings": 0}, {"row": 8, "col": 34, "center_x": 345, "center_y": 85, "value": 26, "remaining": 26, "crossings": 0}, {"row": 8, "col": 35, "center_x": 355, "center_y": 85, "value": 24, "remaining": 24, "crossings": 0}, {"row": 8, "col": 36, "center_x": 365, "center_y": 85, "value": 20, "remaining": 20, "crossings": 0}, {"row": 8, "col": 42, "center_x": 425, "center_y": 85, "value": 3, "remaining": 3, "crossings": 0}, {"row": 8, "col": 43, "center_x": 435, "center_y": 85, "value": 8, "remaining": 8, "crossings": 0}, {"row": 9, "col": 3, "center_x": 35, "center_y": 95, "value": 5, "remaining": 5, "crossings": 0}, {"row": 9, "col": 4, "center_x": 45, "center_y": 95, "value": 3, "remaining": 3, "crossings": 0}, {"row": 9, "col": 5, "center_x": 55, "center_y": 95, "value": 4, "remaining": 4, "crossings": 0}, {"row": 9, "col": 10, "center_x": 105, "center_y": 95, "value": 23, "remaining": 23, "crossings": 0}, {"row": 9, "col": 11, "center_x": 115, "center_y": 95, "value": 36, "remaining": 36, "crossings": 0}, {"row": 9, "col": 12, "center_x": 125, "center_y": 95, "value": 35, "remaining": 35, "crossings": 0}, {"row": 9, "col": 13, "center_x": 135, "center_y": 95, "value": 35, "remaining": 35, "crossings": 0}, {"row": 9, "col": 14, "center_x": 145, "center_y": 95, "value": 35, "remaining": 35, "crossings": 0}, {"row": 9, "col": 15, "center_x": 155, "center_y": 95, "value": 34, "remaining": 34, "crossings": 0}, {"row": 9, "col": 16, "center_x": 165, "center_y": 95, "value": 35, "remaining": 35, "crossings": 0}, {"row": 9, "col": 17, "center_x": 175, "center_y": 95, "value": 35, "remaining": 35, "crossings": 0}, {"row": 9, "col": 18, "center_x": 185, "center_y": 95, "value": 32, "remaining": 32, "crossings": 0}, {"row": 9, "col": 19, "center_x": 195, "center_y": 95, "value": 27, "remaining": 27, "crossings": 0}, {"row": 9, "col": 20, "center_x": 205, "center_y": 95, "value": 23, "remaining": 23, "crossings": 0}, {"row": 9, "col": 21, "center_x": 215, "center_y": 95, "value": 22, "remaining": 22, "crossings": 0}, {"row": 9, "col": 22, "center_x": 225, "center_y": 95, "value": 20, "remaining": 20, "crossings": 0}, {"row": 9, "col": 23, "center_x": 235, "center_y": 95, "value": 20, "remaining": 20, "crossings": 0}, {"row": 9, "col": 24, "center_x": 245, "center_y": 95, "value": 20, "remaining": 20, "crossings": 0}, {"row": 9, "col": 25, "center_x": 255, "center_y": 95, "value": 22, "remaining": 22, "crossings": 0}, {"row": 9, "col": 26, "center_x": 265, "center_y": 95, "value": 27, "remaining": 27, "crossings": 0}, {"row": 9, "col": 27, "center_x": 275, "center_y": 95, "value": 33, "remaining": 33, "crossings": 0}, {"row": 9, "col": 28, "center_x": 285, "center_y": 95, "value": 35, "remaining": 35, "crossings": 0}, {"row": 9, "col": 29, "center_x": 295, "center_y": 95, "value": 38, "remaining": 38, "crossings": 0}, {"row": 9, "col": 30, "center_x": 305, "center_y": 95, "value": 34, "remaining": 34, "crossings": 0}, {"row": 9, "col": 31, "center_x": 315, "center_y": 95, "value": 29, "remaining": 29, "crossings": 0}, {"row": 9, "col": 32, "center_x": 325, "center_y": 95, "value": 28, "remaining": 28, "crossings": 0}, {"row": 9, "col": 33, "center_x": 335, "center_y": 95, "value": 34, "remaining": 34, "crossings": 0}, {"row": 9, "col": 34, "center_x": 345, "center_y": 95, "value": 37, "remaining": 37, "crossings": 0}, {"row": 9, "col": 35, "center_x": 355, "center_y": 95, "value": 35, "remaining": 35, "crossings": 0}, {"row": 9, "col": 36, "center_x": 365, "center_y": 95, "value": 29, "remaining": 29, "crossings": 0}, {"row": 9, "col": 41, "center_x": 415, "center_y": 95, "value": 4, "remaining": 4, "crossings": 0}, {"row": 9, "col": 44, "center_x": 445, "center_y": 95, "value": 4, "remaining": 4, "crossings": 0}, {"row": 9, "col": 45, "center_x": 455, "center_y": 95, "value": 2, "remaining": 2, "crossings": 0}, {"row": 10, "col": 3, "center_x": 35, "center_y": 105, "value": 6, "remaining": 6, "crossings": 0}, {"row": 10, "col": 4, "center_x": 45, "center_y": 105, "value": 11, "remaining": 11, "crossings": 0}, {"row": 10, "col": 10, "center_x": 105, "center_y": 105, "value": 23, "remaining": 23, "crossings": 0}, {"row": 10, "col": 11, "center_x": 115, "center_y": 105, "value": 36, "remaining": 36, "crossings": 0}, {"row": 10, "col": 12, "center_x": 125, "center_y": 105, "value": 35, "remaining": 35, "crossings": 0}, {"row": 10, "col": 13, "center_x": 135, "center_y": 105, "value": 35, "remaining": 35, "crossings": 0}, {"row": 10, "col": 14, "center_x": 145, "center_y": 105, "value": 35, "remaining": 35, "crossings": 0}, {"row": 10, "col": 15, "center_x": 155, "center_y": 105, "value": 34, "remaining": 34, "crossings": 0}, {"row": 10, "col": 16, "center_x": 165, "center_y": 105, "value": 33, "remaining": 33, "crossings": 0}, {"row": 10, "col": 17, "center_x": 175, "center_y": 105, "value": 27, "remaining": 27, "crossings": 0}, {"row": 10, "col": 18, "center_x": 185, "center_y": 105, "value": 23, "remaining": 23, "crossings": 0}, {"row": 10, "col": 19, "center_x": 195, "center_y": 105, "value": 22, "remaining": 22, "crossings": 0}, {"row": 10, "col": 20, "center_x": 205, "center_y": 105, "value": 22, "remaining": 22, "crossings": 0}, {"row": 10, "col": 21, "center_x": 215, "center_y": 105, "value": 22, "remaining": 22, "crossings": 0}, {"row": 10, "col": 22, "center_x": 225, "center_y": 105, "value": 20, "remaining": 20, "crossings": 0}, {"row": 10, "col": 23, "center_x": 235, "center_y": 105, "value": 20, "remaining": 20, "crossings": 0}, {"row": 10, "col": 24, "center_x": 245, "center_y": 105, "value": 18, "remaining": 18, "crossings": 0}, {"row": 10, "col": 25, "center_x": 255, "center_y": 105, "value": 16, "remaining": 16, "crossings": 0}, {"row": 10, "col": 26, "center_x": 265, "center_y": 105, "value": 15, "remaining": 15, "crossings": 0}, {"row": 10, "col": 27, "center_x": 275, "center_y": 105, "value": 17, "remaining": 17, "crossings": 0}, {"row": 10, "col": 28, "center_x": 285, "center_y": 105, "value": 28, "remaining": 28, "crossings": 0}, {"row": 10, "col": 29, "center_x": 295, "center_y": 105, "value": 38, "remaining": 38, "crossings": 0}, {"row": 10, "col": 30, "center_x": 305, "center_y": 105, "value": 37, "remaining": 37, "crossings": 0}, {"row": 10, "col": 31, "center_x": 315, "center_y": 105, "value": 35, "remaining": 35, "crossings": 0}, {"row": 10, "col": 32, "center_x": 325, "center_y": 105, "value": 32, "remaining": 32, "crossings": 0}, {"row": 10, "col": 33, "center_x": 335, "center_y": 105, "value": 35, "remaining": 35, "crossings": 0}, {"row": 10, "col": 34, "center_x": 345, "center_y": 105, "value": 37, "remaining": 37, "crossings": 0}, {"row": 10, "col": 35, "center_x": 355, "center_y": 105, "value": 35, "remaining": 35, "crossings": 0}, {"row": 10, "col": 36, "center_x": 365, "center_y": 105, "value": 29, "remaining": 29, "crossings": 0}, {"row": 10, "col": 43, "center_x": 435, "center_y": 105, "value": 1, "remaining": 1, "crossings": 0}, {"row": 10, "col": 44, "center_x": 445, "center_y": 105, "value": 4, "remaining": 4, "crossings": 0}, {"row": 10, "col": 45, "center_x": 455, "center_y": 105, "value": 3, "remaining": 3, "crossings": 0}, {"row": 11, "col": 2, "center_x": 25, "center_y": 115, "value": 4, "remaining": 4, "crossings": 0}, {"row": 11, "col": 3, "center_x": 35, "center_y": 115, "value": 4, "remaining": 4, "crossings": 0}, {"row": 11, "col": 4, "center_x": 45, "center_y": 115, "value": 2, "remaining": 2, "crossings": 0}, {"row": 11, "col": 10, "center_x": 105, "center_y": 115, "value": 23, "remaining": 23, "crossings": 0}, {"row": 11, "col": 11, "center_x": 115, "center_y": 115, "value": 36, "remaining": 36, "crossings": 0}, {"row": 11, "col": 12, "center_x": 125, "center_y": 115, "value": 36, "remaining": 36, "crossings": 0}, {"row": 11, "col": 13, "center_x": 135, "center_y": 115, "value": 35, "remaining": 35, "crossings": 0}, {"row": 11, "col": 14, "center_x": 145, "center_y": 115, "value": 35, "remaining": 35, "crossings": 0}, {"row": 11, "col": 15, "center_x": 155, "center_y": 115, "value": 31, "remaining": 31, "crossings": 0}, {"row": 11, "col": 16, "center_x": 165, "center_y": 115, "value": 26, "remaining": 26, "crossings": 0}, {"row": 11, "col": 17, "center_x": 175, "center_y": 115, "value": 24, "remaining": 24, "crossings": 0}, {"row": 11, "col": 18, "center_x": 185, "center_y": 115, "value": 21, "remaining": 21, "crossings": 0}, {"row": 11, "col": 19, "center_x": 195, "center_y": 115, "value": 21, "remaining": 21, "crossings": 0}, {"row": 11, "col": 20, "center_x": 205, "center_y": 115, "value": 20, "remaining": 20, "crossings": 0}, {"row": 11, "col": 21, "center_x": 215, "center_y": 115, "value": 20, "remaining": 20, "crossings": 0}, {"row": 11, "col": 22, "center_x": 225, "center_y": 115, "value": 19, "remaining": 19, "crossings": 0}, {"row": 11, "col": 23, "center_x": 235, "center_y": 115, "value": 19, "remaining": 19, "crossings": 0}, {"row": 11, "col": 24, "center_x": 245, "center_y": 115, "value": 18, "remaining": 18, "crossings": 0}, {"row": 11, "col": 25, "center_x": 255, "center_y": 115, "value": 16, "remaining": 16, "crossings": 0}, {"row": 11, "col": 26, "center_x": 265, "center_y": 115, "value": 15, "remaining": 15, "crossings": 0}, {"row": 11, "col": 27, "center_x": 275, "center_y": 115, "value": 14, "remaining": 14, "crossings": 0}, {"row": 11, "col": 28, "center_x": 285, "center_y": 115, "value": 15, "remaining": 15, "crossings": 0}, {"row": 11, "col": 29, "center_x": 295, "center_y": 115, "value": 26, "remaining": 26, "crossings": 0}, {"row": 11, "col": 30, "center_x": 305, "center_y": 115, "value": 37, "remaining": 37, "crossings": 0}, {"row": 11, "col": 31, "center_x": 315, "center_y": 115, "value": 37, "remaining": 37, "crossings": 0}, {"row": 11, "col": 32, "center_x": 325, "center_y": 115, "value": 36, "remaining": 36, "crossings": 0}, {"row": 11, "col": 33, "center_x": 335, "center_y": 115, "value": 35, "remaining": 35, "crossings": 0}, {"row": 11, "col": 34, "center_x": 345, "center_y": 115, "value": 37, "remaining": 37, "crossings": 0}, {"row": 11, "col": 35, "center_x": 355, "center_y": 115, "value": 35, "remaining": 35, "crossings": 0}, {"row": 11, "col": 36, "center_x": 365, "center_y": 115, "value": 27, "remaining": 27, "crossings": 0}, {"row": 11, "col": 42, "center_x": 425, "center_y": 115, "value": 1, "remaining": 1, "crossings": 0}, {"row": 11, "col": 43, "center_x": 435, "center_y": 115, "value": 2, "remaining": 2, "crossings": 0}, {"row": 11, "col": 45, "center_x": 455, "center_y": 115, "value": 2, "remaining": 2, "crossings": 0}, {"row": 11, "col": 46, "center_x": 465, "center_y": 115, "value": 3, "remaining": 3, "crossings": 0}, {"row": 12, "col": 2, "center_x": 25, "center_y": 125, "value": 10, "remaining": 10, "crossings": 0}, {"row": 12, "col": 3, "center_x": 35, "center_y": 125, "value": 9, "remaining": 9, "crossings": 0}, {"row": 12, "col": 10, "center_x": 105, "center_y": 125, "value": 23, "remaining": 23, "crossings": 0}, {"row": 12, "col": 11, "center_x": 115, "center_y": 125, "value": 36, "remaining": 36, "crossings": 0}, {"row": 12, "col": 12, "center_x": 125, "center_y": 125, "value": 36, "remaining": 36, "crossings": 0}, {"row": 12, "col": 13, "center_x": 135, "center_y": 125, "value": 35, "remaining": 35, "crossings": 0}, {"row": 12, "col": 14, "center_x": 145, "center_y": 125, "value": 32, "remaining": 32, "crossings": 0}, {"row": 12, "col": 15, "center_x": 155, "center_y": 125, "value": 24, "remaining": 24, "crossings": 0}, {"row": 12, "col": 16, "center_x": 165, "center_y": 125, "value": 23, "remaining": 23, "crossings": 0}, {"row": 12, "col": 17, "center_x": 175, "center_y": 125, "value": 21, "remaining": 21, "crossings": 0}, {"row": 12, "col": 18, "center_x": 185, "center_y": 125, "value": 21, "remaining": 21, "crossings": 0}, {"row": 12, "col": 19, "center_x": 195, "center_y": 125, "value": 20, "remaining": 20, "crossings": 0}, {"row": 12, "col": 20, "center_x": 205, "center_y": 125, "value": 18, "remaining": 18, "crossings": 0}, {"row": 12, "col": 21, "center_x": 215, "center_y": 125, "value": 19, "remaining": 19, "crossings": 0}, {"row": 12, "col": 22, "center_x": 225, "center_y": 125, "value": 18, "remaining": 18, "crossings": 0}, {"row": 12, "col": 23, "center_x": 235, "center_y": 125, "value": 17, "remaining": 17, "crossings": 0}, {"row": 12, "col": 24, "center_x": 245, "center_y": 125, "value": 17, "remaining": 17, "crossings": 0}, {"row": 12, "col": 25, "center_x": 255, "center_y": 125, "value": 17, "remaining": 17, "crossings": 0}, {"row": 12, "col": 26, "center_x": 265, "center_y": 125, "value": 15, "remaining": 15, "crossings": 0}, {"row": 12, "col": 27, "center_x": 275, "center_y": 125, "value": 13, "remaining": 13, "crossings": 0}, {"row": 12, "col": 28, "center_x": 285, "center_y": 125, "value": 13, "remaining": 13, "crossings": 0}, {"row": 12, "col": 29, "center_x": 295, "center_y": 125, "value": 14, "remaining": 14, "crossings": 0}, {"row": 12, "col": 30, "center_x": 305, "center_y": 125, "value": 26, "remaining": 26, "crossings": 0}, {"row": 12, "col": 31, "center_x": 315, "center_y": 125, "value": 37, "remaining": 37, "crossings": 0}, {"row": 12, "col": 32, "center_x": 325, "center_y": 125, "value": 36, "remaining": 36, "crossings": 0}, {"row": 12, "col": 33, "center_x": 335, "center_y": 125, "value": 33, "remaining": 33, "crossings": 0}, {"row": 12, "col": 34, "center_x": 345, "center_y": 125, "value": 37, "remaining": 37, "crossings": 0}, {"row": 12, "col": 35, "center_x": 355, "center_y": 125, "value": 35, "remaining": 35, "crossings": 0}, {"row": 12, "col": 36, "center_x": 365, "center_y": 125, "value": 26, "remaining": 26, "crossings": 0}, {"row": 12, "col": 44, "center_x": 445, "center_y": 125, "value": 1, "remaining": 1, "crossings": 0}, {"row": 12, "col": 45, "center_x": 455, "center_y": 125, "value": 5, "remaining": 5, "crossings": 0}, {"row": 12, "col": 46, "center_x": 465, "center_y": 125, "value": 8, "remaining": 8, "crossings": 0}, {"row": 13, "col": 1, "center_x": 15, "center_y": 135, "value": 3, "remaining": 3, "crossings": 0}, {"row": 13, "col": 2, "center_x": 25, "center_y": 135, "value": 3, "remaining": 3, "crossings": 0}, {"row": 13, "col": 3, "center_x": 35, "center_y": 135, "value": 2, "remaining": 2, "crossings": 0}, {"row": 13, "col": 10, "center_x": 105, "center_y": 135, "value": 24, "remaining": 24, "crossings": 0}, {"row": 13, "col": 11, "center_x": 115, "center_y": 135, "value": 34, "remaining": 34, "crossings": 0}, {"row": 13, "col": 12, "center_x": 125, "center_y": 135, "value": 35, "remaining": 35, "crossings": 0}, {"row": 13, "col": 13, "center_x": 135, "center_y": 135, "value": 34, "remaining": 34, "crossings": 0}, {"row": 13, "col": 14, "center_x": 145, "center_y": 135, "value": 23, "remaining": 23, "crossings": 0}, {"row": 13, "col": 15, "center_x": 155, "center_y": 135, "value": 20, "remaining": 20, "crossings": 0}, {"row": 13, "col": 16, "center_x": 165, "center_y": 135, "value": 21, "remaining": 21, "crossings": 0}, {"row": 13, "col": 17, "center_x": 175, "center_y": 135, "value": 19, "remaining": 19, "crossings": 0}, {"row": 13, "col": 18, "center_x": 185, "center_y": 135, "value": 19, "remaining": 19, "crossings": 0}, {"row": 13, "col": 19, "center_x": 195, "center_y": 135, "value": 18, "remaining": 18, "crossings": 0}, {"row": 13, "col": 20, "center_x": 205, "center_y": 135, "value": 17, "remaining": 17, "crossings": 0}, {"row": 13, "col": 21, "center_x": 215, "center_y": 135, "value": 18, "remaining": 18, "crossings": 0}, {"row": 13, "col": 22, "center_x": 225, "center_y": 135, "value": 18, "remaining": 18, "crossings": 0}, {"row": 13, "col": 23, "center_x": 235, "center_y": 135, "value": 17, "remaining": 17, "crossings": 0}, {"row": 13, "col": 24, "center_x": 245, "center_y": 135, "value": 16, "remaining": 16, "crossings": 0}, {"row": 13, "col": 25, "center_x": 255, "center_y": 135, "value": 16, "remaining": 16, "crossings": 0}, {"row": 13, "col": 26, "center_x": 265, "center_y": 135, "value": 14, "remaining": 14, "crossings": 0}, {"row": 13, "col": 27, "center_x": 275, "center_y": 135, "value": 14, "remaining": 14, "crossings": 0}, {"row": 13, "col": 28, "center_x": 285, "center_y": 135, "value": 12, "remaining": 12, "crossings": 0}, {"row": 13, "col": 29, "center_x": 295, "center_y": 135, "value": 12, "remaining": 12, "crossings": 0}, {"row": 13, "col": 30, "center_x": 305, "center_y": 135, "value": 14, "remaining": 14, "crossings": 0}, {"row": 13, "col": 31, "center_x": 315, "center_y": 135, "value": 31, "remaining": 31, "crossings": 0}, {"row": 13, "col": 32, "center_x": 325, "center_y": 135, "value": 35, "remaining": 35, "crossings": 0}, {"row": 13, "col": 33, "center_x": 335, "center_y": 135, "value": 33, "remaining": 33, "crossings": 0}, {"row": 13, "col": 34, "center_x": 345, "center_y": 135, "value": 36, "remaining": 36, "crossings": 0}, {"row": 13, "col": 35, "center_x": 355, "center_y": 135, "value": 35, "remaining": 35, "crossings": 0}, {"row": 13, "col": 36, "center_x": 365, "center_y": 135, "value": 26, "remaining": 26, "crossings": 0}, {"row": 13, "col": 44, "center_x": 445, "center_y": 135, "value": 2, "remaining": 2, "crossings": 0}, {"row": 13, "col": 46, "center_x": 465, "center_y": 135, "value": 1, "remaining": 1, "crossings": 0}, {"row": 13, "col": 47, "center_x": 471, "center_y": 135, "value": 5, "remaining": 5, "crossings": 0}, {"row": 14, "col": 1, "center_x": 15, "center_y": 145, "value": 11, "remaining": 11, "crossings": 0}, {"row": 14, "col": 2, "center_x": 25, "center_y": 145, "value": 10, "remaining": 10, "crossings": 0}, {"row": 14, "col": 10, "center_x": 105, "center_y": 145, "value": 24, "remaining": 24, "crossings": 0}, {"row": 14, "col": 11, "center_x": 115, "center_y": 145, "value": 34, "remaining": 34, "crossings": 0}, {"row": 14, "col": 12, "center_x": 125, "center_y": 145, "value": 34, "remaining": 34, "crossings": 0}, {"row": 14, "col": 13, "center_x": 135, "center_y": 145, "value": 28, "remaining": 28, "crossings": 0}, {"row": 14, "col": 14, "center_x": 145, "center_y": 145, "value": 18, "remaining": 18, "crossings": 0}, {"row": 14, "col": 15, "center_x": 155, "center_y": 145, "value": 18, "remaining": 18, "crossings": 0}, {"row": 14, "col": 16, "center_x": 165, "center_y": 145, "value": 19, "remaining": 19, "crossings": 0}, {"row": 14, "col": 17, "center_x": 175, "center_y": 145, "value": 17, "remaining": 17, "crossings": 0}, {"row": 14, "col": 18, "center_x": 185, "center_y": 145, "value": 18, "remaining": 18, "crossings": 0}, {"row": 14, "col": 19, "center_x": 195, "center_y": 145, "value": 16, "remaining": 16, "crossings": 0}, {"row": 14, "col": 20, "center_x": 205, "center_y": 145, "value": 15, "remaining": 15, "crossings": 0}, {"row": 14, "col": 21, "center_x": 215, "center_y": 145, "value": 15, "remaining": 15, "crossings": 0}, {"row": 14, "col": 22, "center_x": 225, "center_y": 145, "value": 16, "remaining": 16, "crossings": 0}, {"row": 14, "col": 23, "center_x": 235, "center_y": 145, "value": 16, "remaining": 16, "crossings": 0}, {"row": 14, "col": 24, "center_x": 245, "center_y": 145, "value": 16, "remaining": 16, "crossings": 0}, {"row": 14, "col": 25, "center_x": 255, "center_y": 145, "value": 16, "remaining": 16, "crossings": 0}, {"row": 14, "col": 26, "center_x": 265, "center_y": 145, "value": 15, "remaining": 15, "crossings": 0}, {"row": 14, "col": 27, "center_x": 275, "center_y": 145, "value": 13, "remaining": 13, "crossings": 0}, {"row": 14, "col": 28, "center_x": 285, "center_y": 145, "value": 12, "remaining": 12, "crossings": 0}, {"row": 14, "col": 29, "center_x": 295, "center_y": 145, "value": 12, "remaining": 12, "crossings": 0}, {"row": 14, "col": 30, "center_x": 305, "center_y": 145, "value": 11, "remaining": 11, "crossings": 0}, {"row": 14, "col": 31, "center_x": 315, "center_y": 145, "value": 16, "remaining": 16, "crossings": 0}, {"row": 14, "col": 32, "center_x": 325, "center_y": 145, "value": 34, "remaining": 34, "crossings": 0}, {"row": 14, "col": 33, "center_x": 335, "center_y": 145, "value": 35, "remaining": 35, "crossings": 0}, {"row": 14, "col": 34, "center_x": 345, "center_y": 145, "value": 37, "remaining": 37, "crossings": 0}, {"row": 14, "col": 35, "center_x": 355, "center_y": 145, "value": 36, "remaining": 36, "crossings": 0}, {"row": 14, "col": 36, "center_x": 365, "center_y": 145, "value": 27, "remaining": 27, "crossings": 0}, {"row": 14, "col": 46, "center_x": 465, "center_y": 145, "value": 6, "remaining": 6, "crossings": 0}, {"row": 14, "col": 47, "center_x": 471, "center_y": 145, "value": 7, "remaining": 7, "crossings": 0}, {"row": 15, "col": 1, "center_x": 15, "center_y": 155, "value": 1, "remaining": 1, "crossings": 0}, {"row": 15, "col": 2, "center_x": 25, "center_y": 155, "value": 2, "remaining": 2, "crossings": 0}, {"row": 15, "col": 10, "center_x": 105, "center_y": 155, "value": 23, "remaining": 23, "crossings": 0}, {"row": 15, "col": 11, "center_x": 115, "center_y": 155, "value": 34, "remaining": 34, "crossings": 0}, {"row": 15, "col": 12, "center_x": 125, "center_y": 155, "value": 34, "remaining": 34, "crossings": 0}, {"row": 15, "col": 13, "center_x": 135, "center_y": 155, "value": 21, "remaining": 21, "crossings": 0}, {"row": 15, "col": 14, "center_x": 145, "center_y": 155, "value": 17, "remaining": 17, "crossings": 0}, {"row": 15, "col": 15, "center_x": 155, "center_y": 155, "value": 16, "remaining": 16, "crossings": 0}, {"row": 15, "col": 16, "center_x": 165, "center_y": 155, "value": 17, "remaining": 17, "crossings": 0}, {"row": 15, "col": 17, "center_x": 175, "center_y": 155, "value": 17, "remaining": 17, "crossings": 0}, {"row": 15, "col": 18, "center_x": 185, "center_y": 155, "value": 17, "remaining": 17, "crossings": 0}, {"row": 15, "col": 19, "center_x": 195, "center_y": 155, "value": 15, "remaining": 15, "crossings": 0}, {"row": 15, "col": 20, "center_x": 205, "center_y": 155, "value": 15, "remaining": 15, "crossings": 0}, {"row": 15, "col": 21, "center_x": 215, "center_y": 155, "value": 14, "remaining": 14, "crossings": 0}, {"row": 15, "col": 22, "center_x": 225, "center_y": 155, "value": 15, "remaining": 15, "crossings": 0}, {"row": 15, "col": 23, "center_x": 235, "center_y": 155, "value": 15, "remaining": 15, "crossings": 0}, {"row": 15, "col": 24, "center_x": 245, "center_y": 155, "value": 16, "remaining": 16, "crossings": 0}, {"row": 15, "col": 25, "center_x": 255, "center_y": 155, "value": 15, "remaining": 15, "crossings": 0}, {"row": 15, "col": 26, "center_x": 265, "center_y": 155, "value": 14, "remaining": 14, "crossings": 0}, {"row": 15, "col": 27, "center_x": 275, "center_y": 155, "value": 14, "remaining": 14, "crossings": 0}, {"row": 15, "col": 28, "center_x": 285, "center_y": 155, "value": 12, "remaining": 12, "crossings": 0}, {"row": 15, "col": 29, "center_x": 295, "center_y": 155, "value": 12, "remaining": 12, "crossings": 0}, {"row": 15, "col": 30, "center_x": 305, "center_y": 155, "value": 12, "remaining": 12, "crossings": 0}, {"row": 15, "col": 31, "center_x": 315, "center_y": 155, "value": 12, "remaining": 12, "crossings": 0}, {"row": 15, "col": 32, "center_x": 325, "center_y": 155, "value": 26, "remaining": 26, "crossings": 0}, {"row": 15, "col": 33, "center_x": 335, "center_y": 155, "value": 36, "remaining": 36, "crossings": 0}, {"row": 15, "col": 34, "center_x": 345, "center_y": 155, "value": 37, "remaining": 37, "crossings": 0}, {"row": 15, "col": 35, "center_x": 355, "center_y": 155, "value": 37, "remaining": 37, "crossings": 0}, {"row": 15, "col": 36, "center_x": 365, "center_y": 155, "value": 30, "remaining": 30, "crossings": 0}, {"row": 15, "col": 45, "center_x": 455, "center_y": 155, "value": 3, "remaining": 3, "crossings": 0}, {"row": 16, "col": 0, "center_x": 5, "center_y": 165, "value": 8, "remaining": 8, "crossings": 0}, {"row": 16, "col": 1, "center_x": 15, "center_y": 165, "value": 10, "remaining": 10, "crossings": 0}, {"row": 16, "col": 10, "center_x": 105, "center_y": 165, "value": 24, "remaining": 24, "crossings": 0}, {"row": 16, "col": 11, "center_x": 115, "center_y": 165, "value": 34, "remaining": 34, "crossings": 0}, {"row": 16, "col": 12, "center_x": 125, "center_y": 165, "value": 32, "remaining": 32, "crossings": 0}, {"row": 16, "col": 13, "center_x": 135, "center_y": 165, "value": 17, "remaining": 17, "crossings": 0}, {"row": 16, "col": 14, "center_x": 145, "center_y": 165, "value": 15, "remaining": 15, "crossings": 0}, {"row": 16, "col": 15, "center_x": 155, "center_y": 165, "value": 15, "remaining": 15, "crossings": 0}, {"row": 16, "col": 16, "center_x": 165, "center_y": 165, "value": 17, "remaining": 17, "crossings": 0}, {"row": 16, "col": 17, "center_x": 175, "center_y": 165, "value": 18, "remaining": 18, "crossings": 0}, {"row": 16, "col": 18, "center_x": 185, "center_y": 165, "value": 17, "remaining": 17, "crossings": 0}, {"row": 16, "col": 19, "center_x": 195, "center_y": 165, "value": 15, "remaining": 15, "crossings": 0}, {"row": 16, "col": 20, "center_x": 205, "center_y": 165, "value": 14, "remaining": 14, "crossings": 0}, {"row": 16, "col": 21, "center_x": 215, "center_y": 165, "value": 13, "remaining": 13, "crossings": 0}, {"row": 16, "col": 22, "center_x": 225, "center_y": 165, "value": 13, "remaining": 13, "crossings": 0}, {"row": 16, "col": 23, "center_x": 235, "center_y": 165, "value": 14, "remaining": 14, "crossings": 0}, {"row": 16, "col": 24, "center_x": 245, "center_y": 165, "value": 14, "remaining": 14, "crossings": 0}, {"row": 16, "col": 25, "center_x": 255, "center_y": 165, "value": 15, "remaining": 15, "crossings": 0}, {"row": 16, "col": 26, "center_x": 265, "center_y": 165, "value": 15, "remaining": 15, "crossings": 0}, {"row": 16, "col": 27, "center_x": 275, "center_y": 165, "value": 14, "remaining": 14, "crossings": 0}, {"row": 16, "col": 28, "center_x": 285, "center_y": 165, "value": 13, "remaining": 13, "crossings": 0}, {"row": 16, "col": 29, "center_x": 295, "center_y": 165, "value": 13, "remaining": 13, "crossings": 0}, {"row": 16, "col": 30, "center_x": 305, "center_y": 165, "value": 13, "remaining": 13, "crossings": 0}, {"row": 16, "col": 31, "center_x": 315, "center_y": 165, "value": 12, "remaining": 12, "crossings": 0}, {"row": 16, "col": 32, "center_x": 325, "center_y": 165, "value": 17, "remaining": 17, "crossings": 0}, {"row": 16, "col": 33, "center_x": 335, "center_y": 165, "value": 35, "remaining": 35, "crossings": 0}, {"row": 16, "col": 34, "center_x": 345, "center_y": 165, "value": 36, "remaining": 36, "crossings": 0}, {"row": 16, "col": 35, "center_x": 355, "center_y": 165, "value": 36, "remaining": 36, "crossings": 0}, {"row": 16, "col": 36, "center_x": 365, "center_y": 165, "value": 32, "remaining": 32, "crossings": 0}, {"row": 16, "col": 47, "center_x": 471, "center_y": 165, "value": 20, "remaining": 20, "crossings": 0}, {"row": 17, "col": 0, "center_x": 5, "center_y": 175, "value": 1, "remaining": 1, "crossings": 0}, {"row": 17, "col": 1, "center_x": 15, "center_y": 175, "value": 5, "remaining": 5, "crossings": 0}, {"row": 17, "col": 10, "center_x": 105, "center_y": 175, "value": 24, "remaining": 24, "crossings": 0}, {"row": 17, "col": 11, "center_x": 115, "center_y": 175, "value": 34, "remaining": 34, "crossings": 0}, {"row": 17, "col": 12, "center_x": 125, "center_y": 175, "value": 30, "remaining": 30, "crossings": 0}, {"row": 17, "col": 13, "center_x": 135, "center_y": 175, "value": 16, "remaining": 16, "crossings": 0}, {"row": 17, "col": 14, "center_x": 145, "center_y": 175, "value": 14, "remaining": 14, "crossings": 0}, {"row": 17, "col": 15, "center_x": 155, "center_y": 175, "value": 14, "remaining": 14, "crossings": 0}, {"row": 17, "col": 16, "center_x": 165, "center_y": 175, "value": 16, "remaining": 16, "crossings": 0}, {"row": 17, "col": 17, "center_x": 175, "center_y": 175, "value": 17, "remaining": 17, "crossings": 0}, {"row": 17, "col": 18, "center_x": 185, "center_y": 175, "value": 18, "remaining": 18, "crossings": 0}, {"row": 17, "col": 19, "center_x": 195, "center_y": 175, "value": 16, "remaining": 16, "crossings": 0}, {"row": 17, "col": 20, "center_x": 205, "center_y": 175, "value": 14, "remaining": 14, "crossings": 0}, {"row": 17, "col": 21, "center_x": 215, "center_y": 175, "value": 13, "remaining": 13, "crossings": 0}, {"row": 17, "col": 22, "center_x": 225, "center_y": 175, "value": 13, "remaining": 13, "crossings": 0}, {"row": 17, "col": 23, "center_x": 235, "center_y": 175, "value": 13, "remaining": 13, "crossings": 0}, {"row": 17, "col": 24, "center_x": 245, "center_y": 175, "value": 14, "remaining": 14, "crossings": 0}, {"row": 17, "col": 25, "center_x": 255, "center_y": 175, "value": 14, "remaining": 14, "crossings": 0}, {"row": 17, "col": 26, "center_x": 265, "center_y": 175, "value": 14, "remaining": 14, "crossings": 0}, {"row": 17, "col": 27, "center_x": 275, "center_y": 175, "value": 13, "remaining": 13, "crossings": 0}, {"row": 17, "col": 28, "center_x": 285, "center_y": 175, "value": 11, "remaining": 11, "crossings": 0}, {"row": 17, "col": 29, "center_x": 295, "center_y": 175, "value": 13, "remaining": 13, "crossings": 0}, {"row": 17, "col": 30, "center_x": 305, "center_y": 175, "value": 12, "remaining": 12, "crossings": 0}, {"row": 17, "col": 31, "center_x": 315, "center_y": 175, "value": 12, "remaining": 12, "crossings": 0}, {"row": 17, "col": 32, "center_x": 325, "center_y": 175, "value": 14, "remaining": 14, "crossings": 0}, {"row": 17, "col": 33, "center_x": 335, "center_y": 175, "value": 32, "remaining": 32, "crossings": 0}, {"row": 17, "col": 34, "center_x": 345, "center_y": 175, "value": 35, "remaining": 35, "crossings": 0}, {"row": 17, "col": 35, "center_x": 355, "center_y": 175, "value": 36, "remaining": 36, "crossings": 0}, {"row": 17, "col": 36, "center_x": 365, "center_y": 175, "value": 32, "remaining": 32, "crossings": 0}, {"row": 17, "col": 45, "center_x": 455, "center_y": 175, "value": 4, "remaining": 4, "crossings": 0}, {"row": 17, "col": 47, "center_x": 471, "center_y": 175, "value": 1, "remaining": 1, "crossings": 0}, {"row": 18, "col": 0, "center_x": 5, "center_y": 185, "value": 10, "remaining": 10, "crossings": 0}, {"row": 18, "col": 1, "center_x": 15, "center_y": 185, "value": 4, "remaining": 4, "crossings": 0}, {"row": 18, "col": 10, "center_x": 105, "center_y": 185, "value": 24, "remaining": 24, "crossings": 0}, {"row": 18, "col": 11, "center_x": 115, "center_y": 185, "value": 34, "remaining": 34, "crossings": 0}, {"row": 18, "col": 12, "center_x": 125, "center_y": 185, "value": 29, "remaining": 29, "crossings": 0}, {"row": 18, "col": 13, "center_x": 135, "center_y": 185, "value": 15, "remaining": 15, "crossings": 0}, {"row": 18, "col": 14, "center_x": 145, "center_y": 185, "value": 14, "remaining": 14, "crossings": 0}, {"row": 18, "col": 15, "center_x": 155, "center_y": 185, "value": 14, "remaining": 14, "crossings": 0}, {"row": 18, "col": 16, "center_x": 165, "center_y": 185, "value": 16, "remaining": 16, "crossings": 0}, {"row": 18, "col": 17, "center_x": 175, "center_y": 185, "value": 18, "remaining": 18, "crossings": 0}, {"row": 18, "col": 18, "center_x": 185, "center_y": 185, "value": 19, "remaining": 19, "crossings": 0}, {"row": 18, "col": 19, "center_x": 195, "center_y": 185, "value": 18, "remaining": 18, "crossings": 0}, {"row": 18, "col": 20, "center_x": 205, "center_y": 185, "value": 15, "remaining": 15, "crossings": 0}, {"row": 18, "col": 21, "center_x": 215, "center_y": 185, "value": 14, "remaining": 14, "crossings": 0}, {"row": 18, "col": 22, "center_x": 225, "center_y": 185, "value": 14, "remaining": 14, "crossings": 0}, {"row": 18, "col": 23, "center_x": 235, "center_y": 185, "value": 13, "remaining": 13, "crossings": 0}, {"row": 18, "col": 24, "center_x": 245, "center_y": 185, "value": 14, "remaining": 14, "crossings": 0}, {"row": 18, "col": 25, "center_x": 255, "center_y": 185, "value": 14, "remaining": 14, "crossings": 0}, {"row": 18, "col": 26, "center_x": 265, "center_y": 185, "value": 13, "remaining": 13, "crossings": 0}, {"row": 18, "col": 27, "center_x": 275, "center_y": 185, "value": 13, "remaining": 13, "crossings": 0}, {"row": 18, "col": 28, "center_x": 285, "center_y": 185, "value": 12, "remaining": 12, "crossings": 0}, {"row": 18, "col": 29, "center_x": 295, "center_y": 185, "value": 13, "remaining": 13, "crossings": 0}, {"row": 18, "col": 30, "center_x": 305, "center_y": 185, "value": 12, "remaining": 12, "crossings": 0}, {"row": 18, "col": 31, "center_x": 315, "center_y": 185, "value": 11, "remaining": 11, "crossings": 0}, {"row": 18, "col": 32, "center_x": 325, "center_y": 185, "value": 13, "remaining": 13, "crossings": 0}, {"row": 18, "col": 33, "center_x": 335, "center_y": 185, "value": 30, "remaining": 30, "crossings": 0}, {"row": 18, "col": 34, "center_x": 345, "center_y": 185, "value": 35, "remaining": 35, "crossings": 0}, {"row": 18, "col": 35, "center_x": 355, "center_y": 185, "value": 36, "remaining": 36, "crossings": 0}, {"row": 18, "col": 36, "center_x": 365, "center_y": 185, "value": 31, "remaining": 31, "crossings": 0}, {"row": 19, "col": 0, "center_x": 5, "center_y": 195, "value": 12, "remaining": 12, "crossings": 0}, {"row": 19, "col": 1, "center_x": 15, "center_y": 195, "value": 5, "remaining": 5, "crossings": 0}, {"row": 19, "col": 10, "center_x": 105, "center_y": 195, "value": 24, "remaining": 24, "crossings": 0}, {"row": 19, "col": 11, "center_x": 115, "center_y": 195, "value": 34, "remaining": 34, "crossings": 0}, {"row": 19, "col": 12, "center_x": 125, "center_y": 195, "value": 31, "remaining": 31, "crossings": 0}, {"row": 19, "col": 13, "center_x": 135, "center_y": 195, "value": 16, "remaining": 16, "crossings": 0}, {"row": 19, "col": 14, "center_x": 145, "center_y": 195, "value": 15, "remaining": 15, "crossings": 0}, {"row": 19, "col": 15, "center_x": 155, "center_y": 195, "value": 13, "remaining": 13, "crossings": 0}, {"row": 19, "col": 16, "center_x": 165, "center_y": 195, "value": 17, "remaining": 17, "crossings": 0}, {"row": 19, "col": 17, "center_x": 175, "center_y": 195, "value": 19, "remaining": 19, "crossings": 0}, {"row": 19, "col": 18, "center_x": 185, "center_y": 195, "value": 18, "remaining": 18, "crossings": 0}, {"row": 19, "col": 19, "center_x": 195, "center_y": 195, "value": 17, "remaining": 17, "crossings": 0}, {"row": 19, "col": 20, "center_x": 205, "center_y": 195, "value": 16, "remaining": 16, "crossings": 0}, {"row": 19, "col": 21, "center_x": 215, "center_y": 195, "value": 15, "remaining": 15, "crossings": 0}, {"row": 19, "col": 22, "center_x": 225, "center_y": 195, "value": 14, "remaining": 14, "crossings": 0}, {"row": 19, "col": 23, "center_x": 235, "center_y": 195, "value": 16, "remaining": 16, "crossings": 0}, {"row": 19, "col": 24, "center_x": 245, "center_y": 195, "value": 16, "remaining": 16, "crossings": 0}, {"row": 19, "col": 25, "center_x": 255, "center_y": 195, "value": 16, "remaining": 16, "crossings": 0}, {"row": 19, "col": 26, "center_x": 265, "center_y": 195, "value": 16, "remaining": 16, "crossings": 0}, {"row": 19, "col": 27, "center_x": 275, "center_y": 195, "value": 14, "remaining": 14, "crossings": 0}, {"row": 19, "col": 28, "center_x": 285, "center_y": 195, "value": 13, "remaining": 13, "crossings": 0}, {"row": 19, "col": 29, "center_x": 295, "center_y": 195, "value": 14, "remaining": 14, "crossings": 0}, {"row": 19, "col": 30, "center_x": 305, "center_y": 195, "value": 15, "remaining": 15, "crossings": 0}, {"row": 19, "col": 31, "center_x": 315, "center_y": 195, "value": 13, "remaining": 13, "crossings": 0}, {"row": 19, "col": 32, "center_x": 325, "center_y": 195, "value": 13, "remaining": 13, "crossings": 0}, {"row": 19, "col": 33, "center_x": 335, "center_y": 195, "value": 29, "remaining": 29, "crossings": 0}, {"row": 19, "col": 34, "center_x": 345, "center_y": 195, "value": 35, "remaining": 35, "crossings": 0}, {"row": 19, "col": 35, "center_x": 355, "center_y": 195, "value": 36, "remaining": 36, "crossings": 0}, {"row": 19, "col": 36, "center_x": 365, "center_y": 195, "value": 32, "remaining": 32, "crossings": 0}, {"row": 19, "col": 46, "center_x": 465, "center_y": 195, "value": 4, "remaining": 4, "crossings": 0}, {"row": 20, "col": 0, "center_x": 5, "center_y": 205, "value": 3, "remaining": 3, "crossings": 0}, {"row": 20, "col": 10, "center_x": 105, "center_y": 205, "value": 23, "remaining": 23, "crossings": 0}, {"row": 20, "col": 11, "center_x": 115, "center_y": 205, "value": 33, "remaining": 33, "crossings": 0}, {"row": 20, "col": 12, "center_x": 125, "center_y": 205, "value": 32, "remaining": 32, "crossings": 0}, {"row": 20, "col": 13, "center_x": 135, "center_y": 205, "value": 17, "remaining": 17, "crossings": 0}, {"row": 20, "col": 14, "center_x": 145, "center_y": 205, "value": 18, "remaining": 18, "crossings": 0}, {"row": 20, "col": 15, "center_x": 155, "center_y": 205, "value": 14, "remaining": 14, "crossings": 0}, {"row": 20, "col": 16, "center_x": 165, "center_y": 205, "value": 17, "remaining": 17, "crossings": 0}, {"row": 20, "col": 17, "center_x": 175, "center_y": 205, "value": 19, "remaining": 19, "crossings": 0}, {"row": 20, "col": 18, "center_x": 185, "center_y": 205, "value": 18, "remaining": 18, "crossings": 0}, {"row": 20, "col": 19, "center_x": 195, "center_y": 205, "value": 18, "remaining": 18, "crossings": 0}, {"row": 20, "col": 20, "center_x": 205, "center_y": 205, "value": 19, "remaining": 19, "crossings": 0}, {"row": 20, "col": 21, "center_x": 215, "center_y": 205, "value": 18, "remaining": 18, "crossings": 0}, {"row": 20, "col": 22, "center_x": 225, "center_y": 205, "value": 20, "remaining": 20, "crossings": 0}, {"row": 20, "col": 23, "center_x": 235, "center_y": 205, "value": 20, "remaining": 20, "crossings": 0}, {"row": 20, "col": 24, "center_x": 245, "center_y": 205, "value": 26, "remaining": 26, "crossings": 0}, {"row": 20, "col": 25, "center_x": 255, "center_y": 205, "value": 27, "remaining": 27, "crossings": 0}, {"row": 20, "col": 26, "center_x": 265, "center_y": 205, "value": 24, "remaining": 24, "crossings": 0}, {"row": 20, "col": 27, "center_x": 275, "center_y": 205, "value": 19, "remaining": 19, "crossings": 0}, {"row": 20, "col": 28, "center_x": 285, "center_y": 205, "value": 15, "remaining": 15, "crossings": 0}, {"row": 20, "col": 29, "center_x": 295, "center_y": 205, "value": 23, "remaining": 23, "crossings": 0}, {"row": 20, "col": 30, "center_x": 305, "center_y": 205, "value": 25, "remaining": 25, "crossings": 0}, {"row": 20, "col": 31, "center_x": 315, "center_y": 205, "value": 20, "remaining": 20, "crossings": 0}, {"row": 20, "col": 32, "center_x": 325, "center_y": 205, "value": 17, "remaining": 17, "crossings": 0}, {"row": 20, "col": 33, "center_x": 335, "center_y": 205, "value": 26, "remaining": 26, "crossings": 0}, {"row": 20, "col": 34, "center_x": 345, "center_y": 205, "value": 33, "remaining": 33, "crossings": 0}, {"row": 20, "col": 35, "center_x": 355, "center_y": 205, "value": 36, "remaining": 36, "crossings": 0}, {"row": 20, "col": 36, "center_x": 365, "center_y": 205, "value": 32, "remaining": 32, "crossings": 0}, {"row": 21, "col": 0, "center_x": 5, "center_y": 215, "value": 18, "remaining": 18, "crossings": 0}, {"row": 21, "col": 1, "center_x": 15, "center_y": 215, "value": 4, "remaining": 4, "crossings": 0}, {"row": 21, "col": 10, "center_x": 105, "center_y": 215, "value": 20, "remaining": 20, "crossings": 0}, {"row": 21, "col": 11, "center_x": 115, "center_y": 215, "value": 27, "remaining": 27, "crossings": 0}, {"row": 21, "col": 12, "center_x": 125, "center_y": 215, "value": 27, "remaining": 27, "crossings": 0}, {"row": 21, "col": 13, "center_x": 135, "center_y": 215, "value": 20, "remaining": 20, "crossings": 0}, {"row": 21, "col": 14, "center_x": 145, "center_y": 215, "value": 22, "remaining": 22, "crossings": 0}, {"row": 21, "col": 15, "center_x": 155, "center_y": 215, "value": 15, "remaining": 15, "crossings": 0}, {"row": 21, "col": 16, "center_x": 165, "center_y": 215, "value": 16, "remaining": 16, "crossings": 0}, {"row": 21, "col": 17, "center_x": 175, "center_y": 215, "value": 17, "remaining": 17, "crossings": 0}, {"row": 21, "col": 18, "center_x": 185, "center_y": 215, "value": 16, "remaining": 16, "crossings": 0}, {"row": 21, "col": 19, "center_x": 195, "center_y": 215, "value": 18, "remaining": 18, "crossings": 0}, {"row": 21, "col": 20, "center_x": 205, "center_y": 215, "value": 19, "remaining": 19, "crossings": 0}, {"row": 21, "col": 21, "center_x": 215, "center_y": 215, "value": 21, "remaining": 21, "crossings": 0}, {"row": 21, "col": 22, "center_x": 225, "center_y": 215, "value": 22, "remaining": 22, "crossings": 0}, {"row": 21, "col": 23, "center_x": 235, "center_y": 215, "value": 22, "remaining": 22, "crossings": 0}, {"row": 21, "col": 24, "center_x": 245, "center_y": 215, "value": 26, "remaining": 26, "crossings": 0}, {"row": 21, "col": 25, "center_x": 255, "center_y": 215, "value": 28, "remaining": 28, "crossings": 0}, {"row": 21, "col": 26, "center_x": 265, "center_y": 215, "value": 25, "remaining": 25, "crossings": 0}, {"row": 21, "col": 27, "center_x": 275, "center_y": 215, "value": 15, "remaining": 15, "crossings": 0}, {"row": 21, "col": 28, "center_x": 285, "center_y": 215, "value": 13, "remaining": 13, "crossings": 0}, {"row": 21, "col": 29, "center_x": 295, "center_y": 215, "value": 23, "remaining": 23, "crossings": 0}, {"row": 21, "col": 30, "center_x": 305, "center_y": 215, "value": 28, "remaining": 28, "crossings": 0}, {"row": 21, "col": 31, "center_x": 315, "center_y": 215, "value": 25, "remaining": 25, "crossings": 0}, {"row": 21, "col": 32, "center_x": 325, "center_y": 215, "value": 20, "remaining": 20, "crossings": 0}, {"row": 21, "col": 33, "center_x": 335, "center_y": 215, "value": 27, "remaining": 27, "crossings": 0}, {"row": 21, "col": 34, "center_x": 345, "center_y": 215, "value": 32, "remaining": 32, "crossings": 0}, {"row": 21, "col": 35, "center_x": 355, "center_y": 215, "value": 36, "remaining": 36, "crossings": 0}, {"row": 21, "col": 36, "center_x": 365, "center_y": 215, "value": 32, "remaining": 32, "crossings": 0}, {"row": 21, "col": 46, "center_x": 465, "center_y": 215, "value": 4, "remaining": 4, "crossings": 0}, {"row": 22, "col": 10, "center_x": 105, "center_y": 225, "value": 16, "remaining": 16, "crossings": 0}, {"row": 22, "col": 11, "center_x": 115, "center_y": 225, "value": 22, "remaining": 22, "crossings": 0}, {"row": 22, "col": 12, "center_x": 125, "center_y": 225, "value": 24, "remaining": 24, "crossings": 0}, {"row": 22, "col": 13, "center_x": 135, "center_y": 225, "value": 26, "remaining": 26, "crossings": 0}, {"row": 22, "col": 14, "center_x": 145, "center_y": 225, "value": 17, "remaining": 17, "crossings": 0}, {"row": 22, "col": 15, "center_x": 155, "center_y": 225, "value": 16, "remaining": 16, "crossings": 0}, {"row": 22, "col": 16, "center_x": 165, "center_y": 225, "value": 15, "remaining": 15, "crossings": 0}, {"row": 22, "col": 17, "center_x": 175, "center_y": 225, "value": 18, "remaining": 18, "crossings": 0}, {"row": 22, "col": 18, "center_x": 185, "center_y": 225, "value": 17, "remaining": 17, "crossings": 0}, {"row": 22, "col": 19, "center_x": 195, "center_y": 225, "value": 17, "remaining": 17, "crossings": 0}, {"row": 22, "col": 20, "center_x": 205, "center_y": 225, "value": 17, "remaining": 17, "crossings": 0}, {"row": 22, "col": 21, "center_x": 215, "center_y": 225, "value": 18, "remaining": 18, "crossings": 0}, {"row": 22, "col": 22, "center_x": 225, "center_y": 225, "value": 23, "remaining": 23, "crossings": 0}, {"row": 22, "col": 23, "center_x": 235, "center_y": 225, "value": 32, "remaining": 32, "crossings": 0}, {"row": 22, "col": 24, "center_x": 245, "center_y": 225, "value": 30, "remaining": 30, "crossings": 0}, {"row": 22, "col": 25, "center_x": 255, "center_y": 225, "value": 29, "remaining": 29, "crossings": 0}, {"row": 22, "col": 26, "center_x": 265, "center_y": 225, "value": 22, "remaining": 22, "crossings": 0}, {"row": 22, "col": 27, "center_x": 275, "center_y": 225, "value": 16, "remaining": 16, "crossings": 0}, {"row": 22, "col": 28, "center_x": 285, "center_y": 225, "value": 15, "remaining": 15, "crossings": 0}, {"row": 22, "col": 29, "center_x": 295, "center_y": 225, "value": 25, "remaining": 25, "crossings": 0}, {"row": 22, "col": 30, "center_x": 305, "center_y": 225, "value": 33, "remaining": 33, "crossings": 0}, {"row": 22, "col": 31, "center_x": 315, "center_y": 225, "value": 34, "remaining": 34, "crossings": 0}, {"row": 22, "col": 32, "center_x": 325, "center_y": 225, "value": 27, "remaining": 27, "crossings": 0}, {"row": 22, "col": 33, "center_x": 335, "center_y": 225, "value": 27, "remaining": 27, "crossings": 0}, {"row": 22, "col": 34, "center_x": 345, "center_y": 225, "value": 33, "remaining": 33, "crossings": 0}, {"row": 22, "col": 35, "center_x": 355, "center_y": 225, "value": 36, "remaining": 36, "crossings": 0}, {"row": 22, "col": 36, "center_x": 365, "center_y": 225, "value": 32, "remaining": 32, "crossings": 0}, {"row": 23, "col": 0, "center_x": 5, "center_y": 235, "value": 14, "remaining": 14, "crossings": 0}, {"row": 23, "col": 1, "center_x": 15, "center_y": 235, "value": 3, "remaining": 3, "crossings": 0}, {"row": 23, "col": 10, "center_x": 105, "center_y": 235, "value": 15, "remaining": 15, "crossings": 0}, {"row": 23, "col": 11, "center_x": 115, "center_y": 235, "value": 21, "remaining": 21, "crossings": 0}, {"row": 23, "col": 12, "center_x": 125, "center_y": 235, "value": 24, "remaining": 24, "crossings": 0}, {"row": 23, "col": 13, "center_x": 135, "center_y": 235, "value": 21, "remaining": 21, "crossings": 0}, {"row": 23, "col": 14, "center_x": 145, "center_y": 235, "value": 20, "remaining": 20, "crossings": 0}, {"row": 23, "col": 15, "center_x": 155, "center_y": 235, "value": 18, "remaining": 18, "crossings": 0}, {"row": 23, "col": 16, "center_x": 165, "center_y": 235, "value": 16, "remaining": 16, "crossings": 0}, {"row": 23, "col": 17, "center_x": 175, "center_y": 235, "value": 16, "remaining": 16, "crossings": 0}, {"row": 23, "col": 18, "center_x": 185, "center_y": 235, "value": 18, "remaining": 18, "crossings": 0}, {"row": 23, "col": 19, "center_x": 195, "center_y": 235, "value": 17, "remaining": 17, "crossings": 0}, {"row": 23, "col": 20, "center_x": 205, "center_y": 235, "value": 15, "remaining": 15, "crossings": 0}, {"row": 23, "col": 21, "center_x": 215, "center_y": 235, "value": 14, "remaining": 14, "crossings": 0}, {"row": 23, "col": 22, "center_x": 225, "center_y": 235, "value": 14, "remaining": 14, "crossings": 0}, {"row": 23, "col": 23, "center_x": 235, "center_y": 235, "value": 18, "remaining": 18, "crossings": 0}, {"row": 23, "col": 24, "center_x": 245, "center_y": 235, "value": 21, "remaining": 21, "crossings": 0}, {"row": 23, "col": 25, "center_x": 255, "center_y": 235, "value": 25, "remaining": 25, "crossings": 0}, {"row": 23, "col": 26, "center_x": 265, "center_y": 235, "value": 19, "remaining": 19, "crossings": 0}, {"row": 23, "col": 27, "center_x": 275, "center_y": 235, "value": 16, "remaining": 16, "crossings": 0}, {"row": 23, "col": 28, "center_x": 285, "center_y": 235, "value": 15, "remaining": 15, "crossings": 0}, {"row": 23, "col": 29, "center_x": 295, "center_y": 235, "value": 17, "remaining": 17, "crossings": 0}, {"row": 23, "col": 30, "center_x": 305, "center_y": 235, "value": 24, "remaining": 24, "crossings": 0}, {"row": 23, "col": 31, "center_x": 315, "center_y": 235, "value": 18, "remaining": 18, "crossings": 0}, {"row": 23, "col": 32, "center_x": 325, "center_y": 235, "value": 14, "remaining": 14, "crossings": 0}, {"row": 23, "col": 33, "center_x": 335, "center_y": 235, "value": 25, "remaining": 25, "crossings": 0}, {"row": 23, "col": 34, "center_x": 345, "center_y": 235, "value": 32, "remaining": 32, "crossings": 0}, {"row": 23, "col": 35, "center_x": 355, "center_y": 235, "value": 36, "remaining": 36, "crossings": 0}, {"row": 23, "col": 36, "center_x": 365, "center_y": 235, "value": 32, "remaining": 32, "crossings": 0}, {"row": 24, "col": 0, "center_x": 5, "center_y": 245, "value": 8, "remaining": 8, "crossings": 0}, {"row": 24, "col": 1, "center_x": 15, "center_y": 245, "value": 1, "remaining": 1, "crossings": 0}, {"row": 24, "col": 10, "center_x": 105, "center_y": 245, "value": 16, "remaining": 16, "crossings": 0}, {"row": 24, "col": 11, "center_x": 115, "center_y": 245, "value": 22, "remaining": 22, "crossings": 0}, {"row": 24, "col": 12, "center_x": 125, "center_y": 245, "value": 23, "remaining": 23, "crossings": 0}, {"row": 24, "col": 13, "center_x": 135, "center_y": 245, "value": 20, "remaining": 20, "crossings": 0}, {"row": 24, "col": 14, "center_x": 145, "center_y": 245, "value": 25, "remaining": 25, "crossings": 0}, {"row": 24, "col": 15, "center_x": 155, "center_y": 245, "value": 20, "remaining": 20, "crossings": 0}, {"row": 24, "col": 16, "center_x": 165, "center_y": 245, "value": 19, "remaining": 19, "crossings": 0}, {"row": 24, "col": 17, "center_x": 175, "center_y": 245, "value": 20, "remaining": 20, "crossings": 0}, {"row": 24, "col": 18, "center_x": 185, "center_y": 245, "value": 18, "remaining": 18, "crossings": 0}, {"row": 24, "col": 19, "center_x": 195, "center_y": 245, "value": 17, "remaining": 17, "crossings": 0}, {"row": 24, "col": 20, "center_x": 205, "center_y": 245, "value": 15, "remaining": 15, "crossings": 0}, {"row": 24, "col": 21, "center_x": 215, "center_y": 245, "value": 14, "remaining": 14, "crossings": 0}, {"row": 24, "col": 22, "center_x": 225, "center_y": 245, "value": 16, "remaining": 16, "crossings": 0}, {"row": 24, "col": 23, "center_x": 235, "center_y": 245, "value": 18, "remaining": 18, "crossings": 0}, {"row": 24, "col": 24, "center_x": 245, "center_y": 245, "value": 21, "remaining": 21, "crossings": 0}, {"row": 24, "col": 25, "center_x": 255, "center_y": 245, "value": 16, "remaining": 16, "crossings": 0}, {"row": 24, "col": 26, "center_x": 265, "center_y": 245, "value": 15, "remaining": 15, "crossings": 0}, {"row": 24, "col": 27, "center_x": 275, "center_y": 245, "value": 16, "remaining": 16, "crossings": 0}, {"row": 24, "col": 28, "center_x": 285, "center_y": 245, "value": 15, "remaining": 15, "crossings": 0}, {"row": 24, "col": 29, "center_x": 295, "center_y": 245, "value": 13, "remaining": 13, "crossings": 0}, {"row": 24, "col": 30, "center_x": 305, "center_y": 245, "value": 18, "remaining": 18, "crossings": 0}, {"row": 24, "col": 31, "center_x": 315, "center_y": 245, "value": 19, "remaining": 19, "crossings": 0}, {"row": 24, "col": 32, "center_x": 325, "center_y": 245, "value": 15, "remaining": 15, "crossings": 0}, {"row": 24, "col": 33, "center_x": 335, "center_y": 245, "value": 21, "remaining": 21, "crossings": 0}, {"row": 24, "col": 34, "center_x": 345, "center_y": 245, "value": 31, "remaining": 31, "crossings": 0}, {"row": 24, "col": 35, "center_x": 355, "center_y": 245, "value": 35, "remaining": 35, "crossings": 0}, {"row": 24, "col": 36, "center_x": 365, "center_y": 245, "value": 32, "remaining": 32, "crossings": 0}, {"row": 24, "col": 46, "center_x": 465, "center_y": 245, "value": 4, "remaining": 4, "crossings": 0}, {"row": 25, "col": 0, "center_x": 5, "center_y": 255, "value": 6, "remaining": 6, "crossings": 0}, {"row": 25, "col": 1, "center_x": 15, "center_y": 255, "value": 3, "remaining": 3, "crossings": 0}, {"row": 25, "col": 10, "center_x": 105, "center_y": 255, "value": 15, "remaining": 15, "crossings": 0}, {"row": 25, "col": 11, "center_x": 115, "center_y": 255, "value": 23, "remaining": 23, "crossings": 0}, {"row": 25, "col": 12, "center_x": 125, "center_y": 255, "value": 25, "remaining": 25, "crossings": 0}, {"row": 25, "col": 13, "center_x": 135, "center_y": 255, "value": 20, "remaining": 20, "crossings": 0}, {"row": 25, "col": 14, "center_x": 145, "center_y": 255, "value": 20, "remaining": 20, "crossings": 0}, {"row": 25, "col": 15, "center_x": 155, "center_y": 255, "value": 15, "remaining": 15, "crossings": 0}, {"row": 25, "col": 16, "center_x": 165, "center_y": 255, "value": 19, "remaining": 19, "crossings": 0}, {"row": 25, "col": 17, "center_x": 175, "center_y": 255, "value": 21, "remaining": 21, "crossings": 0}, {"row": 25, "col": 18, "center_x": 185, "center_y": 255, "value": 19, "remaining": 19, "crossings": 0}, {"row": 25, "col": 19, "center_x": 195, "center_y": 255, "value": 18, "remaining": 18, "crossings": 0}, {"row": 25, "col": 20, "center_x": 205, "center_y": 255, "value": 16, "remaining": 16, "crossings": 0}, {"row": 25, "col": 21, "center_x": 215, "center_y": 255, "value": 15, "remaining": 15, "crossings": 0}, {"row": 25, "col": 22, "center_x": 225, "center_y": 255, "value": 15, "remaining": 15, "crossings": 0}, {"row": 25, "col": 23, "center_x": 235, "center_y": 255, "value": 15, "remaining": 15, "crossings": 0}, {"row": 25, "col": 24, "center_x": 245, "center_y": 255, "value": 15, "remaining": 15, "crossings": 0}, {"row": 25, "col": 25, "center_x": 255, "center_y": 255, "value": 14, "remaining": 14, "crossings": 0}, {"row": 25, "col": 26, "center_x": 265, "center_y": 255, "value": 16, "remaining": 16, "crossings": 0}, {"row": 25, "col": 27, "center_x": 275, "center_y": 255, "value": 16, "remaining": 16, "crossings": 0}, {"row": 25, "col": 28, "center_x": 285, "center_y": 255, "value": 14, "remaining": 14, "crossings": 0}, {"row": 25, "col": 29, "center_x": 295, "center_y": 255, "value": 12, "remaining": 12, "crossings": 0}, {"row": 25, "col": 30, "center_x": 305, "center_y": 255, "value": 14, "remaining": 14, "crossings": 0}, {"row": 25, "col": 31, "center_x": 315, "center_y": 255, "value": 12, "remaining": 12, "crossings": 0}, {"row": 25, "col": 32, "center_x": 325, "center_y": 255, "value": 12, "remaining": 12, "crossings": 0}, {"row": 25, "col": 33, "center_x": 335, "center_y": 255, "value": 23, "remaining": 23, "crossings": 0}, {"row": 25, "col": 34, "center_x": 345, "center_y": 255, "value": 32, "remaining": 32, "crossings": 0}, {"row": 25, "col": 35, "center_x": 355, "center_y": 255, "value": 35, "remaining": 35, "crossings": 0}, {"row": 25, "col": 36, "center_x": 365, "center_y": 255, "value": 32, "remaining": 32, "crossings": 0}, {"row": 26, "col": 0, "center_x": 5, "center_y": 265, "value": 12, "remaining": 12, "crossings": 0}, {"row": 26, "col": 1, "center_x": 15, "center_y": 265, "value": 1, "remaining": 1, "crossings": 0}, {"row": 26, "col": 10, "center_x": 105, "center_y": 265, "value": 15, "remaining": 15, "crossings": 0}, {"row": 26, "col": 11, "center_x": 115, "center_y": 265, "value": 26, "remaining": 26, "crossings": 0}, {"row": 26, "col": 12, "center_x": 125, "center_y": 265, "value": 32, "remaining": 32, "crossings": 0}, {"row": 26, "col": 13, "center_x": 135, "center_y": 265, "value": 20, "remaining": 20, "crossings": 0}, {"row": 26, "col": 14, "center_x": 145, "center_y": 265, "value": 16, "remaining": 16, "crossings": 0}, {"row": 26, "col": 15, "center_x": 155, "center_y": 265, "value": 15, "remaining": 15, "crossings": 0}, {"row": 26, "col": 16, "center_x": 165, "center_y": 265, "value": 18, "remaining": 18, "crossings": 0}, {"row": 26, "col": 17, "center_x": 175, "center_y": 265, "value": 22, "remaining": 22, "crossings": 0}, {"row": 26, "col": 18, "center_x": 185, "center_y": 265, "value": 19, "remaining": 19, "crossings": 0}, {"row": 26, "col": 19, "center_x": 195, "center_y": 265, "value": 19, "remaining": 19, "crossings": 0}, {"row": 26, "col": 20, "center_x": 205, "center_y": 265, "value": 18, "remaining": 18, "crossings": 0}, {"row": 26, "col": 21, "center_x": 215, "center_y": 265, "value": 16, "remaining": 16, "crossings": 0}, {"row": 26, "col": 22, "center_x": 225, "center_y": 265, "value": 15, "remaining": 15, "crossings": 0}, {"row": 26, "col": 23, "center_x": 235, "center_y": 265, "value": 15, "remaining": 15, "crossings": 0}, {"row": 26, "col": 24, "center_x": 245, "center_y": 265, "value": 14, "remaining": 14, "crossings": 0}, {"row": 26, "col": 25, "center_x": 255, "center_y": 265, "value": 18, "remaining": 18, "crossings": 0}, {"row": 26, "col": 26, "center_x": 265, "center_y": 265, "value": 18, "remaining": 18, "crossings": 0}, {"row": 26, "col": 27, "center_x": 275, "center_y": 265, "value": 18, "remaining": 18, "crossings": 0}, {"row": 26, "col": 28, "center_x": 285, "center_y": 265, "value": 16, "remaining": 16, "crossings": 0}, {"row": 26, "col": 29, "center_x": 295, "center_y": 265, "value": 17, "remaining": 17, "crossings": 0}, {"row": 26, "col": 30, "center_x": 305, "center_y": 265, "value": 15, "remaining": 15, "crossings": 0}, {"row": 26, "col": 31, "center_x": 315, "center_y": 265, "value": 13, "remaining": 13, "crossings": 0}, {"row": 26, "col": 32, "center_x": 325, "center_y": 265, "value": 14, "remaining": 14, "crossings": 0}, {"row": 26, "col": 33, "center_x": 335, "center_y": 265, "value": 25, "remaining": 25, "crossings": 0}, {"row": 26, "col": 34, "center_x": 345, "center_y": 265, "value": 32, "remaining": 32, "crossings": 0}, {"row": 26, "col": 35, "center_x": 355, "center_y": 265, "value": 35, "remaining": 35, "crossings": 0}, {"row": 26, "col": 36, "center_x": 365, "center_y": 265, "value": 32, "remaining": 32, "crossings": 0}, {"row": 26, "col": 46, "center_x": 465, "center_y": 265, "value": 4, "remaining": 4, "crossings": 0}, {"row": 27, "col": 10, "center_x": 105, "center_y": 275, "value": 19, "remaining": 19, "crossings": 0}, {"row": 27, "col": 11, "center_x": 115, "center_y": 275, "value": 34, "remaining": 34, "crossings": 0}, {"row": 27, "col": 12, "center_x": 125, "center_y": 275, "value": 30, "remaining": 30, "crossings": 0}, {"row": 27, "col": 13, "center_x": 135, "center_y": 275, "value": 23, "remaining": 23, "crossings": 0}, {"row": 27, "col": 14, "center_x": 145, "center_y": 275, "value": 17, "remaining": 17, "crossings": 0}, {"row": 27, "col": 15, "center_x": 155, "center_y": 275, "value": 18, "remaining": 18, "crossings": 0}, {"row": 27, "col": 16, "center_x": 165, "center_y": 275, "value": 18, "remaining": 18, "crossings": 0}, {"row": 27, "col": 17, "center_x": 175, "center_y": 275, "value": 21, "remaining": 21, "crossings": 0}, {"row": 27, "col": 18, "center_x": 185, "center_y": 275, "value": 20, "remaining": 20, "crossings": 0}, {"row": 27, "col": 19, "center_x": 195, "center_y": 275, "value": 19, "remaining": 19, "crossings": 0}, {"row": 27, "col": 20, "center_x": 205, "center_y": 275, "value": 17, "remaining": 17, "crossings": 0}, {"row": 27, "col": 21, "center_x": 215, "center_y": 275, "value": 16, "remaining": 16, "crossings": 0}, {"row": 27, "col": 22, "center_x": 225, "center_y": 275, "value": 15, "remaining": 15, "crossings": 0}, {"row": 27, "col": 23, "center_x": 235, "center_y": 275, "value": 15, "remaining": 15, "crossings": 0}, {"row": 27, "col": 24, "center_x": 245, "center_y": 275, "value": 15, "remaining": 15, "crossings": 0}, {"row": 27, "col": 25, "center_x": 255, "center_y": 275, "value": 22, "remaining": 22, "crossings": 0}, {"row": 27, "col": 26, "center_x": 265, "center_y": 275, "value": 19, "remaining": 19, "crossings": 0}, {"row": 27, "col": 27, "center_x": 275, "center_y": 275, "value": 32, "remaining": 32, "crossings": 0}, {"row": 27, "col": 28, "center_x": 285, "center_y": 275, "value": 28, "remaining": 28, "crossings": 0}, {"row": 27, "col": 29, "center_x": 295, "center_y": 275, "value": 26, "remaining": 26, "crossings": 0}, {"row": 27, "col": 30, "center_x": 305, "center_y": 275, "value": 25, "remaining": 25, "crossings": 0}, {"row": 27, "col": 31, "center_x": 315, "center_y": 275, "value": 20, "remaining": 20, "crossings": 0}, {"row": 27, "col": 32, "center_x": 325, "center_y": 275, "value": 17, "remaining": 17, "crossings": 0}, {"row": 27, "col": 33, "center_x": 335, "center_y": 275, "value": 27, "remaining": 27, "crossings": 0}, {"row": 27, "col": 34, "center_x": 345, "center_y": 275, "value": 30, "remaining": 30, "crossings": 0}, {"row": 27, "col": 35, "center_x": 355, "center_y": 275, "value": 34, "remaining": 34, "crossings": 0}, {"row": 27, "col": 36, "center_x": 365, "center_y": 275, "value": 32, "remaining": 32, "crossings": 0}, {"row": 28, "col": 0, "center_x": 5, "center_y": 285, "value": 14, "remaining": 14, "crossings": 0}, {"row": 28, "col": 1, "center_x": 15, "center_y": 285, "value": 16, "remaining": 16, "crossings": 0}, {"row": 28, "col": 10, "center_x": 105, "center_y": 285, "value": 24, "remaining": 24, "crossings": 0}, {"row": 28, "col": 11, "center_x": 115, "center_y": 285, "value": 35, "remaining": 35, "crossings": 0}, {"row": 28, "col": 12, "center_x": 125, "center_y": 285, "value": 27, "remaining": 27, "crossings": 0}, {"row": 28, "col": 13, "center_x": 135, "center_y": 285, "value": 23, "remaining": 23, "crossings": 0}, {"row": 28, "col": 14, "center_x": 145, "center_y": 285, "value": 20, "remaining": 20, "crossings": 0}, {"row": 28, "col": 15, "center_x": 155, "center_y": 285, "value": 14, "remaining": 14, "crossings": 0}, {"row": 28, "col": 16, "center_x": 165, "center_y": 285, "value": 20, "remaining": 20, "crossings": 0}, {"row": 28, "col": 17, "center_x": 175, "center_y": 285, "value": 21, "remaining": 21, "crossings": 0}, {"row": 28, "col": 18, "center_x": 185, "center_y": 285, "value": 20, "remaining": 20, "crossings": 0}, {"row": 28, "col": 19, "center_x": 195, "center_y": 285, "value": 19, "remaining": 19, "crossings": 0}, {"row": 28, "col": 20, "center_x": 205, "center_y": 285, "value": 18, "remaining": 18, "crossings": 0}, {"row": 28, "col": 21, "center_x": 215, "center_y": 285, "value": 16, "remaining": 16, "crossings": 0}, {"row": 28, "col": 22, "center_x": 225, "center_y": 285, "value": 15, "remaining": 15, "crossings": 0}, {"row": 28, "col": 23, "center_x": 235, "center_y": 285, "value": 15, "remaining": 15, "crossings": 0}, {"row": 28, "col": 24, "center_x": 245, "center_y": 285, "value": 17, "remaining": 17, "crossings": 0}, {"row": 28, "col": 25, "center_x": 255, "center_y": 285, "value": 14, "remaining": 14, "crossings": 0}, {"row": 28, "col": 26, "center_x": 265, "center_y": 285, "value": 12, "remaining": 12, "crossings": 0}, {"row": 28, "col": 27, "center_x": 275, "center_y": 285, "value": 13, "remaining": 13, "crossings": 0}, {"row": 28, "col": 28, "center_x": 285, "center_y": 285, "value": 14, "remaining": 14, "crossings": 0}, {"row": 28, "col": 29, "center_x": 295, "center_y": 285, "value": 14, "remaining": 14, "crossings": 0}, {"row": 28, "col": 30, "center_x": 305, "center_y": 285, "value": 14, "remaining": 14, "crossings": 0}, {"row": 28, "col": 31, "center_x": 315, "center_y": 285, "value": 21, "remaining": 21, "crossings": 0}, {"row": 28, "col": 32, "center_x": 325, "center_y": 285, "value": 19, "remaining": 19, "crossings": 0}, {"row": 28, "col": 33, "center_x": 335, "center_y": 285, "value": 29, "remaining": 29, "crossings": 0}, {"row": 28, "col": 34, "center_x": 345, "center_y": 285, "value": 29, "remaining": 29, "crossings": 0}, {"row": 28, "col": 35, "center_x": 355, "center_y": 285, "value": 34, "remaining": 34, "crossings": 0}, {"row": 28, "col": 36, "center_x": 365, "center_y": 285, "value": 31, "remaining": 31, "crossings": 0}, {"row": 28, "col": 46, "center_x": 465, "center_y": 285, "value": 4, "remaining": 4, "crossings": 0}, {"row": 29, "col": 10, "center_x": 105, "center_y": 295, "value": 26, "remaining": 26, "crossings": 0}, {"row": 29, "col": 11, "center_x": 115, "center_y": 295, "value": 33, "remaining": 33, "crossings": 0}, {"row": 29, "col": 12, "center_x": 125, "center_y": 295, "value": 28, "remaining": 28, "crossings": 0}, {"row": 29, "col": 13, "center_x": 135, "center_y": 295, "value": 24, "remaining": 24, "crossings": 0}, {"row": 29, "col": 14, "center_x": 145, "center_y": 295, "value": 27, "remaining": 27, "crossings": 0}, {"row": 29, "col": 15, "center_x": 155, "center_y": 295, "value": 30, "remaining": 30, "crossings": 0}, {"row": 29, "col": 16, "center_x": 165, "center_y": 295, "value": 26, "remaining": 26, "crossings": 0}, {"row": 29, "col": 17, "center_x": 175, "center_y": 295, "value": 21, "remaining": 21, "crossings": 0}, {"row": 29, "col": 18, "center_x": 185, "center_y": 295, "value": 19, "remaining": 19, "crossings": 0}, {"row": 29, "col": 19, "center_x": 195, "center_y": 295, "value": 18, "remaining": 18, "crossings": 0}, {"row": 29, "col": 20, "center_x": 205, "center_y": 295, "value": 18, "remaining": 18, "crossings": 0}, {"row": 29, "col": 21, "center_x": 215, "center_y": 295, "value": 16, "remaining": 16, "crossings": 0}, {"row": 29, "col": 22, "center_x": 225, "center_y": 295, "value": 16, "remaining": 16, "crossings": 0}, {"row": 29, "col": 23, "center_x": 235, "center_y": 295, "value": 15, "remaining": 15, "crossings": 0}, {"row": 29, "col": 24, "center_x": 245, "center_y": 295, "value": 14, "remaining": 14, "crossings": 0}, {"row": 29, "col": 25, "center_x": 255, "center_y": 295, "value": 14, "remaining": 14, "crossings": 0}, {"row": 29, "col": 26, "center_x": 265, "center_y": 295, "value": 13, "remaining": 13, "crossings": 0}, {"row": 29, "col": 27, "center_x": 275, "center_y": 295, "value": 14, "remaining": 14, "crossings": 0}, {"row": 29, "col": 28, "center_x": 285, "center_y": 295, "value": 14, "remaining": 14, "crossings": 0}, {"row": 29, "col": 29, "center_x": 295, "center_y": 295, "value": 14, "remaining": 14, "crossings": 0}, {"row": 29, "col": 30, "center_x": 305, "center_y": 295, "value": 14, "remaining": 14, "crossings": 0}, {"row": 29, "col": 31, "center_x": 315, "center_y": 295, "value": 17, "remaining": 17, "crossings": 0}, {"row": 29, "col": 32, "center_x": 325, "center_y": 295, "value": 18, "remaining": 18, "crossings": 0}, {"row": 29, "col": 33, "center_x": 335, "center_y": 295, "value": 31, "remaining": 31, "crossings": 0}, {"row": 29, "col": 34, "center_x": 345, "center_y": 295, "value": 30, "remaining": 30, "crossings": 0}, {"row": 29, "col": 35, "center_x": 355, "center_y": 295, "value": 33, "remaining": 33, "crossings": 0}, {"row": 29, "col": 36, "center_x": 365, "center_y": 295, "value": 31, "remaining": 31, "crossings": 0}, {"row": 30, "col": 0, "center_x": 5, "center_y": 305, "value": 1, "remaining": 1, "crossings": 0}, {"row": 30, "col": 1, "center_x": 15, "center_y": 305, "value": 16, "remaining": 16, "crossings": 0}, {"row": 30, "col": 2, "center_x": 25, "center_y": 305, "value": 3, "remaining": 3, "crossings": 0}, {"row": 30, "col": 10, "center_x": 105, "center_y": 305, "value": 25, "remaining": 25, "crossings": 0}, {"row": 30, "col": 11, "center_x": 115, "center_y": 305, "value": 33, "remaining": 33, "crossings": 0}, {"row": 30, "col": 12, "center_x": 125, "center_y": 305, "value": 32, "remaining": 32, "crossings": 0}, {"row": 30, "col": 13, "center_x": 135, "center_y": 305, "value": 32, "remaining": 32, "crossings": 0}, {"row": 30, "col": 14, "center_x": 145, "center_y": 305, "value": 35, "remaining": 35, "crossings": 0}, {"row": 30, "col": 15, "center_x": 155, "center_y": 305, "value": 32, "remaining": 32, "crossings": 0}, {"row": 30, "col": 16, "center_x": 165, "center_y": 305, "value": 24, "remaining": 24, "crossings": 0}, {"row": 30, "col": 17, "center_x": 175, "center_y": 305, "value": 21, "remaining": 21, "crossings": 0}, {"row": 30, "col": 18, "center_x": 185, "center_y": 305, "value": 19, "remaining": 19, "crossings": 0}, {"row": 30, "col": 19, "center_x": 195, "center_y": 305, "value": 18, "remaining": 18, "crossings": 0}, {"row": 30, "col": 20, "center_x": 205, "center_y": 305, "value": 18, "remaining": 18, "crossings": 0}, {"row": 30, "col": 21, "center_x": 215, "center_y": 305, "value": 17, "remaining": 17, "crossings": 0}, {"row": 30, "col": 22, "center_x": 225, "center_y": 305, "value": 15, "remaining": 15, "crossings": 0}, {"row": 30, "col": 23, "center_x": 235, "center_y": 305, "value": 15, "remaining": 15, "crossings": 0}, {"row": 30, "col": 24, "center_x": 245, "center_y": 305, "value": 15, "remaining": 15, "crossings": 0}, {"row": 30, "col": 25, "center_x": 255, "center_y": 305, "value": 18, "remaining": 18, "crossings": 0}, {"row": 30, "col": 26, "center_x": 265, "center_y": 305, "value": 19, "remaining": 19, "crossings": 0}, {"row": 30, "col": 27, "center_x": 275, "center_y": 305, "value": 22, "remaining": 22, "crossings": 0}, {"row": 30, "col": 28, "center_x": 285, "center_y": 305, "value": 24, "remaining": 24, "crossings": 0}, {"row": 30, "col": 29, "center_x": 295, "center_y": 305, "value": 23, "remaining": 23, "crossings": 0}, {"row": 30, "col": 30, "center_x": 305, "center_y": 305, "value": 20, "remaining": 20, "crossings": 0}, {"row": 30, "col": 31, "center_x": 315, "center_y": 305, "value": 17, "remaining": 17, "crossings": 0}, {"row": 30, "col": 32, "center_x": 325, "center_y": 305, "value": 18, "remaining": 18, "crossings": 0}, {"row": 30, "col": 33, "center_x": 335, "center_y": 305, "value": 32, "remaining": 32, "crossings": 0}, {"row": 30, "col": 34, "center_x": 345, "center_y": 305, "value": 31, "remaining": 31, "crossings": 0}, {"row": 30, "col": 35, "center_x": 355, "center_y": 305, "value": 33, "remaining": 33, "crossings": 0}, {"row": 30, "col": 36, "center_x": 365, "center_y": 305, "value": 31, "remaining": 31, "crossings": 0}, {"row": 30, "col": 45, "center_x": 455, "center_y": 305, "value": 3, "remaining": 3, "crossings": 0}, {"row": 30, "col": 46, "center_x": 465, "center_y": 305, "value": 4, "remaining": 4, "crossings": 0}, {"row": 30, "col": 47, "center_x": 471, "center_y": 305, "value": 12, "remaining": 12, "crossings": 0}, {"row": 31, "col": 1, "center_x": 15, "center_y": 315, "value": 6, "remaining": 6, "crossings": 0}, {"row": 31, "col": 2, "center_x": 25, "center_y": 315, "value": 2, "remaining": 2, "crossings": 0}, {"row": 31, "col": 10, "center_x": 105, "center_y": 315, "value": 21, "remaining": 21, "crossings": 0}, {"row": 31, "col": 11, "center_x": 115, "center_y": 315, "value": 30, "remaining": 30, "crossings": 0}, {"row": 31, "col": 12, "center_x": 125, "center_y": 315, "value": 33, "remaining": 33, "crossings": 0}, {"row": 31, "col": 13, "center_x": 135, "center_y": 315, "value": 33, "remaining": 33, "crossings": 0}, {"row": 31, "col": 14, "center_x": 145, "center_y": 315, "value": 33, "remaining": 33, "crossings": 0}, {"row": 31, "col": 15, "center_x": 155, "center_y": 315, "value": 29, "remaining": 29, "crossings": 0}, {"row": 31, "col": 16, "center_x": 165, "center_y": 315, "value": 23, "remaining": 23, "crossings": 0}, {"row": 31, "col": 17, "center_x": 175, "center_y": 315, "value": 24, "remaining": 24, "crossings": 0}, {"row": 31, "col": 18, "center_x": 185, "center_y": 315, "value": 21, "remaining": 21, "crossings": 0}, {"row": 31, "col": 19, "center_x": 195, "center_y": 315, "value": 20, "remaining": 20, "crossings": 0}, {"row": 31, "col": 20, "center_x": 205, "center_y": 315, "value": 19, "remaining": 19, "crossings": 0}, {"row": 31, "col": 21, "center_x": 215, "center_y": 315, "value": 17, "remaining": 17, "crossings": 0}, {"row": 31, "col": 22, "center_x": 225, "center_y": 315, "value": 15, "remaining": 15, "crossings": 0}, {"row": 31, "col": 23, "center_x": 235, "center_y": 315, "value": 16, "remaining": 16, "crossings": 0}, {"row": 31, "col": 24, "center_x": 245, "center_y": 315, "value": 17, "remaining": 17, "crossings": 0}, {"row": 31, "col": 25, "center_x": 255, "center_y": 315, "value": 23, "remaining": 23, "crossings": 0}, {"row": 31, "col": 26, "center_x": 265, "center_y": 315, "value": 24, "remaining": 24, "crossings": 0}, {"row": 31, "col": 27, "center_x": 275, "center_y": 315, "value": 24, "remaining": 24, "crossings": 0}, {"row": 31, "col": 28, "center_x": 285, "center_y": 315, "value": 25, "remaining": 25, "crossings": 0}, {"row": 31, "col": 29, "center_x": 295, "center_y": 315, "value": 23, "remaining": 23, "crossings": 0}, {"row": 31, "col": 30, "center_x": 305, "center_y": 315, "value": 25, "remaining": 25, "crossings": 0}, {"row": 31, "col": 31, "center_x": 315, "center_y": 315, "value": 20, "remaining": 20, "crossings": 0}, {"row": 31, "col": 32, "center_x": 325, "center_y": 315, "value": 19, "remaining": 19, "crossings": 0}, {"row": 31, "col": 33, "center_x": 335, "center_y": 315, "value": 32, "remaining": 32, "crossings": 0}, {"row": 31, "col": 34, "center_x": 345, "center_y": 315, "value": 33, "remaining": 33, "crossings": 0}, {"row": 31, "col": 35, "center_x": 355, "center_y": 315, "value": 34, "remaining": 34, "crossings": 0}, {"row": 31, "col": 36, "center_x": 365, "center_y": 315, "value": 31, "remaining": 31, "crossings": 0}, {"row": 31, "col": 45, "center_x": 455, "center_y": 315, "value": 1, "remaining": 1, "crossings": 0}, {"row": 31, "col": 46, "center_x": 465, "center_y": 315, "value": 4, "remaining": 4, "crossings": 0}, {"row": 31, "col": 47, "center_x": 471, "center_y": 315, "value": 4, "remaining": 4, "crossings": 0}, {"row": 32, "col": 2, "center_x": 25, "center_y": 325, "value": 6, "remaining": 6, "crossings": 0}, {"row": 32, "col": 10, "center_x": 105, "center_y": 325, "value": 21, "remaining": 21, "crossings": 0}, {"row": 32, "col": 11, "center_x": 115, "center_y": 325, "value": 32, "remaining": 32, "crossings": 0}, {"row": 32, "col": 12, "center_x": 125, "center_y": 325, "value": 33, "remaining": 33, "crossings": 0}, {"row": 32, "col": 13, "center_x": 135, "center_y": 325, "value": 33, "remaining": 33, "crossings": 0}, {"row": 32, "col": 14, "center_x": 145, "center_y": 325, "value": 33, "remaining": 33, "crossings": 0}, {"row": 32, "col": 15, "center_x": 155, "center_y": 325, "value": 29, "remaining": 29, "crossings": 0}, {"row": 32, "col": 16, "center_x": 165, "center_y": 325, "value": 20, "remaining": 20, "crossings": 0}, {"row": 32, "col": 17, "center_x": 175, "center_y": 325, "value": 25, "remaining": 25, "crossings": 0}, {"row": 32, "col": 18, "center_x": 185, "center_y": 325, "value": 25, "remaining": 25, "crossings": 0}, {"row": 32, "col": 19, "center_x": 195, "center_y": 325, "value": 23, "remaining": 23, "crossings": 0}, {"row": 32, "col": 20, "center_x": 205, "center_y": 325, "value": 21, "remaining": 21, "crossings": 0}, {"row": 32, "col": 21, "center_x": 215, "center_y": 325, "value": 19, "remaining": 19, "crossings": 0}, {"row": 32, "col": 22, "center_x": 225, "center_y": 325, "value": 18, "remaining": 18, "crossings": 0}, {"row": 32, "col": 23, "center_x": 235, "center_y": 325, "value": 16, "remaining": 16, "crossings": 0}, {"row": 32, "col": 24, "center_x": 245, "center_y": 325, "value": 18, "remaining": 18, "crossings": 0}, {"row": 32, "col": 25, "center_x": 255, "center_y": 325, "value": 15, "remaining": 15, "crossings": 0}, {"row": 32, "col": 26, "center_x": 265, "center_y": 325, "value": 16, "remaining": 16, "crossings": 0}, {"row": 32, "col": 27, "center_x": 275, "center_y": 325, "value": 17, "remaining": 17, "crossings": 0}, {"row": 32, "col": 28, "center_x": 285, "center_y": 325, "value": 17, "remaining": 17, "crossings": 0}, {"row": 32, "col": 29, "center_x": 295, "center_y": 325, "value": 16, "remaining": 16, "crossings": 0}, {"row": 32, "col": 30, "center_x": 305, "center_y": 325, "value": 17, "remaining": 17, "crossings": 0}, {"row": 32, "col": 31, "center_x": 315, "center_y": 325, "value": 19, "remaining": 19, "crossings": 0}, {"row": 32, "col": 32, "center_x": 325, "center_y": 325, "value": 22, "remaining": 22, "crossings": 0}, {"row": 32, "col": 33, "center_x": 335, "center_y": 325, "value": 32, "remaining": 32, "crossings": 0}, {"row": 32, "col": 34, "center_x": 345, "center_y": 325, "value": 33, "remaining": 33, "crossings": 0}, {"row": 32, "col": 35, "center_x": 355, "center_y": 325, "value": 34, "remaining": 34, "crossings": 0}, {"row": 32, "col": 36, "center_x": 365, "center_y": 325, "value": 32, "remaining": 32, "crossings": 0}, {"row": 32, "col": 45, "center_x": 455, "center_y": 325, "value": 2, "remaining": 2, "crossings": 0}, {"row": 32, "col": 46, "center_x": 465, "center_y": 325, "value": 5, "remaining": 5, "crossings": 0}, {"row": 33, "col": 1, "center_x": 15, "center_y": 335, "value": 3, "remaining": 3, "crossings": 0}, {"row": 33, "col": 2, "center_x": 25, "center_y": 335, "value": 15, "remaining": 15, "crossings": 0}, {"row": 33, "col": 3, "center_x": 35, "center_y": 335, "value": 2, "remaining": 2, "crossings": 0}, {"row": 33, "col": 10, "center_x": 105, "center_y": 335, "value": 23, "remaining": 23, "crossings": 0}, {"row": 33, "col": 11, "center_x": 115, "center_y": 335, "value": 33, "remaining": 33, "crossings": 0}, {"row": 33, "col": 12, "center_x": 125, "center_y": 335, "value": 31, "remaining": 31, "crossings": 0}, {"row": 33, "col": 13, "center_x": 135, "center_y": 335, "value": 32, "remaining": 32, "crossings": 0}, {"row": 33, "col": 14, "center_x": 145, "center_y": 335, "value": 32, "remaining": 32, "crossings": 0}, {"row": 33, "col": 15, "center_x": 155, "center_y": 335, "value": 26, "remaining": 26, "crossings": 0}, {"row": 33, "col": 16, "center_x": 165, "center_y": 335, "value": 20, "remaining": 20, "crossings": 0}, {"row": 33, "col": 17, "center_x": 175, "center_y": 335, "value": 20, "remaining": 20, "crossings": 0}, {"row": 33, "col": 18, "center_x": 185, "center_y": 335, "value": 23, "remaining": 23, "crossings": 0}, {"row": 33, "col": 19, "center_x": 195, "center_y": 335, "value": 24, "remaining": 24, "crossings": 0}, {"row": 33, "col": 20, "center_x": 205, "center_y": 335, "value": 24, "remaining": 24, "crossings": 0}, {"row": 33, "col": 21, "center_x": 215, "center_y": 335, "value": 22, "remaining": 22, "crossings": 0}, {"row": 33, "col": 22, "center_x": 225, "center_y": 335, "value": 21, "remaining": 21, "crossings": 0}, {"row": 33, "col": 23, "center_x": 235, "center_y": 335, "value": 18, "remaining": 18, "crossings": 0}, {"row": 33, "col": 24, "center_x": 245, "center_y": 335, "value": 17, "remaining": 17, "crossings": 0}, {"row": 33, "col": 25, "center_x": 255, "center_y": 335, "value": 16, "remaining": 16, "crossings": 0}, {"row": 33, "col": 26, "center_x": 265, "center_y": 335, "value": 17, "remaining": 17, "crossings": 0}, {"row": 33, "col": 27, "center_x": 275, "center_y": 335, "value": 20, "remaining": 20, "crossings": 0}, {"row": 33, "col": 28, "center_x": 285, "center_y": 335, "value": 25, "remaining": 25, "crossings": 0}, {"row": 33, "col": 29, "center_x": 295, "center_y": 335, "value": 24, "remaining": 24, "crossings": 0}, {"row": 33, "col": 30, "center_x": 305, "center_y": 335, "value": 19, "remaining": 19, "crossings": 0}, {"row": 33, "col": 31, "center_x": 315, "center_y": 335, "value": 20, "remaining": 20, "crossings": 0}, {"row": 33, "col": 32, "center_x": 325, "center_y": 335, "value": 29, "remaining": 29, "crossings": 0}, {"row": 33, "col": 33, "center_x": 335, "center_y": 335, "value": 33, "remaining": 33, "crossings": 0}, {"row": 33, "col": 34, "center_x": 345, "center_y": 335, "value": 33, "remaining": 33, "crossings": 0}, {"row": 33, "col": 35, "center_x": 355, "center_y": 335, "value": 34, "remaining": 34, "crossings": 0}, {"row": 33, "col": 36, "center_x": 365, "center_y": 335, "value": 33, "remaining": 33, "crossings": 0}, {"row": 33, "col": 44, "center_x": 445, "center_y": 335, "value": 4, "remaining": 4, "crossings": 0}, {"row": 33, "col": 45, "center_x": 455, "center_y": 335, "value": 4, "remaining": 4, "crossings": 0}, {"row": 33, "col": 46, "center_x": 465, "center_y": 335, "value": 7, "remaining": 7, "crossings": 0}, {"row": 34, "col": 3, "center_x": 35, "center_y": 345, "value": 4, "remaining": 4, "crossings": 0}, {"row": 34, "col": 4, "center_x": 45, "center_y": 345, "value": 2, "remaining": 2, "crossings": 0}, {"row": 34, "col": 10, "center_x": 105, "center_y": 345, "value": 23, "remaining": 23, "crossings": 0}, {"row": 34, "col": 11, "center_x": 115, "center_y": 345, "value": 33, "remaining": 33, "crossings": 0}, {"row": 34, "col": 12, "center_x": 125, "center_y": 345, "value": 32, "remaining": 32, "crossings": 0}, {"row": 34, "col": 13, "center_x": 135, "center_y": 345, "value": 32, "remaining": 32, "crossings": 0}, {"row": 34, "col": 14, "center_x": 145, "center_y": 345, "value": 35, "remaining": 35, "crossings": 0}, {"row": 34, "col": 15, "center_x": 155, "center_y": 345, "value": 14, "remaining": 14, "crossings": 0}, {"row": 34, "col": 16, "center_x": 165, "center_y": 345, "value": 19, "remaining": 19, "crossings": 0}, {"row": 34, "col": 17, "center_x": 175, "center_y": 345, "value": 19, "remaining": 19, "crossings": 0}, {"row": 34, "col": 18, "center_x": 185, "center_y": 345, "value": 20, "remaining": 20, "crossings": 0}, {"row": 34, "col": 19, "center_x": 195, "center_y": 345, "value": 21, "remaining": 21, "crossings": 0}, {"row": 34, "col": 20, "center_x": 205, "center_y": 345, "value": 22, "remaining": 22, "crossings": 0}, {"row": 34, "col": 21, "center_x": 215, "center_y": 345, "value": 23, "remaining": 23, "crossings": 0}, {"row": 34, "col": 22, "center_x": 225, "center_y": 345, "value": 22, "remaining": 22, "crossings": 0}, {"row": 34, "col": 23, "center_x": 235, "center_y": 345, "value": 20, "remaining": 20, "crossings": 0}, {"row": 34, "col": 24, "center_x": 245, "center_y": 345, "value": 18, "remaining": 18, "crossings": 0}, {"row": 34, "col": 25, "center_x": 255, "center_y": 345, "value": 15, "remaining": 15, "crossings": 0}, {"row": 34, "col": 26, "center_x": 265, "center_y": 345, "value": 16, "remaining": 16, "crossings": 0}, {"row": 34, "col": 27, "center_x": 275, "center_y": 345, "value": 19, "remaining": 19, "crossings": 0}, {"row": 34, "col": 28, "center_x": 285, "center_y": 345, "value": 20, "remaining": 20, "crossings": 0}, {"row": 34, "col": 29, "center_x": 295, "center_y": 345, "value": 20, "remaining": 20, "crossings": 0}, {"row": 34, "col": 30, "center_x": 305, "center_y": 345, "value": 17, "remaining": 17, "crossings": 0}, {"row": 34, "col": 31, "center_x": 315, "center_y": 345, "value": 25, "remaining": 25, "crossings": 0}, {"row": 34, "col": 32, "center_x": 325, "center_y": 345, "value": 33, "remaining": 33, "crossings": 0}, {"row": 34, "col": 33, "center_x": 335, "center_y": 345, "value": 36, "remaining": 36, "crossings": 0}, {"row": 34, "col": 34, "center_x": 345, "center_y": 345, "value": 35, "remaining": 35, "crossings": 0}, {"row": 34, "col": 35, "center_x": 355, "center_y": 345, "value": 36, "remaining": 36, "crossings": 0}, {"row": 34, "col": 36, "center_x": 365, "center_y": 345, "value": 33, "remaining": 33, "crossings": 0}, {"row": 34, "col": 44, "center_x": 445, "center_y": 345, "value": 2, "remaining": 2, "crossings": 0}, {"row": 34, "col": 45, "center_x": 455, "center_y": 345, "value": 5, "remaining": 5, "crossings": 0}, {"row": 35, "col": 2, "center_x": 25, "center_y": 355, "value": 2, "remaining": 2, "crossings": 0}, {"row": 35, "col": 3, "center_x": 35, "center_y": 355, "value": 15, "remaining": 15, "crossings": 0}, {"row": 35, "col": 4, "center_x": 45, "center_y": 355, "value": 4, "remaining": 4, "crossings": 0}, {"row": 35, "col": 10, "center_x": 105, "center_y": 355, "value": 22, "remaining": 22, "crossings": 0}, {"row": 35, "col": 11, "center_x": 115, "center_y": 355, "value": 32, "remaining": 32, "crossings": 0}, {"row": 35, "col": 12, "center_x": 125, "center_y": 355, "value": 32, "remaining": 32, "crossings": 0}, {"row": 35, "col": 13, "center_x": 135, "center_y": 355, "value": 40, "remaining": 40, "crossings": 0}, {"row": 35, "col": 14, "center_x": 145, "center_y": 355, "value": 30, "remaining": 30, "crossings": 0}, {"row": 35, "col": 15, "center_x": 155, "center_y": 355, "value": 6, "remaining": 6, "crossings": 0}, {"row": 35, "col": 16, "center_x": 165, "center_y": 355, "value": 18, "remaining": 18, "crossings": 0}, {"row": 35, "col": 17, "center_x": 175, "center_y": 355, "value": 19, "remaining": 19, "crossings": 0}, {"row": 35, "col": 18, "center_x": 185, "center_y": 355, "value": 20, "remaining": 20, "crossings": 0}, {"row": 35, "col": 19, "center_x": 195, "center_y": 355, "value": 20, "remaining": 20, "crossings": 0}, {"row": 35, "col": 20, "center_x": 205, "center_y": 355, "value": 22, "remaining": 22, "crossings": 0}, {"row": 35, "col": 21, "center_x": 215, "center_y": 355, "value": 23, "remaining": 23, "crossings": 0}, {"row": 35, "col": 22, "center_x": 225, "center_y": 355, "value": 25, "remaining": 25, "crossings": 0}, {"row": 35, "col": 23, "center_x": 235, "center_y": 355, "value": 24, "remaining": 24, "crossings": 0}, {"row": 35, "col": 24, "center_x": 245, "center_y": 355, "value": 18, "remaining": 18, "crossings": 0}, {"row": 35, "col": 25, "center_x": 255, "center_y": 355, "value": 15, "remaining": 15, "crossings": 0}, {"row": 35, "col": 26, "center_x": 265, "center_y": 355, "value": 15, "remaining": 15, "crossings": 0}, {"row": 35, "col": 27, "center_x": 275, "center_y": 355, "value": 15, "remaining": 15, "crossings": 0}, {"row": 35, "col": 28, "center_x": 285, "center_y": 355, "value": 16, "remaining": 16, "crossings": 0}, {"row": 35, "col": 29, "center_x": 295, "center_y": 355, "value": 13, "remaining": 13, "crossings": 0}, {"row": 35, "col": 30, "center_x": 305, "center_y": 355, "value": 17, "remaining": 17, "crossings": 0}, {"row": 35, "col": 31, "center_x": 315, "center_y": 355, "value": 32, "remaining": 32, "crossings": 0}, {"row": 35, "col": 32, "center_x": 325, "center_y": 355, "value": 34, "remaining": 34, "crossings": 0}, {"row": 35, "col": 33, "center_x": 335, "center_y": 355, "value": 36, "remaining": 36, "crossings": 0}, {"row": 35, "col": 34, "center_x": 345, "center_y": 355, "value": 36, "remaining": 36, "crossings": 0}, {"row": 35, "col": 35, "center_x": 355, "center_y": 355, "value": 36, "remaining": 36, "crossings": 0}, {"row": 35, "col": 36, "center_x": 365, "center_y": 355, "value": 33, "remaining": 33, "crossings": 0}, {"row": 35, "col": 43, "center_x": 435, "center_y": 355, "value": 4, "remaining": 4, "crossings": 0}, {"row": 35, "col": 44, "center_x": 445, "center_y": 355, "value": 6, "remaining": 6, "crossings": 0}, {"row": 35, "col": 45, "center_x": 455, "center_y": 355, "value": 11, "remaining": 11, "crossings": 0}, {"row": 36, "col": 4, "center_x": 45, "center_y": 365, "value": 3, "remaining": 3, "crossings": 0}, {"row": 36, "col": 5, "center_x": 55, "center_y": 365, "value": 3, "remaining": 3, "crossings": 0}, {"row": 36, "col": 10, "center_x": 105, "center_y": 365, "value": 22, "remaining": 22, "crossings": 0}, {"row": 36, "col": 11, "center_x": 115, "center_y": 365, "value": 32, "remaining": 32, "crossings": 0}, {"row": 36, "col": 12, "center_x": 125, "center_y": 365, "value": 36, "remaining": 36, "crossings": 0}, {"row": 36, "col": 13, "center_x": 135, "center_y": 365, "value": 43, "remaining": 43, "crossings": 0}, {"row": 36, "col": 14, "center_x": 145, "center_y": 365, "value": 22, "remaining": 22, "crossings": 0}, {"row": 36, "col": 15, "center_x": 155, "center_y": 365, "value": 5, "remaining": 5, "crossings": 0}, {"row": 36, "col": 16, "center_x": 165, "center_y": 365, "value": 8, "remaining": 8, "crossings": 0}, {"row": 36, "col": 17, "center_x": 175, "center_y": 365, "value": 19, "remaining": 19, "crossings": 0}, {"row": 36, "col": 18, "center_x": 185, "center_y": 365, "value": 20, "remaining": 20, "crossings": 0}, {"row": 36, "col": 19, "center_x": 195, "center_y": 365, "value": 21, "remaining": 21, "crossings": 0}, {"row": 36, "col": 20, "center_x": 205, "center_y": 365, "value": 22, "remaining": 22, "crossings": 0}, {"row": 36, "col": 21, "center_x": 215, "center_y": 365, "value": 24, "remaining": 24, "crossings": 0}, {"row": 36, "col": 22, "center_x": 225, "center_y": 365, "value": 27, "remaining": 27, "crossings": 0}, {"row": 36, "col": 23, "center_x": 235, "center_y": 365, "value": 29, "remaining": 29, "crossings": 0}, {"row": 36, "col": 24, "center_x": 245, "center_y": 365, "value": 24, "remaining": 24, "crossings": 0}, {"row": 36, "col": 25, "center_x": 255, "center_y": 365, "value": 21, "remaining": 21, "crossings": 0}, {"row": 36, "col": 26, "center_x": 265, "center_y": 365, "value": 22, "remaining": 22, "crossings": 0}, {"row": 36, "col": 27, "center_x": 275, "center_y": 365, "value": 23, "remaining": 23, "crossings": 0}, {"row": 36, "col": 28, "center_x": 285, "center_y": 365, "value": 23, "remaining": 23, "crossings": 0}, {"row": 36, "col": 29, "center_x": 295, "center_y": 365, "value": 22, "remaining": 22, "crossings": 0}, {"row": 36, "col": 30, "center_x": 305, "center_y": 365, "value": 27, "remaining": 27, "crossings": 0}, {"row": 36, "col": 31, "center_x": 315, "center_y": 365, "value": 35, "remaining": 35, "crossings": 0}, {"row": 36, "col": 32, "center_x": 325, "center_y": 365, "value": 35, "remaining": 35, "crossings": 0}, {"row": 36, "col": 33, "center_x": 335, "center_y": 365, "value": 36, "remaining": 36, "crossings": 0}, {"row": 36, "col": 34, "center_x": 345, "center_y": 365, "value": 36, "remaining": 36, "crossings": 0}, {"row": 36, "col": 35, "center_x": 355, "center_y": 365, "value": 36, "remaining": 36, "crossings": 0}, {"row": 36, "col": 36, "center_x": 365, "center_y": 365, "value": 33, "remaining": 33, "crossings": 0}, {"row": 36, "col": 42, "center_x": 425, "center_y": 365, "value": 1, "remaining": 1, "crossings": 0}, {"row": 36, "col": 43, "center_x": 435, "center_y": 365, "value": 4, "remaining": 4, "crossings": 0}, {"row": 36, "col": 44, "center_x": 445, "center_y": 365, "value": 4, "remaining": 4, "crossings": 0}, {"row": 37, "col": 4, "center_x": 45, "center_y": 375, "value": 13, "remaining": 13, "crossings": 0}, {"row": 37, "col": 5, "center_x": 55, "center_y": 375, "value": 8, "remaining": 8, "crossings": 0}, {"row": 37, "col": 10, "center_x": 105, "center_y": 375, "value": 23, "remaining": 23, "crossings": 0}, {"row": 37, "col": 11, "center_x": 115, "center_y": 375, "value": 36, "remaining": 36, "crossings": 0}, {"row": 37, "col": 12, "center_x": 125, "center_y": 375, "value": 43, "remaining": 43, "crossings": 0}, {"row": 37, "col": 13, "center_x": 135, "center_y": 375, "value": 44, "remaining": 44, "crossings": 0}, {"row": 37, "col": 14, "center_x": 145, "center_y": 375, "value": 33, "remaining": 33, "crossings": 0}, {"row": 37, "col": 15, "center_x": 155, "center_y": 375, "value": 5, "remaining": 5, "crossings": 0}, {"row": 37, "col": 16, "center_x": 165, "center_y": 375, "value": 4, "remaining": 4, "crossings": 0}, {"row": 37, "col": 17, "center_x": 175, "center_y": 375, "value": 10, "remaining": 10, "crossings": 0}, {"row": 37, "col": 18, "center_x": 185, "center_y": 375, "value": 21, "remaining": 21, "crossings": 0}, {"row": 37, "col": 19, "center_x": 195, "center_y": 375, "value": 21, "remaining": 21, "crossings": 0}, {"row": 37, "col": 20, "center_x": 205, "center_y": 375, "value": 22, "remaining": 22, "crossings": 0}, {"row": 37, "col": 21, "center_x": 215, "center_y": 375, "value": 24, "remaining": 24, "crossings": 0}, {"row": 37, "col": 22, "center_x": 225, "center_y": 375, "value": 27, "remaining": 27, "crossings": 0}, {"row": 37, "col": 23, "center_x": 235, "center_y": 375, "value": 29, "remaining": 29, "crossings": 0}, {"row": 37, "col": 24, "center_x": 245, "center_y": 375, "value": 29, "remaining": 29, "crossings": 0}, {"row": 37, "col": 25, "center_x": 255, "center_y": 375, "value": 28, "remaining": 28, "crossings": 0}, {"row": 37, "col": 26, "center_x": 265, "center_y": 375, "value": 31, "remaining": 31, "crossings": 0}, {"row": 37, "col": 27, "center_x": 275, "center_y": 375, "value": 30, "remaining": 30, "crossings": 0}, {"row": 37, "col": 28, "center_x": 285, "center_y": 375, "value": 30, "remaining": 30, "crossings": 0}, {"row": 37, "col": 29, "center_x": 295, "center_y": 375, "value": 32, "remaining": 32, "crossings": 0}, {"row": 37, "col": 30, "center_x": 305, "center_y": 375, "value": 34, "remaining": 34, "crossings": 0}, {"row": 37, "col": 31, "center_x": 315, "center_y": 375, "value": 36, "remaining": 36, "crossings": 0}, {"row": 37, "col": 32, "center_x": 325, "center_y": 375, "value": 35, "remaining": 35, "crossings": 0}, {"row": 37, "col": 33, "center_x": 335, "center_y": 375, "value": 36, "remaining": 36, "crossings": 0}, {"row": 37, "col": 34, "center_x": 345, "center_y": 375, "value": 36, "remaining": 36, "crossings": 0}, {"row": 37, "col": 35, "center_x": 355, "center_y": 375, "value": 37, "remaining": 37, "crossings": 0}, {"row": 37, "col": 36, "center_x": 365, "center_y": 375, "value": 33, "remaining": 33, "crossings": 0}, {"row": 37, "col": 42, "center_x": 425, "center_y": 375, "value": 3, "remaining": 3, "crossings": 0}, {"row": 37, "col": 43, "center_x": 435, "center_y": 375, "value": 8, "remaining": 8, "crossings": 0}, {"row": 37, "col": 44, "center_x": 445, "center_y": 375, "value": 8, "remaining": 8, "crossings": 0}, {"row": 38, "col": 5, "center_x": 55, "center_y": 385, "value": 1, "remaining": 1, "crossings": 0}, {"row": 38, "col": 6, "center_x": 65, "center_y": 385, "value": 9, "remaining": 9, "crossings": 0}, {"row": 38, "col": 10, "center_x": 105, "center_y": 385, "value": 23, "remaining": 23, "crossings": 0}, {"row": 38, "col": 11, "center_x": 115, "center_y": 385, "value": 39, "remaining": 39, "crossings": 0}, {"row": 38, "col": 12, "center_x": 125, "center_y": 385, "value": 39, "remaining": 39, "crossings": 0}, {"row": 38, "col": 13, "center_x": 135, "center_y": 385, "value": 39, "remaining": 39, "crossings": 0}, {"row": 38, "col": 14, "center_x": 145, "center_y": 385, "value": 39, "remaining": 39, "crossings": 0}, {"row": 38, "col": 15, "center_x": 155, "center_y": 385, "value": 8, "remaining": 8, "crossings": 0}, {"row": 38, "col": 16, "center_x": 165, "center_y": 385, "value": 3, "remaining": 3, "crossings": 0}, {"row": 38, "col": 17, "center_x": 175, "center_y": 385, "value": 3, "remaining": 3, "crossings": 0}, {"row": 38, "col": 18, "center_x": 185, "center_y": 385, "value": 8, "remaining": 8, "crossings": 0}, {"row": 38, "col": 19, "center_x": 195, "center_y": 385, "value": 19, "remaining": 19, "crossings": 0}, {"row": 38, "col": 20, "center_x": 205, "center_y": 385, "value": 20, "remaining": 20, "crossings": 0}, {"row": 38, "col": 21, "center_x": 215, "center_y": 385, "value": 20, "remaining": 20, "crossings": 0}, {"row": 38, "col": 22, "center_x": 225, "center_y": 385, "value": 18, "remaining": 18, "crossings": 0}, {"row": 38, "col": 23, "center_x": 235, "center_y": 385, "value": 18, "remaining": 18, "crossings": 0}, {"row": 38, "col": 24, "center_x": 245, "center_y": 385, "value": 21, "remaining": 21, "crossings": 0}, {"row": 38, "col": 25, "center_x": 255, "center_y": 385, "value": 29, "remaining": 29, "crossings": 0}, {"row": 38, "col": 26, "center_x": 265, "center_y": 385, "value": 29, "remaining": 29, "crossings": 0}, {"row": 38, "col": 27, "center_x": 275, "center_y": 385, "value": 19, "remaining": 19, "crossings": 0}, {"row": 38, "col": 28, "center_x": 285, "center_y": 385, "value": 20, "remaining": 20, "crossings": 0}, {"row": 38, "col": 29, "center_x": 295, "center_y": 385, "value": 30, "remaining": 30, "crossings": 0}, {"row": 38, "col": 30, "center_x": 305, "center_y": 385, "value": 29, "remaining": 29, "crossings": 0}, {"row": 38, "col": 31, "center_x": 315, "center_y": 385, "value": 32, "remaining": 32, "crossings": 0}, {"row": 38, "col": 32, "center_x": 325, "center_y": 385, "value": 33, "remaining": 33, "crossings": 0}, {"row": 38, "col": 33, "center_x": 335, "center_y": 385, "value": 32, "remaining": 32, "crossings": 0}, {"row": 38, "col": 34, "center_x": 345, "center_y": 385, "value": 33, "remaining": 33, "crossings": 0}, {"row": 38, "col": 35, "center_x": 355, "center_y": 385, "value": 33, "remaining": 33, "crossings": 0}, {"row": 38, "col": 36, "center_x": 365, "center_y": 385, "value": 30, "remaining": 30, "crossings": 0}, {"row": 38, "col": 40, "center_x": 405, "center_y": 385, "value": 2, "remaining": 2, "crossings": 0}, {"row": 38, "col": 41, "center_x": 415, "center_y": 385, "value": 3, "remaining": 3, "crossings": 0}, {"row": 38, "col": 42, "center_x": 425, "center_y": 385, "value": 6, "remaining": 6, "crossings": 0}, {"row": 39, "col": 5, "center_x": 55, "center_y": 395, "value": 4, "remaining": 4, "crossings": 0}, {"row": 39, "col": 6, "center_x": 65, "center_y": 395, "value": 8, "remaining": 8, "crossings": 0}, {"row": 39, "col": 41, "center_x": 415, "center_y": 395, "value": 3, "remaining": 3, "crossings": 0}, {"row": 39, "col": 42, "center_x": 425, "center_y": 395, "value": 11, "remaining": 11, "crossings": 0}, {"row": 39, "col": 43, "center_x": 435, "center_y": 395, "value": 2, "remaining": 2, "crossings": 0}, {"row": 40, "col": 7, "center_x": 75, "center_y": 405, "value": 12, "remaining": 12, "crossings": 0}, {"row": 40, "col": 8, "center_x": 85, "center_y": 405, "value": 9, "remaining": 9, "crossings": 0}, {"row": 40, "col": 39, "center_x": 395, "center_y": 405, "value": 4, "remaining": 4, "crossings": 0}, {"row": 40, "col": 40, "center_x": 405, "center_y": 405, "value": 10, "remaining": 10, "crossings": 0}, {"row": 40, "col": 41, "center_x": 415, "center_y": 405, "value": 6, "remaining": 6, "crossings": 0}, {"row": 41, "col": 7, "center_x": 75, "center_y": 415, "value": 2, "remaining": 2, "crossings": 0}, {"row": 41, "col": 8, "center_x": 85, "center_y": 415, "value": 4, "remaining": 4, "crossings": 0}, {"row": 41, "col": 9, "center_x": 95, "center_y": 415, "value": 2, "remaining": 2, "crossings": 0}, {"row": 41, "col": 37, "center_x": 375, "center_y": 415, "value": 1, "remaining": 1, "crossings": 0}, {"row": 41, "col": 40, "center_x": 405, "center_y": 415, "value": 6, "remaining": 6, "crossings": 0}, {"row": 41, "col": 41, "center_x": 415, "center_y": 415, "value": 3, "remaining": 3, "crossings": 0}, {"row": 42, "col": 9, "center_x": 95, "center_y": 425, "value": 14, "remaining": 14, "crossings": 0}, {"row": 42, "col": 10, "center_x": 105, "center_y": 425, "value": 12, "remaining": 12, "crossings": 0}, {"row": 42, "col": 37, "center_x": 375, "center_y": 425, "value": 3, "remaining": 3, "crossings": 0}, {"row": 42, "col": 38, "center_x": 385, "center_y": 425, "value": 12, "remaining": 12, "crossings": 0}, {"row": 42, "col": 39, "center_x": 395, "center_y": 425, "value": 14, "remaining": 14, "crossings": 0}, {"row": 43, "col": 11, "center_x": 115, "center_y": 435, "value": 12, "remaining": 12, "crossings": 0}, {"row": 43, "col": 12, "center_x": 125, "center_y": 435, "value": 11, "remaining": 11, "crossings": 0}, {"row": 43, "col": 35, "center_x": 355, "center_y": 435, "value": 4, "remaining": 4, "crossings": 0}, {"row": 43, "col": 36, "center_x": 365, "center_y": 435, "value": 8, "remaining": 8, "crossings": 0}, {"row": 43, "col": 37, "center_x": 375, "center_y": 435, "value": 8, "remaining": 8, "crossings": 0}, {"row": 44, "col": 11, "center_x": 115, "center_y": 445, "value": 4, "remaining": 4, "crossings": 0}, {"row": 44, "col": 12, "center_x": 125, "center_y": 445, "value": 3, "remaining": 3, "crossings": 0}, {"row": 44, "col": 13, "center_x": 135, "center_y": 445, "value": 10, "remaining": 10, "crossings": 0}, {"row": 44, "col": 14, "center_x": 145, "center_y": 445, "value": 7, "remaining": 7, "crossings": 0}, {"row": 44, "col": 33, "center_x": 335, "center_y": 445, "value": 4, "remaining": 4, "crossings": 0}, {"row": 44, "col": 34, "center_x": 345, "center_y": 445, "value": 7, "remaining": 7, "crossings": 0}, {"row": 44, "col": 35, "center_x": 355, "center_y": 445, "value": 9, "remaining": 9, "crossings": 0}, {"row": 44, "col": 36, "center_x": 365, "center_y": 445, "value": 4, "remaining": 4, "crossings": 0}, {"row": 44, "col": 37, "center_x": 375, "center_y": 445, "value": 1, "remaining": 1, "crossings": 0}, {"row": 45, "col": 13, "center_x": 135, "center_y": 455, "value": 5, "remaining": 5, "crossings": 0}, {"row": 45, "col": 14, "center_x": 145, "center_y": 455, "value": 2, "remaining": 2, "crossings": 0}, {"row": 45, "col": 15, "center_x": 155, "center_y": 455, "value": 9, "remaining": 9, "crossings": 0}, {"row": 45, "col": 16, "center_x": 165, "center_y": 455, "value": 11, "remaining": 11, "crossings": 0}, {"row": 45, "col": 17, "center_x": 175, "center_y": 455, "value": 2, "remaining": 2, "crossings": 0}, {"row": 45, "col": 29, "center_x": 295, "center_y": 455, "value": 3, "remaining": 3, "crossings": 0}, {"row": 45, "col": 31, "center_x": 315, "center_y": 455, "value": 4, "remaining": 4, "crossings": 0}, {"row": 45, "col": 32, "center_x": 325, "center_y": 455, "value": 11, "remaining": 11, "crossings": 0}, {"row": 45, "col": 33, "center_x": 335, "center_y": 455, "value": 6, "remaining": 6, "crossings": 0}, {"row": 45, "col": 34, "center_x": 345, "center_y": 455, "value": 7, "remaining": 7, "crossings": 0}, {"row": 45, "col": 35, "center_x": 355, "center_y": 455, "value": 5, "remaining": 5, "crossings": 0}, {"row": 46, "col": 15, "center_x": 155, "center_y": 465, "value": 4, "remaining": 4, "crossings": 0}, {"row": 46, "col": 16, "center_x": 165, "center_y": 465, "value": 5, "remaining": 5, "crossings": 0}, {"row": 46, "col": 17, "center_x": 175, "center_y": 465, "value": 3, "remaining": 3, "crossings": 0}, {"row": 46, "col": 18, "center_x": 185, "center_y": 465, "value": 19, "remaining": 19, "crossings": 0}, {"row": 46, "col": 19, "center_x": 195, "center_y": 465, "value": 4, "remaining": 4, "crossings": 0}, {"row": 46, "col": 20, "center_x": 205, "center_y": 465, "value": 10, "remaining": 10, "crossings": 0}, {"row": 46, "col": 21, "center_x": 215, "center_y": 465, "value": 5, "remaining": 5, "crossings": 0}, {"row": 46, "col": 22, "center_x": 225, "center_y": 465, "value": 8, "remaining": 8, "crossings": 0}, {"row": 46, "col": 23, "center_x": 235, "center_y": 465, "value": 4, "remaining": 4, "crossings": 0}, {"row": 46, "col": 24, "center_x": 245, "center_y": 465, "value": 4, "remaining": 4, "crossings": 0}, {"row": 46, "col": 25, "center_x": 255, "center_y": 465, "value": 6, "remaining": 6, "crossings": 0}, {"row": 46, "col": 26, "center_x": 265, "center_y": 465, "value": 3, "remaining": 3, "crossings": 0}, {"row": 46, "col": 27, "center_x": 275, "center_y": 465, "value": 9, "remaining": 9, "crossings": 0}, {"row": 46, "col": 28, "center_x": 285, "center_y": 465, "value": 4, "remaining": 4, "crossings": 0}, {"row": 46, "col": 29, "center_x": 295, "center_y": 465, "value": 6, "remaining": 6, "crossings": 0}, {"row": 46, "col": 30, "center_x": 305, "center_y": 465, "value": 18, "remaining": 18, "crossings": 0}, {"row": 46, "col": 31, "center_x": 315, "center_y": 465, "value": 3, "remaining": 3, "crossings": 0}, {"row": 46, "col": 32, "center_x": 325, "center_y": 465, "value": 6, "remaining": 6, "crossings": 0}, {"row": 46, "col": 33, "center_x": 335, "center_y": 465, "value": 2, "remaining": 2, "crossings": 0}, {"row": 47, "col": 18, "center_x": 185, "center_y": 471, "value": 2, "remaining": 2, "crossings": 0}, {"row": 47, "col": 20, "center_x": 205, "center_y": 471, "value": 9, "remaining": 9, "crossings": 0}, {"row": 47, "col": 21, "center_x": 215, "center_y": 471, "value": 20, "remaining": 20, "crossings": 0}, {"row": 47, "col": 22, "center_x": 225, "center_y": 471, "value": 10, "remaining": 10, "crossings": 0}, {"row": 47, "col": 23, "center_x": 235, "center_y": 471, "value": 11, "remaining": 11, "crossings": 0}, {"row": 47, "col": 25, "center_x": 255, "center_y": 471, "value": 12, "remaining": 12, "crossings": 0}, {"row": 47, "col": 26, "center_x": 265, "center_y": 471, "value": 6, "remaining": 6, "crossings": 0}, {"row": 47, "col": 27, "center_x": 275, "center_y": 471, "value": 6, "remaining": 6, "crossings": 0}, {"row": 47, "col": 28, "center_x": 285, "center_y": 471, "value": 7, "remaining": 7, "crossings": 0}, {"row": 47, "col": 29, "center_x": 295, "center_y": 471, "value": 1, "remaining": 1, "crossings": 0}, {"row": 47, "col": 30, "center_x": 305, "center_y": 471, "value": 1, "remaining": 1, "crossings": 0}]}