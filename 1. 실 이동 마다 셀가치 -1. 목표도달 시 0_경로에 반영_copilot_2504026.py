import tkinter as tk
import numpy as np
import math
import cv2
from tkinter import simpledialog

def ask_canvas_type():
    """
    tkinter를 사용하여 캔버스 타입을 GUI로 선택합니다.
    """
    root = tk.Tk()
    root.title("캔버스 타입 선택")
    root.geometry("400x500")
    
    # 선택된 캔버스 타입을 저장할 변수
    selected_type = tk.StringVar()
    
    # 캔버스 타입 목록
    canvas_types = [
        "사각형",
        "정삼각형",
        "동그라미",
        "별모양",
        "6각형",
        "8각형",
        "12각형"
    ]
    
    # 각 타입에 대한 설명
    descriptions = {
        "사각형": "가장 일반적인 형태의 캔버스",
        "정삼각형": "세 개의 동일한 변을 가진 삼각형",
        "동그라미": "원형 캔버스",
        "별모양": "5개의 꼭지점을 가진 별 모양",
        "6각형": "6개의 동일한 변을 가진 정육각형",
        "8각형": "8개의 동일한 변을 가진 정팔각형",
        "12각형": "12개의 동일한 변을 가진 정십이각형"
    }
    
    # 프레임 생성
    frame = tk.Frame(root)
    frame.pack(pady=20)
    
    # 제목 레이블
    title_label = tk.Label(frame, text="캔버스 타입을 선택하세요", font=("Arial", 14, "bold"))
    title_label.pack(pady=10)
    
    # 각 타입에 대한 라디오 버튼 생성
    for canvas_type in canvas_types:
        radio_frame = tk.Frame(frame)
        radio_frame.pack(fill="x", pady=5)
        
        radio = tk.Radiobutton(radio_frame, text=canvas_type, 
                             variable=selected_type, value=canvas_type,
                             font=("Arial", 12))
        radio.pack(side="left", padx=10)
        
        desc_label = tk.Label(radio_frame, text=descriptions[canvas_type],
                            font=("Arial", 10), fg="gray")
        desc_label.pack(side="left", padx=10)
    
    # 확인 버튼
    def on_confirm():
        if selected_type.get():
            root.quit()
    
    confirm_button = tk.Button(frame, text="확인", command=on_confirm,
                             font=("Arial", 12), width=10)
    confirm_button.pack(pady=20)
    
    # 창을 중앙에 위치
    root.update_idletasks()
    width = root.winfo_width()
    height = root.winfo_height()
    x = (root.winfo_screenwidth() // 2) - (width // 2)
    y = (root.winfo_screenheight() // 2) - (height // 2)
    root.geometry(f"{width}x{height}+{x}+{y}")
    
    root.mainloop()
    canvas_type = selected_type.get()
    root.destroy()
    return canvas_type 

def create_canvas(canvas_type, size, dpi=300):
    """
    선택한 캔버스 타입과 크기에 따라 캔버스를 생성합니다.
    캔버스 형태는 value 값에 영향을 주지 않습니다.
    """
    cm_to_pixel = lambda x: int(x * dpi / 2.54)  # cm를 픽셀로 변환
    
    if canvas_type == "사각형":
        width, height = size
        canvas = np.ones((cm_to_pixel(height), cm_to_pixel(width), 3), dtype=np.uint8) * 255
        
    elif canvas_type == "정삼각형":
        side = size
        height = side * math.sqrt(3) / 2  # 정삼각형 높이 계산
        canvas = np.ones((cm_to_pixel(height), cm_to_pixel(side), 3), dtype=np.uint8) * 255
        
    elif canvas_type == "동그라미":
        radius = size
        diameter = cm_to_pixel(radius * 2)
        canvas = np.ones((diameter, diameter, 3), dtype=np.uint8) * 255
        cv2.circle(canvas, (diameter // 2, diameter // 2), cm_to_pixel(radius), (0, 0, 0), 2)
        
    elif canvas_type == "별모양":
        radius = size
        diameter = cm_to_pixel(radius * 2)
        canvas = np.ones((diameter, diameter, 3), dtype=np.uint8) * 255
        center = (diameter // 2, diameter // 2)
        points = []
        for i in range(10):
            angle = math.pi / 2 + i * math.pi / 5
            r = cm_to_pixel(radius) if i % 2 == 0 else cm_to_pixel(radius * 0.4)
            x = center[0] + r * math.cos(angle)
            y = center[1] + r * math.sin(angle)
            points.append((int(x), int(y)))
        points = np.array(points, np.int32)
        cv2.polylines(canvas, [points], True, (0, 0, 0), 2)
        
    elif canvas_type == "6각형":
        radius = size
        diameter = cm_to_pixel(radius * 2)
        canvas = np.ones((diameter, diameter, 3), dtype=np.uint8) * 255
        center = (diameter // 2, diameter // 2)
        points = []
        for i in range(6):
            angle = math.pi / 6 + i * math.pi / 3
            x = center[0] + cm_to_pixel(radius) * math.cos(angle)
            y = center[1] + cm_to_pixel(radius) * math.sin(angle)
            points.append((int(x), int(y)))
        points = np.array(points, np.int32)
        cv2.polylines(canvas, [points], True, (0, 0, 0), 2)
        
    elif canvas_type == "8각형":
        radius = size
        diameter = cm_to_pixel(radius * 2)
        canvas = np.ones((diameter, diameter, 3), dtype=np.uint8) * 255
        center = (diameter // 2, diameter // 2)
        points = []
        for i in range(8):
            angle = math.pi / 8 + i * math.pi / 4
            x = center[0] + cm_to_pixel(radius) * math.cos(angle)
            y = center[1] + cm_to_pixel(radius) * math.sin(angle)
            points.append((int(x), int(y)))
        points = np.array(points, np.int32)
        cv2.polylines(canvas, [points], True, (0, 0, 0), 2)
        
    elif canvas_type == "12각형":
        radius = size
        diameter = cm_to_pixel(radius * 2)
        canvas = np.ones((diameter, diameter, 3), dtype=np.uint8) * 255
        center = (diameter // 2, diameter // 2)
        points = []
        for i in range(12):
            angle = math.pi / 12 + i * math.pi / 6
            x = center[0] + cm_to_pixel(radius) * math.cos(angle)
            y = center[1] + cm_to_pixel(radius) * math.sin(angle)
            points.append((int(x), int(y)))
        points = np.array(points, np.int32)
        cv2.polylines(canvas, [points], True, (0, 0, 0), 2)
        
    else:
        canvas = None
        
    return canvas 

def draw_numbered_points(canvas, canvas_type, size, margin, dpi=300):
    """
    캔버스 가장자리에 번호가 붙은 점을 표시하고 핀 좌표를 반환합니다.
    핀과 핀 번호는 value 값에 영향을 주지 않습니다.
    """
    cm_to_pixel = lambda x: int(x * dpi / 2.54)  # cm를 픽셀로 변환
    margin_px = cm_to_pixel(margin)
    font = cv2.FONT_HERSHEY_SIMPLEX
    font_scale = 0.5
    font_color = (0, 0, 0)
    thickness = 1
    pins = []  # 핀 좌표와 번호를 저장할 리스트

    if canvas_type == "사각형":
        width, height = size
        width_px, height_px = cm_to_pixel(width), cm_to_pixel(height)
        pin_number = 0
        # 상단 가장자리
        for i in range(0, width_px, margin_px):
            if i < width_px:  # 캔버스 경계를 넘지 않도록
                pin_number += 1
                cv2.circle(canvas, (i, margin_px), 2, (0, 0, 255), -1)
                cv2.putText(canvas, str(pin_number), (i, margin_px + 15), font, font_scale, font_color, thickness)
                pins.append({"number": pin_number, "x": i, "y": margin_px})
        # 우측 가장자리
        for i in range(margin_px, height_px, margin_px):
            if i < height_px:  # 캔버스 경계를 넘지 않도록
                pin_number += 1
                cv2.circle(canvas, (width_px - margin_px, i), 2, (0, 0, 255), -1)
                cv2.putText(canvas, str(pin_number), (width_px - margin_px + 15, i), font, font_scale, font_color, thickness)
                pins.append({"number": pin_number, "x": width_px - margin_px, "y": i})
        # 하단 가장자리 (역순)
        for i in range(width_px - margin_px, -1, -margin_px):
            if i >= 0:  # 캔버스 경계를 넘지 않도록
                pin_number += 1
                cv2.circle(canvas, (i, height_px - margin_px), 2, (0, 0, 255), -1)
                cv2.putText(canvas, str(pin_number), (i, height_px - margin_px + 15), font, font_scale, font_color, thickness)
                pins.append({"number": pin_number, "x": i, "y": height_px - margin_px})
        # 좌측 가장자리 (역순)
        for i in range(height_px - margin_px, margin_px, -margin_px):
            if i >= 0:  # 캔버스 경계를 넘지 않도록
                pin_number += 1
                cv2.circle(canvas, (margin_px, i), 2, (0, 0, 255), -1)
                cv2.putText(canvas, str(pin_number), (margin_px + 15, i), font, font_scale, font_color, thickness)
                pins.append({"number": pin_number, "x": margin_px, "y": i})
                
    elif canvas_type == "정삼각형":
        side = size
        side_px = cm_to_pixel(side)
        height_px = int(side_px * math.sqrt(3) / 2)
        pin_number = 0
        # 각 변에 점 표시
        # 하단 변
        for i in range(0, side_px, margin_px):
            if i < side_px:  # 캔버스 경계를 넘지 않도록
                pin_number += 1
                cv2.circle(canvas, (i, height_px), 2, (0, 0, 255), -1)
                cv2.putText(canvas, str(pin_number), (i, height_px + 15), font, font_scale, font_color, thickness)
                pins.append({"number": pin_number, "x": i, "y": height_px})
        # 우측 변
        right_count = int((side_px / 2) / margin_px)
        for i in range(right_count):
            x = side_px - i * margin_px * math.cos(math.radians(30))
            y = height_px - i * margin_px * math.sin(math.radians(30))
            if 0 <= x < canvas.shape[1] and 0 <= y < canvas.shape[0]:  # 캔버스 경계를 넘지 않도록
                pin_number += 1
                cv2.circle(canvas, (int(x), int(y)), 2, (0, 0, 255), -1)
                cv2.putText(canvas, str(pin_number), (int(x) + 15, int(y)), font, font_scale, font_color, thickness)
                pins.append({"number": pin_number, "x": int(x), "y": int(y)})
        # 좌측 변
        left_count = int((side_px / 2) / margin_px)
        for i in range(left_count):
            x = i * margin_px * math.cos(math.radians(30))
            y = height_px - i * margin_px * math.sin(math.radians(30))
            if 0 <= x < canvas.shape[1] and 0 <= y < canvas.shape[0]:  # 캔버스 경계를 넘지 않도록
                pin_number += 1
                cv2.circle(canvas, (int(x), int(y)), 2, (0, 0, 255), -1)
                cv2.putText(canvas, str(pin_number), (int(x) - 15, int(y)), font, font_scale, font_color, thickness)
                pins.append({"number": pin_number, "x": int(x), "y": int(y)})
                
    elif canvas_type == "동그라미":
        radius = size
        radius_px = cm_to_pixel(radius)
        center = (radius_px, radius_px)
        diameter = radius_px * 2
        pin_number = 0
        # 원주에 점 표시
        pins_count = int(2 * math.pi * radius_px / margin_px)  # 마진에 따라 핀 수 계산
        angle_step = 360 / pins_count
        for angle in range(0, 360, int(angle_step)):  # 간격에 따라 핀 생성
            pin_number += 1
            x = int(center[0] + radius_px * math.cos(math.radians(angle)))
            y = int(center[1] + radius_px * math.sin(math.radians(angle)))
            if 0 <= x < canvas.shape[1] and 0 <= y < canvas.shape[0]:  # 캔버스 경계를 넘지 않도록
                cv2.circle(canvas, (x, y), 2, (0, 0, 255), -1)
                cv2.putText(canvas, str(pin_number), (x + 1, y + 1), font, font_scale, font_color, thickness)
                pins.append({"number": pin_number, "x": x, "y": y})
                
    elif canvas_type == "별모양":
        radius = size
        radius_px = cm_to_pixel(radius)
        center = (radius_px, radius_px)
        diameter = radius_px * 2
        pin_number = 0
        # 별 모양의 꼭지점에 핀 배치
        for i in range(10):
            angle = math.pi / 2 + i * math.pi / 5
            r = radius_px if i % 2 == 0 else radius_px * 0.4
            x = int(center[0] + r * math.cos(angle))
            y = int(center[1] + r * math.sin(angle))
            if 0 <= x < canvas.shape[1] and 0 <= y < canvas.shape[0]:
                pin_number += 1
                cv2.circle(canvas, (x, y), 2, (0, 0, 255), -1)
                cv2.putText(canvas, str(pin_number), (x + 1, y + 1), font, font_scale, font_color, thickness)
                pins.append({"number": pin_number, "x": x, "y": y})
                
    elif canvas_type == "6각형":
        radius = size
        radius_px = cm_to_pixel(radius)
        center = (radius_px, radius_px)
        diameter = radius_px * 2
        pin_number = 0
        # 6각형의 꼭지점에 핀 배치
        for i in range(6):
            angle = math.pi / 6 + i * math.pi / 3
            x = int(center[0] + radius_px * math.cos(angle))
            y = int(center[1] + radius_px * math.sin(angle))
            if 0 <= x < canvas.shape[1] and 0 <= y < canvas.shape[0]:
                pin_number += 1
                cv2.circle(canvas, (x, y), 2, (0, 0, 255), -1)
                cv2.putText(canvas, str(pin_number), (x + 1, y + 1), font, font_scale, font_color, thickness)
                pins.append({"number": pin_number, "x": x, "y": y})
                
    elif canvas_type == "8각형":
        radius = size
        radius_px = cm_to_pixel(radius)
        center = (radius_px, radius_px)
        diameter = radius_px * 2
        pin_number = 0
        # 8각형의 꼭지점에 핀 배치
        for i in range(8):
            angle = math.pi / 8 + i * math.pi / 4
            x = int(center[0] + radius_px * math.cos(angle))
            y = int(center[1] + radius_px * math.sin(angle))
            if 0 <= x < canvas.shape[1] and 0 <= y < canvas.shape[0]:
                pin_number += 1
                cv2.circle(canvas, (x, y), 2, (0, 0, 255), -1)
                cv2.putText(canvas, str(pin_number), (x + 1, y + 1), font, font_scale, font_color, thickness)
                pins.append({"number": pin_number, "x": x, "y": y})
                
    elif canvas_type == "12각형":
        radius = size
        radius_px = cm_to_pixel(radius)
        center = (radius_px, radius_px)
        diameter = radius_px * 2
        pin_number = 0
        # 12각형의 꼭지점에 핀 배치
        for i in range(12):
            angle = math.pi / 12 + i * math.pi / 6
            x = int(center[0] + radius_px * math.cos(angle))
            y = int(center[1] + radius_px * math.sin(angle))
            if 0 <= x < canvas.shape[1] and 0 <= y < canvas.shape[0]:
                pin_number += 1
                cv2.circle(canvas, (x, y), 2, (0, 0, 255), -1)
                cv2.putText(canvas, str(pin_number), (x + 1, y + 1), font, font_scale, font_color, thickness)
                pins.append({"number": pin_number, "x": x, "y": y})
                
    return canvas, pins 

def ask_canvas_size(canvas_type):
    """
    tkinter를 사용하여 캔버스 크기를 입력받습니다.
    """
    root = tk.Tk()
    root.withdraw()  # 루트 창 숨김
    
    if canvas_type == "사각형":
        width = simpledialog.askfloat("사각형 가로 길이 입력", "가로 길이(cm)를 입력하세요 (기본값: 10):", minvalue=1, initialvalue=10)
        height = simpledialog.askfloat("사각형 세로 길이 입력", "세로 길이(cm)를 입력하세요 (기본값: 10):", minvalue=1, initialvalue=10)
        size = (width, height)
    elif canvas_type in ["정삼각형", "동그라미", "별모양", "6각형", "8각형", "12각형"]:
        radius = simpledialog.askfloat(f"{canvas_type} 크기 입력", "반지름(cm)을 입력하세요 (기본값: 10):", minvalue=1, initialvalue=10)
        size = radius
    else:
        size = None
        
    root.destroy()
    return size 