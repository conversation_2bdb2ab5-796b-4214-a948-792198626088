import cv2
import numpy as np
import matplotlib.pyplot as plt
from PIL import Image
import math
import json
import os
import tkinter as tk
from tkinter import ttk
from tkinter import filedialog, simpledialog, messagebox
import pandas as pd
from scipy.spatial.distance import cdist
import heapq
from itertools import combinations
import time  # Import time module for timing limits

def ask_canvas_type():
    """
    tkinter를 사용하여 캔버스 타입을 라디오 버튼으로 선택합니다.
    """
    root = tk.Tk()
    root.title("캔버스 타입 선택")
    
    # 창 크기 설정
    window_width = 400
    window_height = 300
    screen_width = root.winfo_screenwidth()
    screen_height = root.winfo_screenheight()
    x = (screen_width - window_width) // 2
    y = (screen_height - window_height) // 2
    root.geometry(f"{window_width}x{window_height}+{x}+{y}")
    
    # 선택된 캔버스 타입을 저장할 변수
    canvas_type = tk.StringVar(value="사각형")
    
    # 라디오 버튼 스타일 설정
    style = ttk.Style()
    style.configure("TRadiobutton", font=("Arial", 12))
    
    # 프레임 생성
    frame = ttk.Frame(root, padding="20")
    frame.pack(expand=True, fill="both")
    
    # 제목 레이블
    title_label = ttk.Label(frame, text="캔버스 타입을 선택하세요", font=("Arial", 14, "bold"))
    title_label.pack(pady=(0, 20))
    
    # 캔버스 타입 옵션
    canvas_types = [
        "사각형",
        "정삼각형",
        "동그라미",
        "별모양",
        "6각형",
        "8각형",
        "12각형"
    ]
    
    # 라디오 버튼 생성
    for ctype in canvas_types:
        rb = ttk.Radiobutton(
            frame,
            text=ctype,
            value=ctype,
            variable=canvas_type,
            style="TRadiobutton"
        )
        rb.pack(anchor="w", pady=5)
    
    # 확인 버튼
    def on_confirm():
        root.quit()
    
    confirm_button = ttk.Button(
        frame,
        text="확인",
        command=on_confirm,
        style="TButton"
    )
    confirm_button.pack(pady=20)
    
    root.mainloop()
    selected_type = canvas_type.get()
    root.destroy()
    return selected_type

def create_canvas(canvas_type, size, dpi=300):
    """
    선택한 캔버스 타입과 크기에 따라 캔버스를 생성합니다.
    캔버스 형태는 value 값에 영향을 주지 않습니다.
    """
    cm_to_pixel = lambda x: int(x * dpi / 2.54)  # cm를 픽셀로 변환
    
    if canvas_type == "사각형":
        width, height = size
        canvas = np.ones((cm_to_pixel(height), cm_to_pixel(width), 3), dtype=np.uint8) * 255
    elif canvas_type == "정삼각형":
        side = size
        height = side * math.sqrt(3) / 2  # 정삼각형 높이 계산
        canvas = np.ones((cm_to_pixel(height), cm_to_pixel(side), 3), dtype=np.uint8) * 255
    elif canvas_type == "동그라미":
        radius = size
        diameter = cm_to_pixel(radius * 2)
        canvas = np.ones((diameter, diameter, 3), dtype=np.uint8) * 255
        cv2.circle(canvas, (diameter // 2, diameter // 2), cm_to_pixel(radius), (0, 0, 0), 2)
    elif canvas_type == "별모양":
        radius = size
        diameter = cm_to_pixel(radius * 2)
        canvas = np.ones((diameter, diameter, 3), dtype=np.uint8) * 255
        center = (diameter // 2, diameter // 2)
        points = []
        for i in range(10):
            angle = math.pi / 2 + i * math.pi / 5
            r = cm_to_pixel(radius) if i % 2 == 0 else cm_to_pixel(radius * 0.4)
            x = center[0] + r * math.cos(angle)
            y = center[1] + r * math.sin(angle)
            points.append((int(x), int(y)))
        cv2.polylines(canvas, [np.array(points)], True, (0, 0, 0), 2)
    elif canvas_type == "6각형":
        radius = size
        diameter = cm_to_pixel(radius * 2)
        canvas = np.ones((diameter, diameter, 3), dtype=np.uint8) * 255
        center = (diameter // 2, diameter // 2)
        points = []
        for i in range(6):
            angle = math.pi / 2 + i * math.pi / 3
            x = center[0] + cm_to_pixel(radius) * math.cos(angle)
            y = center[1] + cm_to_pixel(radius) * math.sin(angle)
            points.append((int(x), int(y)))
        cv2.polylines(canvas, [np.array(points)], True, (0, 0, 0), 2)
    elif canvas_type == "8각형":
        radius = size
        diameter = cm_to_pixel(radius * 2)
        canvas = np.ones((diameter, diameter, 3), dtype=np.uint8) * 255
        center = (diameter // 2, diameter // 2)
        points = []
        for i in range(8):
            angle = math.pi / 2 + i * math.pi / 4
            x = center[0] + cm_to_pixel(radius) * math.cos(angle)
            y = center[1] + cm_to_pixel(radius) * math.sin(angle)
            points.append((int(x), int(y)))
        cv2.polylines(canvas, [np.array(points)], True, (0, 0, 0), 2)
    elif canvas_type == "12각형":
        radius = size
        diameter = cm_to_pixel(radius * 2)
        canvas = np.ones((diameter, diameter, 3), dtype=np.uint8) * 255
        center = (diameter // 2, diameter // 2)
        points = []
        for i in range(12):
            angle = math.pi / 2 + i * math.pi / 6
            x = center[0] + cm_to_pixel(radius) * math.cos(angle)
            y = center[1] + cm_to_pixel(radius) * math.sin(angle)
            points.append((int(x), int(y)))
        cv2.polylines(canvas, [np.array(points)], True, (0, 0, 0), 2)
    else:
        canvas = None
    return canvas

def draw_numbered_points(canvas, canvas_type, size, margin, dpi=300):
    """
    캔버스 가장자리에 번호가 붙은 점을 표시하고 핀 좌표를 반환합니다.
    핀과 핀 번호는 value 값에 영향을 주지 않습니다.
    """
    cm_to_pixel = lambda x: int(x * dpi / 2.54)  # cm를 픽셀로 변환
    margin_px = cm_to_pixel(margin)
    font = cv2.FONT_HERSHEY_SIMPLEX
    font_scale = 0.5
    font_color = (0, 0, 0)
    thickness = 1
    pins = []  # 핀 좌표와 번호를 저장할 리스트

    if canvas_type == "사각형":
        width, height = size
        width_px, height_px = cm_to_pixel(width), cm_to_pixel(height)
        pin_number = 0
        # 상단 가장자리
        for i in range(0, width_px, margin_px):
            if i < width_px:  # 캔버스 경계를 넘지 않도록
                pin_number += 1
                cv2.circle(canvas, (i, margin_px), 2, (0, 0, 255), -1)
                cv2.putText(canvas, str(pin_number), (i, margin_px + 15), font, font_scale, font_color, thickness)
                pins.append({"number": pin_number, "x": i, "y": margin_px})
        # 우측 가장자리
        for i in range(margin_px, height_px, margin_px):
            if i < height_px:  # 캔버스 경계를 넘지 않도록
                pin_number += 1
                cv2.circle(canvas, (width_px - margin_px, i), 2, (0, 0, 255), -1)
                cv2.putText(canvas, str(pin_number), (width_px - margin_px + 15, i), font, font_scale, font_color, thickness)
                pins.append({"number": pin_number, "x": width_px - margin_px, "y": i})
        # 하단 가장자리 (역순)
        for i in range(width_px - margin_px, -1, -margin_px):
            if i >= 0:  # 캔버스 경계를 넘지 않도록
                pin_number += 1
                cv2.circle(canvas, (i, height_px - margin_px), 2, (0, 0, 255), -1)
                cv2.putText(canvas, str(pin_number), (i, height_px - margin_px + 15), font, font_scale, font_color, thickness)
                pins.append({"number": pin_number, "x": i, "y": height_px - margin_px})
        # 좌측 가장자리 (역순)
        for i in range(height_px - margin_px, margin_px, -margin_px):
            if i >= 0:  # 캔버스 경계를 넘지 않도록
                pin_number += 1
                cv2.circle(canvas, (margin_px, i), 2, (0, 0, 255), -1)
                cv2.putText(canvas, str(pin_number), (margin_px + 15, i), font, font_scale, font_color, thickness)
                pins.append({"number": pin_number, "x": margin_px, "y": i})
    elif canvas_type == "정삼각형":
        side = size
        side_px = cm_to_pixel(side)
        height_px = int(side_px * math.sqrt(3) / 2)
        pin_number = 0
        # 각 변에 점 표시
        # 하단 변
        for i in range(0, side_px, margin_px):
            if i < side_px:  # 캔버스 경계를 넘지 않도록
                pin_number += 1
                cv2.circle(canvas, (i, height_px), 2, (0, 0, 255), -1)
                cv2.putText(canvas, str(pin_number), (i, height_px + 15), font, font_scale, font_color, thickness)
                pins.append({"number": pin_number, "x": i, "y": height_px})
        # 우측 변
        right_count = int((side_px / 2) / margin_px)
        for i in range(right_count):
            x = side_px - i * margin_px * math.cos(math.radians(30))
            y = height_px - i * margin_px * math.sin(math.radians(30))
            if 0 <= x < canvas.shape[1] and 0 <= y < canvas.shape[0]:  # 캔버스 경계를 넘지 않도록
                pin_number += 1
                cv2.circle(canvas, (int(x), int(y)), 2, (0, 0, 255), -1)
                cv2.putText(canvas, str(pin_number), (int(x) + 15, int(y)), font, font_scale, font_color, thickness)
                pins.append({"number": pin_number, "x": int(x), "y": int(y)})
        # 좌측 변
        left_count = int((side_px / 2) / margin_px)
        for i in range(left_count):
            x = i * margin_px * math.cos(math.radians(30))
            y = height_px - i * margin_px * math.sin(math.radians(30))
            if 0 <= x < canvas.shape[1] and 0 <= y < canvas.shape[0]:  # 캔버스 경계를 넘지 않도록
                pin_number += 1
                cv2.circle(canvas, (int(x), int(y)), 2, (0, 0, 255), -1)
                cv2.putText(canvas, str(pin_number), (int(x) - 15, int(y)), font, font_scale, font_color, thickness)
                pins.append({"number": pin_number, "x": int(x), "y": int(y)})
    elif canvas_type == "동그라미":
        radius = size
        radius_px = cm_to_pixel(radius)
        center = (radius_px, radius_px)
        diameter = radius_px * 2
        pin_number = 0
        # 원주에 점 표시
        pins_count = int(2 * math.pi * radius_px / margin_px)  # 마진에 따라 핀 수 계산
        angle_step = 360 / pins_count
        for angle in range(0, 360, int(angle_step)):  # 간격에 따라 핀 생성
            pin_number += 1
            x = int(center[0] + radius_px * math.cos(math.radians(angle)))
            y = int(center[1] + radius_px * math.sin(math.radians(angle)))
            if 0 <= x < canvas.shape[1] and 0 <= y < canvas.shape[0]:  # 캔버스 경계를 넘지 않도록
                cv2.circle(canvas, (x, y), 2, (0, 0, 255), -1)
                cv2.putText(canvas, str(pin_number), (x + 1, y + 1), font, font_scale, font_color, thickness)
                pins.append({"number": pin_number, "x": x, "y": y})
    elif canvas_type in ["별모양", "6각형", "8각형", "12각형"]:
        radius = size
        radius_px = cm_to_pixel(radius)
        center = (radius_px, radius_px)
        diameter = radius_px * 2
        pin_number = 0
        
        # 각 도형별 꼭지점 수 설정
        if canvas_type == "별모양":
            vertices = 10
        elif canvas_type == "6각형":
            vertices = 6
        elif canvas_type == "8각형":
            vertices = 8
        else:  # 12각형
            vertices = 12
            
        # 각 꼭지점에 핀 배치
        for i in range(vertices):
            angle = math.pi / 2 + i * 2 * math.pi / vertices
            r = radius_px if canvas_type != "별모양" or i % 2 == 0 else radius_px * 0.4
            x = int(center[0] + r * math.cos(angle))
            y = int(center[1] + r * math.sin(angle))
            
            if 0 <= x < canvas.shape[1] and 0 <= y < canvas.shape[0]:
                pin_number += 1
                cv2.circle(canvas, (x, y), 2, (0, 0, 255), -1)
                cv2.putText(canvas, str(pin_number), (x + 1, y + 1), font, font_scale, font_color, thickness)
                pins.append({"number": pin_number, "x": x, "y": y})
                
        # 변을 따라 추가 핀 배치
        for i in range(vertices):
            angle1 = math.pi / 2 + i * 2 * math.pi / vertices
            angle2 = math.pi / 2 + ((i + 1) % vertices) * 2 * math.pi / vertices
            
            r1 = radius_px if canvas_type != "별모양" or i % 2 == 0 else radius_px * 0.4
            r2 = radius_px if canvas_type != "별모양" or (i + 1) % 2 == 0 else radius_px * 0.4
            
            x1 = int(center[0] + r1 * math.cos(angle1))
            y1 = int(center[1] + r1 * math.sin(angle1))
            x2 = int(center[0] + r2 * math.cos(angle2))
            y2 = int(center[1] + r2 * math.sin(angle2))
            
            # 두 점 사이의 거리 계산
            distance = math.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2)
            num_pins = int(distance / margin_px)
            
            # 두 점 사이에 핀 배치
            for j in range(1, num_pins):
                t = j / num_pins
                x = int(x1 + t * (x2 - x1))
                y = int(y1 + t * (y2 - y1))
                
                if 0 <= x < canvas.shape[1] and 0 <= y < canvas.shape[0]:
                    pin_number += 1
                    cv2.circle(canvas, (x, y), 2, (0, 0, 255), -1)
                    cv2.putText(canvas, str(pin_number), (x + 1, y + 1), font, font_scale, font_color, thickness)
                    pins.append({"number": pin_number, "x": x, "y": y})
    
    return canvas, pins 