"""
고도화된 주가 예측 AI 시스템
- 다중 데이터 소스 통합 (주가, 날씨, 키워드)
- 실시간 키워드 분석 및 상관관계 분석
- 다중 AI 알고리즘 비교 및 AutoML
- 키워드 영향도 시차 분석
- 누적 학습 및 예측 정확도 개선
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import re
from datetime import datetime, timedelta
import warnings
import os
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading
import requests
from bs4 import BeautifulSoup
import json
import sqlite3
from pytrends.request import TrendReq
from scipy.stats import pearsonr
import yfinance as yf

# ML 라이브러리
from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.linear_model import LinearRegression, Ridge, Lasso, ElasticNet
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor, ExtraTreesRegressor
from sklearn.svm import SVR
from sklearn.neural_network import MLPRegressor
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
import xgboost as xgb
import lightgbm as lgb
from catboost import CatBoostRegressor

# AutoML
try:
    from autosklearn.regression import AutoSklearnRegressor
    AUTOSKLEARN_AVAILABLE = True
except ImportError:
    AUTOSKLEARN_AVAILABLE = False

try:
    from tpot import TPOTRegressor
    TPOT_AVAILABLE = True
except ImportError:
    TPOT_AVAILABLE = False

warnings.filterwarnings('ignore')

# 한글 폰트 설정
plt.rcParams['font.family'] = 'Malgun Gothic'
plt.rcParams['axes.unicode_minus'] = False

class AdvancedStockPredictionSystem:
    """고도화된 주가 예측 AI 시스템"""

    def __init__(self):
        # 데이터 저장
        self.stock_df = None
        self.weather_df = None
        self.keyword_df = None
        self.combined_df = None
        self.original_columns = None
        self.encoding_used = None
        self.file_path = None

        # 키워드 및 상관관계 데이터
        self.keyword_correlations = {}
        self.keyword_weights = {}
        self.lag_effects = {}
        self.user_keywords = []
        self.ai_keywords = []

        # 모델 및 예측 결과
        self.models = {}
        self.model_results = {}
        self.predictions = {}

        # 데이터베이스 초기화
        self.init_database()

        # GUI 초기화
        self.root = tk.Tk()
        self.root.title("고도화된 주가 예측 AI 시스템")
        self.root.geometry("1400x900")
        self.root.state('zoomed')  # 최대화

        self.setup_gui()

    def init_database(self):
        """데이터베이스 초기화"""
        try:
            self.conn = sqlite3.connect('stock_prediction.db')
            cursor = self.conn.cursor()

            # 키워드 상관관계 테이블
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS keyword_correlations (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    keyword TEXT NOT NULL,
                    correlation REAL NOT NULL,
                    weight REAL DEFAULT 1.0,
                    lag_days INTEGER DEFAULT 0,
                    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # 예측 결과 테이블
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS prediction_results (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    model_name TEXT NOT NULL,
                    mae REAL,
                    r2_score REAL,
                    prediction_date DATE,
                    predicted_price REAL,
                    actual_price REAL,
                    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            self.conn.commit()
        except Exception as e:
            print(f"데이터베이스 초기화 오류: {str(e)}")

    def setup_gui(self):
        """고도화된 GUI 설정"""
        # 메인 노트북 (탭)
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 1. 데이터 로드 탭
        self.setup_data_tab()

        # 2. 키워드 분석 탭
        self.setup_keyword_tab()

        # 3. 모델 학습 탭
        self.setup_model_tab()

        # 4. 예측 탭
        self.setup_prediction_tab()

        # 5. 결과 분석 탭
        self.setup_analysis_tab()

        # 상태바
        self.status_var = tk.StringVar(value="시스템 준비 완료")
        status_bar = ttk.Label(self.root, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.pack(fill=tk.X, padx=10, pady=(0, 10))

    def setup_data_tab(self):
        """데이터 로드 탭 설정"""
        data_frame = ttk.Frame(self.notebook)
        self.notebook.add(data_frame, text="📊 데이터 로드")

        # 주가 데이터 섹션
        stock_frame = ttk.LabelFrame(data_frame, text="주가 데이터", padding="10")
        stock_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Button(stock_frame, text="주가 CSV 파일 선택", command=self.select_stock_file).pack(side=tk.LEFT, padx=5)
        self.stock_file_label = ttk.Label(stock_frame, text="파일이 선택되지 않았습니다.")
        self.stock_file_label.pack(side=tk.LEFT, padx=10)

        # 날씨 데이터 섹션
        weather_frame = ttk.LabelFrame(data_frame, text="날씨 데이터", padding="10")
        weather_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Button(weather_frame, text="날씨 CSV 파일 선택", command=self.select_weather_file).pack(side=tk.LEFT, padx=5)
        ttk.Button(weather_frame, text="날씨 데이터 다운로드", command=self.download_weather_data).pack(side=tk.LEFT, padx=5)
        self.weather_file_label = ttk.Label(weather_frame, text="파일이 선택되지 않았습니다.")
        self.weather_file_label.pack(side=tk.LEFT, padx=10)

        # 데이터 미리보기
        preview_frame = ttk.LabelFrame(data_frame, text="데이터 미리보기", padding="10")
        preview_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # 텍스트 위젯
        text_frame = ttk.Frame(preview_frame)
        text_frame.pack(fill=tk.BOTH, expand=True)

        self.data_text = tk.Text(text_frame, wrap=tk.WORD, height=20)
        data_scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=self.data_text.yview)
        self.data_text.configure(yscrollcommand=data_scrollbar.set)

        self.data_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        data_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def setup_keyword_tab(self):
        """키워드 분석 탭 설정"""
        keyword_frame = ttk.Frame(self.notebook)
        self.notebook.add(keyword_frame, text="🔍 키워드 분석")

        # 키워드 입력 섹션
        input_frame = ttk.LabelFrame(keyword_frame, text="키워드 설정", padding="10")
        input_frame.pack(fill=tk.X, padx=10, pady=5)

        # 사용자 키워드
        ttk.Label(input_frame, text="사용자 키워드 (쉼표로 구분):").pack(anchor=tk.W)
        self.user_keywords_var = tk.StringVar()
        ttk.Entry(input_frame, textvariable=self.user_keywords_var, width=80).pack(fill=tk.X, pady=2)

        # 사이트 URL
        ttk.Label(input_frame, text="분석할 사이트 URL:").pack(anchor=tk.W, pady=(10, 0))
        self.site_url_var = tk.StringVar()
        ttk.Entry(input_frame, textvariable=self.site_url_var, width=80).pack(fill=tk.X, pady=2)

        # 버튼들
        button_frame = ttk.Frame(input_frame)
        button_frame.pack(fill=tk.X, pady=10)

        ttk.Button(button_frame, text="AI 키워드 추천", command=self.get_ai_keywords).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="키워드 분석 시작", command=self.analyze_keywords).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="상관관계 분석", command=self.analyze_correlations).pack(side=tk.LEFT, padx=5)

        # 시차 효과 설정
        lag_frame = ttk.LabelFrame(keyword_frame, text="시차 효과 설정", padding="10")
        lag_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Label(lag_frame, text="분석할 시차 (일):").pack(side=tk.LEFT)
        self.lag_days = tk.StringVar(value="1,2,3,4,5,7,14,30,60,90")
        ttk.Entry(lag_frame, textvariable=self.lag_days, width=40).pack(side=tk.LEFT, padx=10)

        # 키워드 결과 표시
        result_frame = ttk.LabelFrame(keyword_frame, text="키워드 분석 결과", padding="10")
        result_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # 트리뷰 (테이블)
        columns = ('키워드', '상관계수', '가중치', '최적시차', 'P값')
        self.keyword_tree = ttk.Treeview(result_frame, columns=columns, show='headings', height=15)

        for col in columns:
            self.keyword_tree.heading(col, text=col)
            self.keyword_tree.column(col, width=100)

        keyword_scrollbar = ttk.Scrollbar(result_frame, orient=tk.VERTICAL, command=self.keyword_tree.yview)
        self.keyword_tree.configure(yscrollcommand=keyword_scrollbar.set)

        self.keyword_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        keyword_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 가중치 수정 버튼
        ttk.Button(result_frame, text="가중치 수정", command=self.modify_weights).pack(pady=5)

    def setup_model_tab(self):
        """모델 학습 탭 설정"""
        model_frame = ttk.Frame(self.notebook)
        self.notebook.add(model_frame, text="🤖 AI 모델")

        # 모델 선택 섹션
        selection_frame = ttk.LabelFrame(model_frame, text="모델 선택", padding="10")
        selection_frame.pack(fill=tk.X, padx=10, pady=5)

        # 모델 체크박스들
        self.model_vars = {}
        models = [
            ('Linear Regression', 'linear'),
            ('Ridge Regression', 'ridge'),
            ('Lasso Regression', 'lasso'),
            ('Random Forest', 'rf'),
            ('XGBoost', 'xgb'),
            ('LightGBM', 'lgb'),
            ('CatBoost', 'catboost'),
            ('Neural Network', 'nn'),
            ('SVR', 'svr')
        ]

        for i, (name, key) in enumerate(models):
            var = tk.BooleanVar(value=True)
            self.model_vars[key] = var
            ttk.Checkbutton(selection_frame, text=name, variable=var).grid(row=i//3, column=i%3, sticky=tk.W, padx=10, pady=2)

        # AutoML 옵션
        automl_frame = ttk.LabelFrame(model_frame, text="AutoML 옵션", padding="10")
        automl_frame.pack(fill=tk.X, padx=10, pady=5)

        self.use_automl = tk.BooleanVar()
        ttk.Checkbutton(automl_frame, text="AutoML 사용 (TPOT)", variable=self.use_automl).pack(side=tk.LEFT)

        # 학습 버튼
        ttk.Button(model_frame, text="모델 학습 시작", command=self.train_models).pack(pady=20)

        # 모델 결과 표시
        results_frame = ttk.LabelFrame(model_frame, text="모델 성능 비교", padding="10")
        results_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # 결과 트리뷰
        result_columns = ('모델명', 'MAE', 'RMSE', 'R²', '학습시간', '상태')
        self.model_tree = ttk.Treeview(results_frame, columns=result_columns, show='headings', height=10)

        for col in result_columns:
            self.model_tree.heading(col, text=col)
            self.model_tree.column(col, width=100)

        model_scrollbar = ttk.Scrollbar(results_frame, orient=tk.VERTICAL, command=self.model_tree.yview)
        self.model_tree.configure(yscrollcommand=model_scrollbar.set)

        self.model_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        model_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def setup_prediction_tab(self):
        """예측 탭 설정"""
        pred_frame = ttk.Frame(self.notebook)
        self.notebook.add(pred_frame, text="📈 예측")

        # 예측 설정
        settings_frame = ttk.LabelFrame(pred_frame, text="예측 설정", padding="10")
        settings_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Label(settings_frame, text="예측 날짜:").pack(side=tk.LEFT)
        self.pred_date_var = tk.StringVar(value=datetime.now().strftime('%Y-%m-%d'))
        ttk.Entry(settings_frame, textvariable=self.pred_date_var, width=15).pack(side=tk.LEFT, padx=10)

        ttk.Button(settings_frame, text="예측 실행", command=self.make_prediction).pack(side=tk.LEFT, padx=20)
        ttk.Button(settings_frame, text="실시간 예측", command=self.real_time_prediction).pack(side=tk.LEFT, padx=5)

        # 예측 결과 표시
        result_frame = ttk.LabelFrame(pred_frame, text="예측 결과", padding="10")
        result_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # 예측 결과 텍스트
        pred_text_frame = ttk.Frame(result_frame)
        pred_text_frame.pack(fill=tk.BOTH, expand=True)

        self.pred_text = tk.Text(pred_text_frame, wrap=tk.WORD, height=20)
        pred_scrollbar = ttk.Scrollbar(pred_text_frame, orient=tk.VERTICAL, command=self.pred_text.yview)
        self.pred_text.configure(yscrollcommand=pred_scrollbar.set)

        self.pred_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        pred_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def setup_analysis_tab(self):
        """결과 분석 탭 설정"""
        analysis_frame = ttk.Frame(self.notebook)
        self.notebook.add(analysis_frame, text="📊 분석 결과")

        # 차트 버튼들
        chart_frame = ttk.LabelFrame(analysis_frame, text="차트 생성", padding="10")
        chart_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Button(chart_frame, text="주가 차트", command=self.plot_stock_chart).pack(side=tk.LEFT, padx=5)
        ttk.Button(chart_frame, text="상관관계 히트맵", command=self.plot_correlation_heatmap).pack(side=tk.LEFT, padx=5)
        ttk.Button(chart_frame, text="모델 성능 비교", command=self.plot_model_comparison).pack(side=tk.LEFT, padx=5)
        ttk.Button(chart_frame, text="예측 vs 실제", command=self.plot_prediction_comparison).pack(side=tk.LEFT, padx=5)

        # 로그 표시
        log_frame = ttk.LabelFrame(analysis_frame, text="시스템 로그", padding="10")
        log_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        log_text_frame = ttk.Frame(log_frame)
        log_text_frame.pack(fill=tk.BOTH, expand=True)

        self.log_text = tk.Text(log_text_frame, wrap=tk.WORD, height=20)
        log_scrollbar = ttk.Scrollbar(log_text_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)

        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def log_message(self, message):
        """로그 메시지 출력"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        log_entry = f"{timestamp} - {message}\n"

        # 로그 텍스트에 추가
        if hasattr(self, 'log_text'):
            self.log_text.insert(tk.END, log_entry)
            self.log_text.see(tk.END)

        # 상태바 업데이트
        self.status_var.set(message)
        self.root.update_idletasks()

        print(log_entry.strip())  # 콘솔에도 출력

    # ==================== 데이터 로드 기능 ====================

    def select_stock_file(self):
        """주가 CSV 파일 선택"""
        file_path = filedialog.askopenfilename(
            title="주가 CSV 파일 선택",
            filetypes=[("CSV 파일", "*.csv"), ("모든 파일", "*.*")]
        )

        if file_path:
            self.file_path = file_path
            self.stock_file_label.config(text=os.path.basename(file_path))
            self.log_message(f"주가 파일 선택됨: {os.path.basename(file_path)}")

            # 백그라운드에서 파일 로드
            threading.Thread(target=self.load_stock_file, daemon=True).start()

    def select_weather_file(self):
        """날씨 CSV 파일 선택"""
        file_path = filedialog.askopenfilename(
            title="날씨 CSV 파일 선택",
            filetypes=[("CSV 파일", "*.csv"), ("모든 파일", "*.*")]
        )

        if file_path:
            self.weather_file_label.config(text=os.path.basename(file_path))
            self.log_message(f"날씨 파일 선택됨: {os.path.basename(file_path)}")

            # 백그라운드에서 파일 로드
            threading.Thread(target=self.load_weather_file, args=(file_path,), daemon=True).start()

    def load_stock_file(self):
        """주가 파일 로드"""
        try:
            self.log_message("주가 데이터 로딩 중...")

            # CSV 파일 읽기 (유연한 방식)
            self.stock_df = self.read_csv_ultimate(self.file_path)

            if self.stock_df is not None:
                self.log_message(f"✅ 주가 데이터 로드 완료: {self.stock_df.shape[0]}행 × {self.stock_df.shape[1]}열")

                # 기술적 지표 계산
                if self.is_stock_data():
                    self.calculate_technical_indicators()
                    self.log_message("✅ 기술적 지표 계산 완료")

                # 데이터 미리보기 업데이트
                self.update_data_preview()
            else:
                self.log_message("❌ 주가 파일 로드 실패")

        except Exception as e:
            self.log_message(f"❌ 주가 파일 로드 오류: {str(e)}")

    def load_weather_file(self, file_path):
        """날씨 파일 로드"""
        try:
            self.log_message("날씨 데이터 로딩 중...")

            self.weather_df = self.read_csv_ultimate(file_path)

            if self.weather_df is not None:
                self.log_message(f"✅ 날씨 데이터 로드 완료: {self.weather_df.shape[0]}행 × {self.weather_df.shape[1]}열")
                self.update_data_preview()
            else:
                self.log_message("❌ 날씨 파일 로드 실패")

        except Exception as e:
            self.log_message(f"❌ 날씨 파일 로드 오류: {str(e)}")

    def download_weather_data(self):
        """날씨 데이터 다운로드 (API 사용)"""
        try:
            self.log_message("날씨 데이터 다운로드 시작...")

            # 간단한 날씨 데이터 생성 (실제로는 API 사용)
            if self.stock_df is not None and isinstance(self.stock_df.index, pd.DatetimeIndex):
                dates = self.stock_df.index

                # 가상의 날씨 데이터 생성
                np.random.seed(42)
                weather_data = {
                    'date': dates,
                    'temperature': np.random.normal(20, 10, len(dates)),
                    'humidity': np.random.normal(60, 20, len(dates)),
                    'pressure': np.random.normal(1013, 20, len(dates)),
                    'wind_speed': np.random.exponential(5, len(dates)),
                    'precipitation': np.random.exponential(2, len(dates))
                }

                self.weather_df = pd.DataFrame(weather_data)
                self.weather_df.set_index('date', inplace=True)

                self.log_message("✅ 날씨 데이터 다운로드 완료 (가상 데이터)")
                self.weather_file_label.config(text="날씨 데이터 다운로드됨")
                self.update_data_preview()
            else:
                self.log_message("❌ 먼저 주가 데이터를 로드해주세요.")

        except Exception as e:
            self.log_message(f"❌ 날씨 데이터 다운로드 오류: {str(e)}")

    def update_data_preview(self):
        """데이터 미리보기 업데이트"""
        try:
            preview_text = ""

            if self.stock_df is not None:
                preview_text += "=== 주가 데이터 ===\n"
                preview_text += f"형태: {self.stock_df.shape}\n"
                preview_text += f"컬럼: {list(self.stock_df.columns)}\n"
                preview_text += f"기간: {self.stock_df.index.min()} ~ {self.stock_df.index.max()}\n\n"
                preview_text += str(self.stock_df.head()) + "\n\n"

            if self.weather_df is not None:
                preview_text += "=== 날씨 데이터 ===\n"
                preview_text += f"형태: {self.weather_df.shape}\n"
                preview_text += f"컬럼: {list(self.weather_df.columns)}\n"
                preview_text += str(self.weather_df.head()) + "\n\n"

            self.data_text.delete(1.0, tk.END)
            self.data_text.insert(tk.END, preview_text)

        except Exception as e:
            self.log_message(f"❌ 데이터 미리보기 업데이트 오류: {str(e)}")

    # ==================== CSV 읽기 기능 (기존 코드 재사용) ====================

    def read_csv_ultimate(self, file_path):
        """Ultimate CSV 읽기 함수"""
        try:
            # 1단계: 인코딩 감지 및 읽기
            df = self._read_with_encoding_detection(file_path)

            # 2단계: 컬럼명 정리 및 매핑
            df = self._clean_and_map_columns(df)

            # 3단계: 데이터 타입 자동 변환
            df = self._auto_convert_datatypes(df)

            # 4단계: 날짜 인덱스 안전 설정
            df = self._safe_set_datetime_index(df)

            # 5단계: 최종 정리
            df = self._final_cleanup(df)

            return df

        except Exception as e:
            self.log_message(f"❌ CSV 읽기 실패: {str(e)}")
            return None

    def _read_with_encoding_detection(self, file_path):
        """다양한 인코딩으로 파일 읽기 시도"""
        encodings = ['utf-8-sig', 'utf-8', 'cp949', 'euc-kr', 'latin-1', 'cp1252']

        for encoding in encodings:
            try:
                df = pd.read_csv(file_path, encoding=encoding, low_memory=False)
                self.encoding_used = encoding
                return df
            except (UnicodeDecodeError, UnicodeError):
                continue
            except Exception as e:
                if encoding == encodings[-1]:
                    raise e
                continue

        raise Exception("지원되는 인코딩을 찾을 수 없습니다.")

    def _clean_and_map_columns(self, df):
        """컬럼명 정리 및 표준화"""
        self.original_columns = df.columns.tolist()

        # 컬럼명 정리
        cleaned_columns = []
        for col in df.columns:
            clean_col = str(col).strip().replace('﻿', '')
            clean_col = re.sub(r'[^\w\s가-힣]', '', clean_col)
            clean_col = re.sub(r'\s+', '_', clean_col)
            cleaned_columns.append(clean_col)

        df.columns = cleaned_columns

        # 표준 컬럼명 매핑
        column_mapping = {}
        for col in df.columns:
            col_lower = col.lower()

            if any(keyword in col_lower for keyword in ['date', 'time', '날짜', '일자', '시간']):
                column_mapping[col] = 'date'
            elif any(keyword in col_lower for keyword in ['open', '시가', '시작가']):
                column_mapping[col] = 'open'
            elif any(keyword in col_lower for keyword in ['high', '고가', '최고가']):
                column_mapping[col] = 'high'
            elif any(keyword in col_lower for keyword in ['low', '저가', '최저가']):
                column_mapping[col] = 'low'
            elif any(keyword in col_lower for keyword in ['close', '종가', '마감가', '끝가']):
                column_mapping[col] = 'close'
            elif any(keyword in col_lower for keyword in ['volume', '거래량', '볼륨']):
                column_mapping[col] = 'volume'

        if column_mapping:
            df = df.rename(columns=column_mapping)

        return df

    def _auto_convert_datatypes(self, df):
        """데이터 타입 자동 변환"""
        for col in df.columns:
            if col == 'date':
                df[col] = self._convert_to_datetime(df[col])
            elif col in ['open', 'high', 'low', 'close', 'volume']:
                df[col] = self._convert_to_numeric(df[col])
            else:
                # 자동 감지 시도
                numeric_converted = self._convert_to_numeric(df[col], errors='ignore')
                if not numeric_converted.equals(df[col]):
                    df[col] = numeric_converted

        return df

    def _convert_to_datetime(self, series):
        """안전한 날짜 변환"""
        try:
            if series.dtype == 'object':
                cleaned = series.astype(str).str.strip()
                cleaned = cleaned.replace(['nan', 'NaN', 'null', 'NULL', ''], np.nan)
            else:
                cleaned = series

            converted = pd.to_datetime(cleaned, errors='coerce')
            success_rate = converted.notna().sum() / len(series)

            if success_rate < 0.3:
                return series

            return converted
        except:
            return series

    def _convert_to_numeric(self, series, errors='coerce'):
        """안전한 숫자 변환"""
        try:
            if series.dtype == 'object':
                cleaned = series.astype(str)
                cleaned = cleaned.str.replace(',', '')
                cleaned = cleaned.str.replace('"', '')
                cleaned = cleaned.str.replace("'", '')
                cleaned = cleaned.str.replace('(', '-')
                cleaned = cleaned.str.replace(')', '')
                cleaned = cleaned.str.replace(' ', '')
                cleaned = cleaned.str.replace(r'[^\d.-]', '', regex=True)
                cleaned = cleaned.replace(['', 'nan', 'NaN', 'null', 'NULL'], np.nan)
                converted = pd.to_numeric(cleaned, errors=errors)
            else:
                converted = pd.to_numeric(series, errors=errors)

            return converted
        except:
            return series

    def _safe_set_datetime_index(self, df):
        """안전한 날짜 인덱스 설정"""
        if 'date' in df.columns:
            try:
                if pd.api.types.is_datetime64_any_dtype(df['date']):
                    df = df.set_index('date')
                    df = df.sort_index()
                    df = df[~df.index.duplicated(keep='first')]
            except Exception as e:
                self.log_message(f"⚠️ 날짜 인덱스 설정 실패: {str(e)}")

        return df

    def _final_cleanup(self, df):
        """최종 정리"""
        # 빈 행 제거
        before_rows = len(df)
        df = df.dropna(how='all')
        after_rows = len(df)

        if before_rows != after_rows:
            self.log_message(f"🧹 빈 행 제거: {before_rows - after_rows}행")

        return df

    def is_stock_data(self):
        """주식 데이터인지 확인"""
        if self.stock_df is None:
            return False

        stock_columns = ['open', 'high', 'low', 'close']
        return all(col in self.stock_df.columns for col in stock_columns)

    def calculate_technical_indicators(self):
        """기술적 지표 계산"""
        if not self.is_stock_data():
            return

        try:
            # 이동평균선
            self.stock_df['ma5'] = self.stock_df['close'].rolling(window=5).mean()
            self.stock_df['ma20'] = self.stock_df['close'].rolling(window=20).mean()
            self.stock_df['ma60'] = self.stock_df['close'].rolling(window=60).mean()

            # 볼린저 밴드
            self.stock_df['bb_middle'] = self.stock_df['close'].rolling(window=20).mean()
            bb_std = self.stock_df['close'].rolling(window=20).std()
            self.stock_df['bb_upper'] = self.stock_df['bb_middle'] + (bb_std * 2)
            self.stock_df['bb_lower'] = self.stock_df['bb_middle'] - (bb_std * 2)

            # RSI
            delta = self.stock_df['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            self.stock_df['rsi'] = 100 - (100 / (1 + rs))

            # MACD
            exp1 = self.stock_df['close'].ewm(span=12).mean()
            exp2 = self.stock_df['close'].ewm(span=26).mean()
            self.stock_df['macd'] = exp1 - exp2
            self.stock_df['macd_signal'] = self.stock_df['macd'].ewm(span=9).mean()
            self.stock_df['macd_histogram'] = self.stock_df['macd'] - self.stock_df['macd_signal']

            # 일일 수익률
            self.stock_df['daily_return'] = self.stock_df['close'].pct_change()

            # 변동성
            self.stock_df['volatility'] = self.stock_df['daily_return'].rolling(window=20).std() * np.sqrt(252)

        except Exception as e:
            self.log_message(f"⚠️ 기술적 지표 계산 실패: {str(e)}")

    # ==================== 키워드 분석 기능 ====================

    def get_ai_keywords(self):
        """AI가 추천하는 키워드 생성"""
        try:
            self.log_message("AI 키워드 추천 시작...")

            # 주식 관련 기본 키워드들
            base_keywords = [
                "stock market", "economy", "inflation", "interest rate", "GDP",
                "unemployment", "earnings", "revenue", "profit", "loss",
                "merger", "acquisition", "IPO", "dividend", "buyback"
            ]

            # 산업별 키워드 (주가 데이터에 따라 동적 생성 가능)
            industry_keywords = [
                "technology", "healthcare", "finance", "energy", "retail",
                "manufacturing", "real estate", "telecommunications", "utilities"
            ]

            # 경제 지표 키워드
            economic_keywords = [
                "federal reserve", "central bank", "monetary policy", "fiscal policy",
                "trade war", "tariff", "currency", "oil price", "gold price"
            ]

            self.ai_keywords = base_keywords + industry_keywords + economic_keywords

            # 사용자 키워드 필드에 추가
            current_keywords = self.user_keywords_var.get()
            if current_keywords:
                all_keywords = current_keywords + ", " + ", ".join(self.ai_keywords)
            else:
                all_keywords = ", ".join(self.ai_keywords)

            self.user_keywords_var.set(all_keywords)

            self.log_message(f"✅ AI 키워드 {len(self.ai_keywords)}개 추천 완료")

        except Exception as e:
            self.log_message(f"❌ AI 키워드 추천 오류: {str(e)}")

    def analyze_keywords(self):
        """키워드 분석 시작"""
        try:
            self.log_message("키워드 분석 시작...")

            # 사용자 키워드 파싱
            keywords_text = self.user_keywords_var.get()
            if not keywords_text.strip():
                self.log_message("❌ 키워드를 입력해주세요.")
                return

            self.user_keywords = [kw.strip() for kw in keywords_text.split(',') if kw.strip()]

            # 백그라운드에서 키워드 분석 실행
            threading.Thread(target=self.analyze_keywords_thread, daemon=True).start()

        except Exception as e:
            self.log_message(f"❌ 키워드 분석 오류: {str(e)}")

    def analyze_keywords_thread(self):
        """키워드 분석 스레드"""
        try:
            # Google Trends 데이터 수집
            self.collect_google_trends()

            # 사이트 키워드 분석
            if self.site_url_var.get().strip():
                self.analyze_site_keywords()

            # 상관관계 분석
            self.analyze_correlations()

        except Exception as e:
            self.log_message(f"❌ 키워드 분석 스레드 오류: {str(e)}")

    def collect_google_trends(self):
        """Google Trends 데이터 수집"""
        try:
            self.log_message("Google Trends 데이터 수집 중...")

            if self.stock_df is None:
                self.log_message("❌ 먼저 주가 데이터를 로드해주세요.")
                return

            # 날짜 범위 설정
            start_date = self.stock_df.index.min().strftime('%Y-%m-%d')
            end_date = self.stock_df.index.max().strftime('%Y-%m-%d')

            # PyTrends 초기화
            pytrends = TrendReq(hl='en-US', tz=360)

            keyword_data = {}

            for i, keyword in enumerate(self.user_keywords):
                try:
                    self.log_message(f"키워드 '{keyword}' 분석 중... ({i+1}/{len(self.user_keywords)})")

                    # Google Trends 데이터 요청
                    pytrends.build_payload([keyword], timeframe=f'{start_date} {end_date}')
                    trends_df = pytrends.interest_over_time()

                    if not trends_df.empty and keyword in trends_df.columns:
                        # 날짜 인덱스 맞추기
                        trends_df.index = pd.to_datetime(trends_df.index)
                        keyword_data[keyword] = trends_df[keyword]

                        self.log_message(f"✅ '{keyword}' 데이터 수집 완료")
                    else:
                        self.log_message(f"⚠️ '{keyword}' 데이터 없음")

                except Exception as e:
                    self.log_message(f"⚠️ '{keyword}' 수집 실패: {str(e)}")
                    continue

            # 키워드 데이터프레임 생성
            if keyword_data:
                self.keyword_df = pd.DataFrame(keyword_data)
                self.log_message(f"✅ Google Trends 데이터 수집 완료: {len(keyword_data)}개 키워드")
            else:
                self.log_message("❌ Google Trends 데이터 수집 실패")

        except Exception as e:
            self.log_message(f"❌ Google Trends 수집 오류: {str(e)}")

    def analyze_site_keywords(self):
        """사이트 키워드 분석"""
        try:
            site_url = self.site_url_var.get().strip()
            if not site_url:
                return

            self.log_message(f"사이트 키워드 분석 중: {site_url}")

            # 웹 스크래핑
            response = requests.get(site_url, timeout=10)
            soup = BeautifulSoup(response.text, 'html.parser')

            # 텍스트 추출
            text = soup.get_text()

            # 키워드 빈도 분석
            words = re.findall(r'\b\w+\b', text.lower())
            word_freq = {}

            for keyword in self.user_keywords:
                keyword_lower = keyword.lower()
                count = words.count(keyword_lower)
                if count > 0:
                    word_freq[keyword] = count

            if word_freq:
                self.log_message(f"✅ 사이트 키워드 분석 완료: {len(word_freq)}개 키워드 발견")

                # 키워드 빈도를 시계열 데이터로 변환 (간단한 예시)
                if self.keyword_df is not None:
                    for keyword, freq in word_freq.items():
                        if keyword not in self.keyword_df.columns:
                            # 빈도를 기반으로 가상의 시계열 데이터 생성
                            self.keyword_df[f"{keyword}_site"] = freq
            else:
                self.log_message("⚠️ 사이트에서 키워드를 찾을 수 없습니다.")

        except Exception as e:
            self.log_message(f"❌ 사이트 키워드 분석 오류: {str(e)}")

    def analyze_correlations(self):
        """상관관계 분석"""
        try:
            self.log_message("상관관계 분석 시작...")

            if self.stock_df is None or self.keyword_df is None:
                self.log_message("❌ 주가 데이터와 키워드 데이터가 필요합니다.")
                return

            # 시차 효과 분석
            lag_days_list = [int(x.strip()) for x in self.lag_days.get().split(',') if x.strip().isdigit()]

            correlation_results = []

            for keyword in self.keyword_df.columns:
                best_correlation = 0
                best_lag = 0
                best_pvalue = 1.0

                for lag in lag_days_list:
                    try:
                        # 키워드 데이터를 lag만큼 시프트
                        keyword_shifted = self.keyword_df[keyword].shift(lag)

                        # 공통 날짜 범위에서 상관관계 계산
                        common_dates = self.stock_df.index.intersection(keyword_shifted.index)

                        if len(common_dates) > 10:  # 최소 10개 데이터 포인트
                            stock_prices = self.stock_df.loc[common_dates, 'close']
                            keyword_values = keyword_shifted.loc[common_dates]

                            # NaN 제거
                            valid_mask = ~(stock_prices.isna() | keyword_values.isna())
                            if valid_mask.sum() > 10:
                                correlation, pvalue = pearsonr(
                                    stock_prices[valid_mask],
                                    keyword_values[valid_mask]
                                )

                                if abs(correlation) > abs(best_correlation):
                                    best_correlation = correlation
                                    best_lag = lag
                                    best_pvalue = pvalue

                    except Exception as e:
                        continue

                # 결과 저장
                if abs(best_correlation) > 0.1:  # 최소 상관관계 임계값
                    correlation_results.append({
                        'keyword': keyword,
                        'correlation': best_correlation,
                        'lag': best_lag,
                        'pvalue': best_pvalue,
                        'weight': 1.0  # 기본 가중치
                    })

                    # 데이터베이스에 저장
                    self.save_correlation_to_db(keyword, best_correlation, 1.0, best_lag)

            # 결과 표시
            self.display_correlation_results(correlation_results)

            self.log_message(f"✅ 상관관계 분석 완료: {len(correlation_results)}개 유의미한 상관관계 발견")

        except Exception as e:
            self.log_message(f"❌ 상관관계 분석 오류: {str(e)}")

    def save_correlation_to_db(self, keyword, correlation, weight, lag_days):
        """상관관계를 데이터베이스에 저장"""
        try:
            cursor = self.conn.cursor()

            # 기존 데이터 확인
            cursor.execute(
                "SELECT id FROM keyword_correlations WHERE keyword = ?",
                (keyword,)
            )
            existing = cursor.fetchone()

            if existing:
                # 업데이트
                cursor.execute("""
                    UPDATE keyword_correlations
                    SET correlation = ?, weight = ?, lag_days = ?, updated_date = CURRENT_TIMESTAMP
                    WHERE keyword = ?
                """, (correlation, weight, lag_days, keyword))
            else:
                # 새로 삽입
                cursor.execute("""
                    INSERT INTO keyword_correlations (keyword, correlation, weight, lag_days)
                    VALUES (?, ?, ?, ?)
                """, (keyword, correlation, weight, lag_days))

            self.conn.commit()

        except Exception as e:
            self.log_message(f"⚠️ DB 저장 오류: {str(e)}")

    def display_correlation_results(self, results):
        """상관관계 결과 표시"""
        try:
            # 기존 결과 삭제
            for item in self.keyword_tree.get_children():
                self.keyword_tree.delete(item)

            # 새 결과 추가
            for result in sorted(results, key=lambda x: abs(x['correlation']), reverse=True):
                self.keyword_tree.insert('', 'end', values=(
                    result['keyword'],
                    f"{result['correlation']:.4f}",
                    f"{result['weight']:.2f}",
                    f"{result['lag']}일",
                    f"{result['pvalue']:.4f}"
                ))

        except Exception as e:
            self.log_message(f"❌ 결과 표시 오류: {str(e)}")

    def modify_weights(self):
        """가중치 수정"""
        try:
            selected_item = self.keyword_tree.selection()
            if not selected_item:
                messagebox.showwarning("경고", "수정할 키워드를 선택해주세요.")
                return

            # 선택된 항목의 값 가져오기
            item_values = self.keyword_tree.item(selected_item[0])['values']
            keyword = item_values[0]
            current_weight = float(item_values[2])

            # 새 가중치 입력 받기
            new_weight = tk.simpledialog.askfloat(
                "가중치 수정",
                f"'{keyword}'의 새 가중치를 입력하세요:",
                initialvalue=current_weight,
                minvalue=0.0,
                maxvalue=10.0
            )

            if new_weight is not None:
                # 트리뷰 업데이트
                new_values = list(item_values)
                new_values[2] = f"{new_weight:.2f}"
                self.keyword_tree.item(selected_item[0], values=new_values)

                # 데이터베이스 업데이트
                cursor = self.conn.cursor()
                cursor.execute(
                    "UPDATE keyword_correlations SET weight = ?, updated_date = CURRENT_TIMESTAMP WHERE keyword = ?",
                    (new_weight, keyword)
                )
                self.conn.commit()

                self.log_message(f"✅ '{keyword}' 가중치를 {new_weight}로 수정했습니다.")

        except Exception as e:
            self.log_message(f"❌ 가중치 수정 오류: {str(e)}")

    # ==================== 모델 학습 기능 ====================

    def train_models(self):
        """모델 학습 시작"""
        try:
            self.log_message("모델 학습 시작...")

            if self.stock_df is None:
                self.log_message("❌ 먼저 주가 데이터를 로드해주세요.")
                return

            # 백그라운드에서 모델 학습 실행
            threading.Thread(target=self.train_models_thread, daemon=True).start()

        except Exception as e:
            self.log_message(f"❌ 모델 학습 오류: {str(e)}")

    def train_models_thread(self):
        """모델 학습 스레드"""
        try:
            # 데이터 준비
            self.prepare_training_data()

            # 선택된 모델들 학습
            self.train_selected_models()

            # AutoML 실행 (선택된 경우)
            if self.use_automl.get():
                self.train_automl_models()

            self.log_message("✅ 모델 학습 완료")

        except Exception as e:
            self.log_message(f"❌ 모델 학습 스레드 오류: {str(e)}")

    def prepare_training_data(self):
        """학습 데이터 준비"""
        try:
            self.log_message("학습 데이터 준비 중...")

            # 기본 주가 데이터
            features = []
            feature_names = []

            # 기술적 지표 추가
            tech_indicators = ['ma5', 'ma20', 'ma60', 'rsi', 'macd', 'volatility']
            for indicator in tech_indicators:
                if indicator in self.stock_df.columns:
                    features.append(self.stock_df[indicator])
                    feature_names.append(indicator)

            # 날씨 데이터 추가
            if self.weather_df is not None:
                weather_cols = ['temperature', 'humidity', 'pressure', 'wind_speed', 'precipitation']
                for col in weather_cols:
                    if col in self.weather_df.columns:
                        # 날짜 맞춤
                        weather_aligned = self.weather_df[col].reindex(self.stock_df.index, method='ffill')
                        features.append(weather_aligned)
                        feature_names.append(f"weather_{col}")

            # 키워드 데이터 추가
            if self.keyword_df is not None:
                for col in self.keyword_df.columns:
                    keyword_aligned = self.keyword_df[col].reindex(self.stock_df.index, method='ffill')
                    features.append(keyword_aligned)
                    feature_names.append(f"keyword_{col}")

            # 데이터프레임 생성
            if features:
                self.combined_df = pd.concat(features, axis=1)
                self.combined_df.columns = feature_names

                # 타겟 변수 (다음 날 종가)
                self.combined_df['target'] = self.stock_df['close'].shift(-1)

                # NaN 제거
                self.combined_df = self.combined_df.dropna()

                self.log_message(f"✅ 학습 데이터 준비 완료: {self.combined_df.shape[0]}행 × {self.combined_df.shape[1]}열")
            else:
                raise Exception("학습에 사용할 특성이 없습니다.")

        except Exception as e:
            self.log_message(f"❌ 학습 데이터 준비 오류: {str(e)}")
            raise

    def train_selected_models(self):
        """선택된 모델들 학습"""
        try:
            if self.combined_df is None:
                raise Exception("학습 데이터가 준비되지 않았습니다.")

            # 특성과 타겟 분리
            X = self.combined_df.drop('target', axis=1)
            y = self.combined_df['target']

            # 데이터 분할
            X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

            # 데이터 스케일링
            scaler = StandardScaler()
            X_train_scaled = scaler.fit_transform(X_train)
            X_test_scaled = scaler.transform(X_test)

            # 모델 정의
            model_configs = {
                'linear': LinearRegression(),
                'ridge': Ridge(alpha=1.0),
                'lasso': Lasso(alpha=0.1),
                'rf': RandomForestRegressor(n_estimators=100, random_state=42),
                'xgb': xgb.XGBRegressor(random_state=42),
                'lgb': lgb.LGBMRegressor(random_state=42),
                'catboost': CatBoostRegressor(random_state=42, verbose=False),
                'nn': MLPRegressor(hidden_layer_sizes=(100, 50), random_state=42, max_iter=500),
                'svr': SVR(kernel='rbf')
            }

            # 기존 결과 삭제
            for item in self.model_tree.get_children():
                self.model_tree.delete(item)

            # 각 모델 학습
            for model_name, model in model_configs.items():
                if self.model_vars[model_name].get():
                    try:
                        self.log_message(f"{model_name} 모델 학습 중...")

                        start_time = datetime.now()

                        # 모델 학습
                        if model_name in ['linear', 'ridge', 'lasso', 'nn', 'svr']:
                            model.fit(X_train_scaled, y_train)
                            y_pred = model.predict(X_test_scaled)
                        else:
                            model.fit(X_train, y_train)
                            y_pred = model.predict(X_test)

                        end_time = datetime.now()
                        training_time = (end_time - start_time).total_seconds()

                        # 성능 평가
                        mae = mean_absolute_error(y_test, y_pred)
                        rmse = np.sqrt(mean_squared_error(y_test, y_pred))
                        r2 = r2_score(y_test, y_pred)

                        # 결과 저장
                        self.models[model_name] = model
                        self.model_results[model_name] = {
                            'mae': mae,
                            'rmse': rmse,
                            'r2': r2,
                            'training_time': training_time
                        }

                        # 결과 표시
                        self.model_tree.insert('', 'end', values=(
                            model_name,
                            f"{mae:.4f}",
                            f"{rmse:.4f}",
                            f"{r2:.4f}",
                            f"{training_time:.2f}s",
                            "완료"
                        ))

                        # 데이터베이스에 저장
                        self.save_model_result_to_db(model_name, mae, r2)

                        self.log_message(f"✅ {model_name} 학습 완료 (R²: {r2:.4f})")

                    except Exception as e:
                        self.log_message(f"❌ {model_name} 학습 실패: {str(e)}")
                        self.model_tree.insert('', 'end', values=(
                            model_name, "오류", "오류", "오류", "0s", "실패"
                        ))

        except Exception as e:
            self.log_message(f"❌ 모델 학습 오류: {str(e)}")

    def train_automl_models(self):
        """AutoML 모델 학습"""
        try:
            if not TPOT_AVAILABLE:
                self.log_message("⚠️ TPOT가 설치되지 않았습니다.")
                return

            self.log_message("AutoML (TPOT) 학습 시작...")

            X = self.combined_df.drop('target', axis=1)
            y = self.combined_df['target']

            X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

            # TPOT 모델
            tpot = TPOTRegressor(
                generations=5,
                population_size=20,
                verbosity=0,
                random_state=42,
                n_jobs=-1
            )

            start_time = datetime.now()
            tpot.fit(X_train, y_train)
            end_time = datetime.now()

            y_pred = tpot.predict(X_test)

            mae = mean_absolute_error(y_test, y_pred)
            rmse = np.sqrt(mean_squared_error(y_test, y_pred))
            r2 = r2_score(y_test, y_pred)
            training_time = (end_time - start_time).total_seconds()

            # 결과 저장
            self.models['automl'] = tpot
            self.model_results['automl'] = {
                'mae': mae,
                'rmse': rmse,
                'r2': r2,
                'training_time': training_time
            }

            # 결과 표시
            self.model_tree.insert('', 'end', values=(
                "AutoML (TPOT)",
                f"{mae:.4f}",
                f"{rmse:.4f}",
                f"{r2:.4f}",
                f"{training_time:.2f}s",
                "완료"
            ))

            self.log_message(f"✅ AutoML 학습 완료 (R²: {r2:.4f})")

        except Exception as e:
            self.log_message(f"❌ AutoML 학습 오류: {str(e)}")

    def save_model_result_to_db(self, model_name, mae, r2_score):
        """모델 결과를 데이터베이스에 저장"""
        try:
            cursor = self.conn.cursor()
            cursor.execute("""
                INSERT INTO prediction_results (model_name, mae, r2_score, prediction_date)
                VALUES (?, ?, ?, ?)
            """, (model_name, mae, r2_score, datetime.now().date()))
            self.conn.commit()

        except Exception as e:
            self.log_message(f"⚠️ 모델 결과 DB 저장 오류: {str(e)}")

    # ==================== 예측 기능 ====================

    def make_prediction(self):
        """예측 실행"""
        try:
            self.log_message("예측 실행 중...")

            if not self.models:
                self.log_message("❌ 먼저 모델을 학습해주세요.")
                return

            # 백그라운드에서 예측 실행
            threading.Thread(target=self.make_prediction_thread, daemon=True).start()

        except Exception as e:
            self.log_message(f"❌ 예측 오류: {str(e)}")

    def make_prediction_thread(self):
        """예측 스레드"""
        try:
            pred_date = self.pred_date_var.get()
            self.log_message(f"{pred_date} 주가 예측 중...")

            # 최신 데이터로 예측
            latest_data = self.combined_df.iloc[-1:].drop('target', axis=1)

            predictions = {}

            for model_name, model in self.models.items():
                try:
                    if model_name in ['linear', 'ridge', 'lasso', 'nn', 'svr']:
                        # 스케일링 필요한 모델
                        scaler = StandardScaler()
                        scaler.fit(self.combined_df.drop('target', axis=1))
                        latest_scaled = scaler.transform(latest_data)
                        pred = model.predict(latest_scaled)[0]
                    else:
                        pred = model.predict(latest_data)[0]

                    predictions[model_name] = pred

                except Exception as e:
                    self.log_message(f"⚠️ {model_name} 예측 실패: {str(e)}")

            # 예측 결과 표시
            self.display_predictions(pred_date, predictions)

        except Exception as e:
            self.log_message(f"❌ 예측 스레드 오류: {str(e)}")

    def display_predictions(self, pred_date, predictions):
        """예측 결과 표시"""
        try:
            result_text = f"=== {pred_date} 주가 예측 결과 ===\n\n"

            # 예측 결과 정렬 (R² 기준)
            sorted_predictions = []
            for model_name, pred_price in predictions.items():
                r2 = self.model_results.get(model_name, {}).get('r2', 0)
                sorted_predictions.append((model_name, pred_price, r2))

            sorted_predictions.sort(key=lambda x: x[2], reverse=True)

            for model_name, pred_price, r2 in sorted_predictions:
                result_text += f"{model_name:15}: {pred_price:8.2f} (R²: {r2:.4f})\n"

            # 앙상블 예측 (가중 평균)
            if len(predictions) > 1:
                weights = []
                values = []
                for model_name, pred_price in predictions.items():
                    r2 = self.model_results.get(model_name, {}).get('r2', 0)
                    if r2 > 0:  # 양의 R² 값만 사용
                        weights.append(r2)
                        values.append(pred_price)

                if weights:
                    ensemble_pred = np.average(values, weights=weights)
                    result_text += f"\n{'앙상블 예측':15}: {ensemble_pred:8.2f}\n"

            # 현재 가격과 비교
            current_price = self.stock_df['close'].iloc[-1]
            result_text += f"\n{'현재 가격':15}: {current_price:8.2f}\n"

            if 'ensemble_pred' in locals():
                change = ensemble_pred - current_price
                change_pct = (change / current_price) * 100
                result_text += f"{'예상 변화':15}: {change:+8.2f} ({change_pct:+.2f}%)\n"

            # 예측 결과 텍스트에 표시
            self.pred_text.delete(1.0, tk.END)
            self.pred_text.insert(tk.END, result_text)

            self.log_message("✅ 예측 완료")

        except Exception as e:
            self.log_message(f"❌ 예측 결과 표시 오류: {str(e)}")

    def real_time_prediction(self):
        """실시간 예측"""
        try:
            self.log_message("실시간 예측 기능은 개발 중입니다.")
            # 실제로는 실시간 데이터 수집 및 예측 로직 구현

        except Exception as e:
            self.log_message(f"❌ 실시간 예측 오류: {str(e)}")

    # ==================== 차트 기능 ====================

    def plot_stock_chart(self):
        """주가 차트 그리기"""
        if self.stock_df is None:
            self.log_message("❌ 먼저 주가 데이터를 로드해주세요.")
            return

        try:
            self.log_message("주가 차트 생성 중...")

            fig, axes = plt.subplots(2, 1, figsize=(15, 10), height_ratios=[3, 1])

            # 주가 차트
            ax1 = axes[0]
            ax1.plot(self.stock_df.index, self.stock_df['close'], label='종가', linewidth=1.5)

            if 'ma20' in self.stock_df.columns:
                ax1.plot(self.stock_df.index, self.stock_df['ma20'], label='MA20', alpha=0.7)

            ax1.set_title('주가 차트')
            ax1.set_ylabel('가격')
            ax1.legend()
            ax1.grid(True, alpha=0.3)

            # 거래량 차트
            if 'volume' in self.stock_df.columns:
                ax2 = axes[1]
                ax2.bar(self.stock_df.index, self.stock_df['volume'], alpha=0.7)
                ax2.set_title('거래량')
                ax2.set_ylabel('거래량')
                ax2.grid(True, alpha=0.3)

            plt.tight_layout()
            plt.show()

            self.log_message("✅ 주가 차트 생성 완료")

        except Exception as e:
            self.log_message(f"❌ 주가 차트 생성 오류: {str(e)}")

    def plot_correlation_heatmap(self):
        """상관관계 히트맵"""
        if self.combined_df is None:
            self.log_message("❌ 먼저 모델 학습을 실행해주세요.")
            return

        try:
            self.log_message("상관관계 히트맵 생성 중...")

            # 상관관계 계산
            corr_matrix = self.combined_df.corr()

            plt.figure(figsize=(12, 10))
            sns.heatmap(corr_matrix, annot=True, cmap='coolwarm', center=0,
                       square=True, fmt='.2f')
            plt.title('특성 간 상관관계')
            plt.tight_layout()
            plt.show()

            self.log_message("✅ 상관관계 히트맵 생성 완료")

        except Exception as e:
            self.log_message(f"❌ 상관관계 히트맵 생성 오류: {str(e)}")

    def plot_model_comparison(self):
        """모델 성능 비교 차트"""
        if not self.model_results:
            self.log_message("❌ 먼저 모델 학습을 실행해주세요.")
            return

        try:
            self.log_message("모델 성능 비교 차트 생성 중...")

            models = list(self.model_results.keys())
            r2_scores = [self.model_results[model]['r2'] for model in models]
            mae_scores = [self.model_results[model]['mae'] for model in models]

            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

            # R² 점수
            ax1.bar(models, r2_scores)
            ax1.set_title('모델별 R² 점수')
            ax1.set_ylabel('R² 점수')
            ax1.tick_params(axis='x', rotation=45)

            # MAE 점수
            ax2.bar(models, mae_scores)
            ax2.set_title('모델별 MAE')
            ax2.set_ylabel('MAE')
            ax2.tick_params(axis='x', rotation=45)

            plt.tight_layout()
            plt.show()

            self.log_message("✅ 모델 성능 비교 차트 생성 완료")

        except Exception as e:
            self.log_message(f"❌ 모델 성능 비교 차트 생성 오류: {str(e)}")

    def plot_prediction_comparison(self):
        """예측 vs 실제 비교 차트"""
        self.log_message("예측 vs 실제 비교 차트는 개발 중입니다.")

    def run(self):
        """애플리케이션 실행"""
        self.log_message("🚀 고도화된 주가 예측 AI 시스템 시작")
        self.root.mainloop()

    def load_file(self):
        """파일 로드 (백그라운드)"""
        try:
            self.status_var.set("파일 로딩 중...")
            self.log_message("CSV 파일 로딩 시작...")

            self.df = self.read_csv_ultimate(self.file_path)

            if self.df is not None:
                self.log_message(f"✅ 파일 로드 완료: {self.df.shape[0]}행 × {self.df.shape[1]}열")
                self.status_var.set("파일 로드 완료")

                # 기술적 지표 계산 (주식 데이터인 경우)
                if self.is_stock_data():
                    self.calculate_technical_indicators()
                    self.log_message("✅ 기술적 지표 계산 완료")
            else:
                self.log_message("❌ 파일 로드 실패")
                self.status_var.set("파일 로드 실패")

        except Exception as e:
            self.log_message(f"❌ 오류 발생: {str(e)}")
            self.status_var.set("오류 발생")

    def read_csv_ultimate(self, file_path):
        """Ultimate CSV 읽기 함수"""
        try:
            self.log_message("📁 CSV 파일 읽기 시작...")

            # 1단계: 인코딩 감지 및 읽기
            df = self._read_with_encoding_detection(file_path)

            # 2단계: 컬럼명 정리 및 매핑
            df = self._clean_and_map_columns(df)

            # 3단계: 데이터 타입 자동 변환
            df = self._auto_convert_datatypes(df)

            # 4단계: 날짜 인덱스 안전 설정
            df = self._safe_set_datetime_index(df)

            # 5단계: 최종 정리
            df = self._final_cleanup(df)

            return df

        except Exception as e:
            self.log_message(f"❌ CSV 읽기 실패: {str(e)}")
            return None

    def _read_with_encoding_detection(self, file_path):
        """다양한 인코딩으로 파일 읽기 시도"""
        encodings = ['utf-8-sig', 'utf-8', 'cp949', 'euc-kr', 'latin-1', 'cp1252']

        for encoding in encodings:
            try:
                df = pd.read_csv(file_path, encoding=encoding, low_memory=False)
                self.encoding_used = encoding
                self.log_message(f"✅ 인코딩 성공: {encoding}")
                return df
            except (UnicodeDecodeError, UnicodeError):
                continue
            except Exception as e:
                if encoding == encodings[-1]:
                    raise e
                continue

        raise Exception("지원되는 인코딩을 찾을 수 없습니다.")

    def _clean_and_map_columns(self, df):
        """컬럼명 정리 및 표준화"""
        self.original_columns = df.columns.tolist()

        # 컬럼명 정리
        cleaned_columns = []
        for col in df.columns:
            clean_col = str(col).strip().replace('﻿', '')
            clean_col = re.sub(r'[^\w\s가-힣]', '', clean_col)
            clean_col = re.sub(r'\s+', '_', clean_col)
            cleaned_columns.append(clean_col)

        df.columns = cleaned_columns

        # 표준 컬럼명 매핑
        column_mapping = {}
        for col in df.columns:
            col_lower = col.lower()

            if any(keyword in col_lower for keyword in ['date', 'time', '날짜', '일자', '시간']):
                column_mapping[col] = 'date'
            elif any(keyword in col_lower for keyword in ['open', '시가', '시작가']):
                column_mapping[col] = 'open'
            elif any(keyword in col_lower for keyword in ['high', '고가', '최고가']):
                column_mapping[col] = 'high'
            elif any(keyword in col_lower for keyword in ['low', '저가', '최저가']):
                column_mapping[col] = 'low'
            elif any(keyword in col_lower for keyword in ['close', '종가', '마감가', '끝가']):
                column_mapping[col] = 'close'
            elif any(keyword in col_lower for keyword in ['volume', '거래량', '볼륨']):
                column_mapping[col] = 'volume'

        if column_mapping:
            df = df.rename(columns=column_mapping)
            self.log_message(f"📝 컬럼 매핑 완료: {len(column_mapping)}개")

        return df

    def _auto_convert_datatypes(self, df):
        """데이터 타입 자동 변환"""
        for col in df.columns:
            if col == 'date':
                df[col] = self._convert_to_datetime(df[col])
            elif col in ['open', 'high', 'low', 'close', 'volume']:
                df[col] = self._convert_to_numeric(df[col])
            else:
                # 자동 감지 시도
                numeric_converted = self._convert_to_numeric(df[col], errors='ignore')
                if not numeric_converted.equals(df[col]):
                    df[col] = numeric_converted

        return df

    def _convert_to_datetime(self, series):
        """안전한 날짜 변환"""
        try:
            if series.dtype == 'object':
                cleaned = series.astype(str).str.strip()
                cleaned = cleaned.replace(['nan', 'NaN', 'null', 'NULL', ''], np.nan)
            else:
                cleaned = series

            converted = pd.to_datetime(cleaned, errors='coerce')
            success_rate = converted.notna().sum() / len(series)

            if success_rate < 0.3:
                return series

            return converted
        except:
            return series

    def _convert_to_numeric(self, series, errors='coerce'):
        """안전한 숫자 변환"""
        try:
            if series.dtype == 'object':
                cleaned = series.astype(str)
                cleaned = cleaned.str.replace(',', '')
                cleaned = cleaned.str.replace('"', '')
                cleaned = cleaned.str.replace("'", '')
                cleaned = cleaned.str.replace('(', '-')
                cleaned = cleaned.str.replace(')', '')
                cleaned = cleaned.str.replace(' ', '')
                cleaned = cleaned.str.replace(r'[^\d.-]', '', regex=True)
                cleaned = cleaned.replace(['', 'nan', 'NaN', 'null', 'NULL'], np.nan)
                converted = pd.to_numeric(cleaned, errors=errors)
            else:
                converted = pd.to_numeric(series, errors=errors)

            return converted
        except:
            return series

    def _safe_set_datetime_index(self, df):
        """안전한 날짜 인덱스 설정"""
        if 'date' in df.columns:
            try:
                if pd.api.types.is_datetime64_any_dtype(df['date']):
                    df = df.set_index('date')
                    df = df.sort_index()
                    df = df[~df.index.duplicated(keep='first')]
                    self.log_message("📅 날짜 인덱스 설정 완료")
            except Exception as e:
                self.log_message(f"⚠️ 날짜 인덱스 설정 실패: {str(e)}")

        return df

    def _final_cleanup(self, df):
        """최종 정리"""
        # 빈 행 제거
        before_rows = len(df)
        df = df.dropna(how='all')
        after_rows = len(df)

        if before_rows != after_rows:
            self.log_message(f"🧹 빈 행 제거: {before_rows - after_rows}행")

        return df

    def is_stock_data(self):
        """주식 데이터인지 확인"""
        if self.df is None:
            return False

        stock_columns = ['open', 'high', 'low', 'close']
        return all(col in self.df.columns for col in stock_columns)

    def calculate_technical_indicators(self):
        """기술적 지표 계산"""
        if not self.is_stock_data():
            return

        try:
            # 이동평균선
            self.df['ma5'] = self.df['close'].rolling(window=5).mean()
            self.df['ma20'] = self.df['close'].rolling(window=20).mean()
            self.df['ma60'] = self.df['close'].rolling(window=60).mean()

            # 볼린저 밴드
            self.df['bb_middle'] = self.df['close'].rolling(window=20).mean()
            bb_std = self.df['close'].rolling(window=20).std()
            self.df['bb_upper'] = self.df['bb_middle'] + (bb_std * 2)
            self.df['bb_lower'] = self.df['bb_middle'] - (bb_std * 2)

            # RSI
            delta = self.df['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            self.df['rsi'] = 100 - (100 / (1 + rs))

            # MACD
            exp1 = self.df['close'].ewm(span=12).mean()
            exp2 = self.df['close'].ewm(span=26).mean()
            self.df['macd'] = exp1 - exp2
            self.df['macd_signal'] = self.df['macd'].ewm(span=9).mean()
            self.df['macd_histogram'] = self.df['macd'] - self.df['macd_signal']

            # 일일 수익률
            self.df['daily_return'] = self.df['close'].pct_change()

            # 변동성
            self.df['volatility'] = self.df['daily_return'].rolling(window=20).std() * np.sqrt(252)

        except Exception as e:
            self.log_message(f"⚠️ 기술적 지표 계산 실패: {str(e)}")

    def safe_date_filter(self, start_date=None, end_date=None):
        """안전한 날짜 필터링"""
        if self.df is None:
            return None

        try:
            if not isinstance(self.df.index, pd.DatetimeIndex):
                self.log_message("⚠️ 날짜 인덱스가 아닙니다.")
                return self.df

            if start_date:
                start_date = pd.to_datetime(start_date)
            if end_date:
                end_date = pd.to_datetime(end_date)

            if not self.df.index.is_monotonic_increasing:
                self.df = self.df.sort_index()

            if start_date and end_date:
                mask = (self.df.index >= start_date) & (self.df.index <= end_date)
                result = self.df[mask]
            elif start_date:
                result = self.df[self.df.index >= start_date]
            elif end_date:
                result = self.df[self.df.index <= end_date]
            else:
                result = self.df

            self.log_message(f"📅 날짜 필터링: {len(self.df)} → {len(result)}행")
            return result

        except Exception as e:
            self.log_message(f"⚠️ 날짜 필터링 실패: {str(e)}")
            return self.df

    # GUI 이벤트 핸들러들
    def show_data_info(self):
        """데이터 정보 표시"""
        if self.df is None:
            self.log_message("❌ 먼저 파일을 선택해주세요.")
            return

        try:
            info_text = f"""
=== 데이터 정보 ===
파일: {os.path.basename(self.file_path) if self.file_path else 'N/A'}
인코딩: {self.encoding_used}
형태: {self.df.shape[0]}행 × {self.df.shape[1]}열
메모리: {self.df.memory_usage(deep=True).sum() / 1024 / 1024:.2f} MB

=== 컬럼 정보 ===
원본 컬럼: {self.original_columns}
현재 컬럼: {list(self.df.columns)}

=== 데이터 타입 ===
"""
            for col, dtype in self.df.dtypes.items():
                info_text += f"{col}: {dtype}\n"

            # 결측값 정보
            missing = self.df.isnull().sum()
            missing_info = missing[missing > 0]
            if len(missing_info) > 0:
                info_text += f"\n=== 결측값 ===\n"
                for col, count in missing_info.items():
                    info_text += f"{col}: {count}개\n"

            # 날짜 범위 정보
            if isinstance(self.df.index, pd.DatetimeIndex):
                info_text += f"\n=== 날짜 범위 ===\n"
                info_text += f"시작: {self.df.index.min()}\n"
                info_text += f"종료: {self.df.index.max()}\n"
                info_text += f"기간: {len(self.df)}일\n"

            self.log_message(info_text)

        except Exception as e:
            self.log_message(f"❌ 데이터 정보 표시 실패: {str(e)}")

    def preview_data(self):
        """데이터 미리보기"""
        if self.df is None:
            self.log_message("❌ 먼저 파일을 선택해주세요.")
            return

        try:
            self.log_message("=== 데이터 미리보기 (첫 10행) ===")
            self.log_message(str(self.df.head(10)))

            self.log_message("\n=== 데이터 미리보기 (마지막 5행) ===")
            self.log_message(str(self.df.tail(5)))

        except Exception as e:
            self.log_message(f"❌ 데이터 미리보기 실패: {str(e)}")

    def show_basic_stats(self):
        """기본 통계 표시"""
        if self.df is None:
            self.log_message("❌ 먼저 파일을 선택해주세요.")
            return

        try:
            self.log_message("=== 기본 통계 ===")
            self.log_message(str(self.df.describe()))

            if self.is_stock_data():
                self.log_message("\n=== 주식 데이터 요약 ===")
                summary = self.get_stock_summary()
                for key, value in summary.items():
                    self.log_message(f"{key}: {value}")

        except Exception as e:
            self.log_message(f"❌ 기본 통계 표시 실패: {str(e)}")

    def get_stock_summary(self):
        """주식 데이터 요약"""
        if not self.is_stock_data():
            return {}

        try:
            summary = {
                '시작가': f"{self.df['close'].iloc[0]:.2f}",
                '종료가': f"{self.df['close'].iloc[-1]:.2f}",
                '최고가': f"{self.df['high'].max():.2f}",
                '최저가': f"{self.df['low'].min():.2f}",
                '총 수익률': f"{((self.df['close'].iloc[-1] / self.df['close'].iloc[0]) - 1) * 100:.2f}%"
            }

            if 'daily_return' in self.df.columns:
                summary['평균 일일 수익률'] = f"{self.df['daily_return'].mean() * 100:.2f}%"
                summary['변동성(연환산)'] = f"{self.df['daily_return'].std() * np.sqrt(252) * 100:.2f}%"

            if 'volume' in self.df.columns and not self.df['volume'].isna().all():
                summary['평균 거래량'] = f"{self.df['volume'].mean():.0f}"

            return summary

        except Exception as e:
            self.log_message(f"⚠️ 주식 요약 계산 실패: {str(e)}")
            return {}

    def apply_date_filter(self):
        """날짜 필터 적용"""
        if self.df is None:
            self.log_message("❌ 먼저 파일을 선택해주세요.")
            return

        start_date = self.start_date_var.get() if self.start_date_var.get() else None
        end_date = self.end_date_var.get() if self.end_date_var.get() else None

        if not start_date and not end_date:
            self.log_message("⚠️ 시작일 또는 종료일을 입력해주세요.")
            return

        filtered_df = self.safe_date_filter(start_date, end_date)

        if filtered_df is not None:
            # 임시로 필터링된 데이터를 저장
            self.filtered_df = filtered_df
            self.log_message("✅ 날짜 필터 적용 완료. 차트 기능에서 필터링된 데이터를 사용합니다.")

    def plot_price_chart(self):
        """가격 차트 그리기"""
        if self.df is None:
            self.log_message("❌ 먼저 파일을 선택해주세요.")
            return

        if not self.is_stock_data():
            self.log_message("❌ 주식 데이터가 아닙니다.")
            return

        try:
            # 필터링된 데이터가 있으면 사용
            plot_df = getattr(self, 'filtered_df', self.df)

            self.log_message("📊 가격 차트를 생성하고 있습니다...")

            fig, axes = plt.subplots(3, 1, figsize=(15, 10), height_ratios=[3, 1, 1])

            # 1. 가격 차트
            ax1 = axes[0]
            ax1.plot(plot_df.index, plot_df['close'], label='종가', linewidth=1.5, color='black')

            if 'ma5' in plot_df.columns:
                ax1.plot(plot_df.index, plot_df['ma5'], label='MA5', alpha=0.7, color='red')
                ax1.plot(plot_df.index, plot_df['ma20'], label='MA20', alpha=0.7, color='blue')
                ax1.plot(plot_df.index, plot_df['ma60'], label='MA60', alpha=0.7, color='green')

            if 'bb_upper' in plot_df.columns:
                ax1.fill_between(plot_df.index, plot_df['bb_upper'], plot_df['bb_lower'],
                               alpha=0.2, color='gray', label='볼린저 밴드')

            ax1.set_title('주가 차트', fontsize=16, fontweight='bold')
            ax1.set_ylabel('가격')
            ax1.legend()
            ax1.grid(True, alpha=0.3)

            # 2. RSI
            if 'rsi' in plot_df.columns:
                ax2 = axes[1]
                ax2.plot(plot_df.index, plot_df['rsi'], color='purple', linewidth=1)
                ax2.axhline(y=70, color='r', linestyle='--', alpha=0.7)
                ax2.axhline(y=30, color='b', linestyle='--', alpha=0.7)
                ax2.set_title('RSI')
                ax2.set_ylabel('RSI')
                ax2.set_ylim(0, 100)
                ax2.grid(True, alpha=0.3)

            # 3. MACD
            if 'macd' in plot_df.columns:
                ax3 = axes[2]
                ax3.plot(plot_df.index, plot_df['macd'], label='MACD', color='blue')
                ax3.plot(plot_df.index, plot_df['macd_signal'], label='Signal', color='red')
                ax3.bar(plot_df.index, plot_df['macd_histogram'], alpha=0.6, color='gray', width=1)
                ax3.set_title('MACD')
                ax3.set_ylabel('MACD')
                ax3.set_xlabel('날짜')
                ax3.legend()
                ax3.grid(True, alpha=0.3)

            plt.tight_layout()
            plt.show()

            self.log_message("✅ 가격 차트 생성 완료")

        except Exception as e:
            self.log_message(f"❌ 가격 차트 생성 실패: {str(e)}")

    def plot_volume_analysis(self):
        """거래량 분석 차트"""
        if self.df is None or not self.is_stock_data():
            self.log_message("❌ 주식 데이터를 먼저 로드해주세요.")
            return

        if 'volume' not in self.df.columns or self.df['volume'].isna().all():
            self.log_message("❌ 거래량 데이터가 없습니다.")
            return

        try:
            plot_df = getattr(self, 'filtered_df', self.df)

            self.log_message("📊 거래량 분석 차트를 생성하고 있습니다...")

            fig, axes = plt.subplots(2, 1, figsize=(15, 8), height_ratios=[2, 1])

            # 1. 가격
            ax1 = axes[0]
            ax1.plot(plot_df.index, plot_df['close'], color='black', linewidth=1.5)
            ax1.set_title('주가와 거래량')
            ax1.set_ylabel('가격')
            ax1.grid(True, alpha=0.3)

            # 2. 거래량
            ax2 = axes[1]
            colors = ['red' if close >= open_price else 'blue'
                     for close, open_price in zip(plot_df['close'], plot_df['open'])]
            ax2.bar(plot_df.index, plot_df['volume'], color=colors, alpha=0.7, width=1)
            ax2.set_ylabel('거래량')
            ax2.set_xlabel('날짜')
            ax2.grid(True, alpha=0.3)

            plt.tight_layout()
            plt.show()

            self.log_message("✅ 거래량 분석 차트 생성 완료")

        except Exception as e:
            self.log_message(f"❌ 거래량 분석 차트 생성 실패: {str(e)}")

    def analyze_returns(self):
        """수익률 분석"""
        if self.df is None or not self.is_stock_data():
            self.log_message("❌ 주식 데이터를 먼저 로드해주세요.")
            return

        if 'daily_return' not in self.df.columns:
            self.log_message("❌ 수익률 데이터가 없습니다.")
            return

        try:
            self.log_message("📊 수익률 분석을 생성하고 있습니다...")

            fig, axes = plt.subplots(2, 2, figsize=(15, 10))

            # 1. 일일 수익률 히스토그램
            axes[0, 0].hist(self.df['daily_return'].dropna(), bins=50, alpha=0.7, color='blue')
            axes[0, 0].set_title('일일 수익률 분포')
            axes[0, 0].set_xlabel('수익률')
            axes[0, 0].set_ylabel('빈도')
            axes[0, 0].grid(True, alpha=0.3)

            # 2. 누적 수익률
            cumulative_returns = (1 + self.df['daily_return']).cumprod()
            axes[0, 1].plot(self.df.index, cumulative_returns, color='green', linewidth=1.5)
            axes[0, 1].set_title('누적 수익률')
            axes[0, 1].set_ylabel('누적 수익률')
            axes[0, 1].grid(True, alpha=0.3)

            # 3. 변동성 추이
            if 'volatility' in self.df.columns:
                axes[1, 0].plot(self.df.index, self.df['volatility'], color='red', linewidth=1)
                axes[1, 0].set_title('변동성 추이')
                axes[1, 0].set_ylabel('변동성')
                axes[1, 0].grid(True, alpha=0.3)

            # 4. 월별 수익률 박스플롯
            monthly_returns = self.df['daily_return'].resample('M').apply(lambda x: (1 + x).prod() - 1)
            axes[1, 1].boxplot([monthly_returns.dropna().values])
            axes[1, 1].set_title('월별 수익률 분포')
            axes[1, 1].set_ylabel('월별 수익률')
            axes[1, 1].grid(True, alpha=0.3)

            plt.tight_layout()
            plt.show()

            self.log_message("✅ 수익률 분석 완료")

        except Exception as e:
            self.log_message(f"❌ 수익률 분석 실패: {str(e)}")

    def show_trading_signals(self):
        """매매 신호 분석"""
        if self.df is None or not self.is_stock_data():
            self.log_message("❌ 주식 데이터를 먼저 로드해주세요.")
            return

        try:
            signals = self.get_recent_signals(30)

            if signals:
                self.log_message("=== 최근 30일 매매 신호 ===")
                for signal in signals[-10:]:  # 최근 10개만 표시
                    self.log_message(f"{signal['날짜']}: {signal['신호']} (종가: {signal['종가']:.2f})")
            else:
                self.log_message("최근 매매 신호가 없습니다.")

        except Exception as e:
            self.log_message(f"❌ 매매 신호 분석 실패: {str(e)}")

    def get_recent_signals(self, days=30):
        """최근 매매 신호 분석"""
        if not self.is_stock_data():
            return []

        try:
            recent_df = self.df.tail(days)
            signals = []

            for i, (date, row) in enumerate(recent_df.iterrows()):
                signal_list = []

                # RSI 신호
                if 'rsi' in row and not pd.isna(row['rsi']):
                    if row['rsi'] > 70:
                        signal_list.append("RSI 과매수")
                    elif row['rsi'] < 30:
                        signal_list.append("RSI 과매도")

                # MACD 신호
                if i > 0 and 'macd' in row and 'macd_signal' in row:
                    prev_row = recent_df.iloc[i-1]
                    if (not pd.isna(row['macd']) and not pd.isna(row['macd_signal']) and
                        not pd.isna(prev_row['macd']) and not pd.isna(prev_row['macd_signal'])):

                        if (row['macd'] > row['macd_signal'] and
                            prev_row['macd'] <= prev_row['macd_signal']):
                            signal_list.append("MACD 골든크로스")
                        elif (row['macd'] < row['macd_signal'] and
                              prev_row['macd'] >= prev_row['macd_signal']):
                            signal_list.append("MACD 데드크로스")

                # 볼린저 밴드 신호
                if ('bb_upper' in row and 'bb_lower' in row and
                    not pd.isna(row['bb_upper']) and not pd.isna(row['bb_lower'])):
                    if row['close'] > row['bb_upper']:
                        signal_list.append("볼린저 밴드 상단 돌파")
                    elif row['close'] < row['bb_lower']:
                        signal_list.append("볼린저 밴드 하단 이탈")

                if signal_list:
                    signals.append({
                        '날짜': date.strftime('%Y-%m-%d'),
                        '종가': row['close'],
                        '신호': ', '.join(signal_list)
                    })

            return signals

        except Exception as e:
            self.log_message(f"⚠️ 신호 분석 중 오류: {str(e)}")
            return []

    def run(self):
        """애플리케이션 실행"""
        self.log_message("🚀 통합 CSV 처리 및 주식 데이터 분석 도구 시작")
        self.log_message("📁 파일 선택 버튼을 클릭하여 CSV 파일을 선택하세요.")
        self.root.mainloop()

def main():
    """메인 함수"""
    try:
        app = IntegratedCSVAnalyzer()
        app.run()
    except Exception as e:
        print(f"애플리케이션 실행 중 오류 발생: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
