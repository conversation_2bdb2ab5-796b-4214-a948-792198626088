"""
고도화된 주가 예측 AI 시스템
- 다중 데이터 소스 통합 (주가, 날씨, 키워드)
- 실시간 키워드 분석 및 상관관계 분석
- 다중 AI 알고리즘 비교 및 AutoML
- 키워드 영향도 시차 분석
- 누적 학습 및 예측 정확도 개선
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import re
from datetime import datetime, timedelta
import warnings
import os
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading
import requests
from bs4 import BeautifulSoup
import json
import sqlite3
from pytrends.request import TrendReq
from scipy.stats import pearsonr
import yfinance as yf

# ML 라이브러리
from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.linear_model import LinearRegression, Ridge, Lasso, ElasticNet
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor, ExtraTreesRegressor
from sklearn.svm import SVR
from sklearn.neural_network import MLPRegressor
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
import xgboost as xgb
import lightgbm as lgb
from catboost import CatBoostRegressor

# AutoML
try:
    from autosklearn.regression import AutoSklearnRegressor
    AUTOSKLEARN_AVAILABLE = True
except ImportError:
    AUTOSKLEARN_AVAILABLE = False

try:
    from tpot import TPOTRegressor
    TPOT_AVAILABLE = True
except ImportError:
    TPOT_AVAILABLE = False

warnings.filterwarnings('ignore')

# 한글 폰트 설정
plt.rcParams['font.family'] = 'Malgun Gothic'
plt.rcParams['axes.unicode_minus'] = False

class AdvancedStockPredictionSystem:
    """고도화된 주가 예측 AI 시스템"""

    def __init__(self):
        # 데이터 저장
        self.stock_df = None
        self.weather_df = None
        self.keyword_df = None
        self.combined_df = None
        self.original_columns = None
        self.encoding_used = None
        self.file_path = None

        # 키워드 및 상관관계 데이터
        self.keyword_correlations = {}
        self.keyword_weights = {}
        self.lag_effects = {}
        self.user_keywords = []
        self.ai_keywords = []

        # 모델 및 예측 결과
        self.models = {}
        self.model_results = {}
        self.predictions = {}

        # 데이터베이스 초기화
        self.init_database()

        # GUI 초기화
        self.root = tk.Tk()
        self.root.title("고도화된 주가 예측 AI 시스템")
        self.root.geometry("1400x900")
        self.root.state('zoomed')  # 최대화

        self.setup_gui()

    def init_database(self):
        """데이터베이스 초기화"""
        try:
            self.conn = sqlite3.connect('stock_prediction.db')
            cursor = self.conn.cursor()

            # 키워드 상관관계 테이블
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS keyword_correlations (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    keyword TEXT NOT NULL,
                    correlation REAL NOT NULL,
                    weight REAL DEFAULT 1.0,
                    lag_days INTEGER DEFAULT 0,
                    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # 예측 결과 테이블
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS prediction_results (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    model_name TEXT NOT NULL,
                    mae REAL,
                    r2_score REAL,
                    prediction_date DATE,
                    predicted_price REAL,
                    actual_price REAL,
                    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            self.conn.commit()
        except Exception as e:
            print(f"데이터베이스 초기화 오류: {str(e)}")

    def setup_gui(self):
        """고도화된 GUI 설정"""
        # 메인 노트북 (탭)
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 1. 데이터 로드 탭
        self.setup_data_tab()

        # 2. 키워드 분석 탭
        self.setup_keyword_tab()

        # 3. 모델 학습 탭
        self.setup_model_tab()

        # 4. 예측 탭
        self.setup_prediction_tab()

        # 5. 결과 분석 탭
        self.setup_analysis_tab()

        # 상태바
        self.status_var = tk.StringVar(value="시스템 준비 완료")
        status_bar = ttk.Label(self.root, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.pack(fill=tk.X, padx=10, pady=(0, 10))

    def setup_data_tab(self):
        """데이터 로드 탭 설정"""
        data_frame = ttk.Frame(self.notebook)
        self.notebook.add(data_frame, text="📊 데이터 로드")

        # 주가 데이터 섹션
        stock_frame = ttk.LabelFrame(data_frame, text="주가 데이터", padding="10")
        stock_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Button(stock_frame, text="주가 CSV 파일 선택", command=self.select_stock_file).pack(side=tk.LEFT, padx=5)
        self.stock_file_label = ttk.Label(stock_frame, text="파일이 선택되지 않았습니다.")
        self.stock_file_label.pack(side=tk.LEFT, padx=10)

        # 날씨 데이터 섹션
        weather_frame = ttk.LabelFrame(data_frame, text="날씨 데이터", padding="10")
        weather_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Button(weather_frame, text="날씨 CSV 파일 선택", command=self.select_weather_file).pack(side=tk.LEFT, padx=5)
        ttk.Button(weather_frame, text="날씨 데이터 다운로드", command=self.download_weather_data).pack(side=tk.LEFT, padx=5)
        self.weather_file_label = ttk.Label(weather_frame, text="파일이 선택되지 않았습니다.")
        self.weather_file_label.pack(side=tk.LEFT, padx=10)

        # 데이터 미리보기
        preview_frame = ttk.LabelFrame(data_frame, text="데이터 미리보기", padding="10")
        preview_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # 텍스트 위젯
        text_frame = ttk.Frame(preview_frame)
        text_frame.pack(fill=tk.BOTH, expand=True)

        self.data_text = tk.Text(text_frame, wrap=tk.WORD, height=20)
        data_scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=self.data_text.yview)
        self.data_text.configure(yscrollcommand=data_scrollbar.set)

        self.data_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        data_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def setup_keyword_tab(self):
        """키워드 분석 탭 설정"""
        keyword_frame = ttk.Frame(self.notebook)
        self.notebook.add(keyword_frame, text="🔍 키워드 분석")

        # 키워드 입력 섹션
        input_frame = ttk.LabelFrame(keyword_frame, text="키워드 설정", padding="10")
        input_frame.pack(fill=tk.X, padx=10, pady=5)

        # 사용자 키워드
        ttk.Label(input_frame, text="사용자 키워드 (쉼표로 구분):").pack(anchor=tk.W)
        self.user_keywords_var = tk.StringVar()
        ttk.Entry(input_frame, textvariable=self.user_keywords_var, width=80).pack(fill=tk.X, pady=2)

        # 사이트 URL
        ttk.Label(input_frame, text="분석할 사이트 URL:").pack(anchor=tk.W, pady=(10, 0))
        self.site_url_var = tk.StringVar()
        ttk.Entry(input_frame, textvariable=self.site_url_var, width=80).pack(fill=tk.X, pady=2)

        # 버튼들
        button_frame = ttk.Frame(input_frame)
        button_frame.pack(fill=tk.X, pady=10)

        ttk.Button(button_frame, text="AI 키워드 추천", command=self.get_ai_keywords).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="키워드 분석 시작", command=self.analyze_keywords).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="상관관계 분석", command=self.analyze_correlations).pack(side=tk.LEFT, padx=5)

        # 시차 효과 설정
        lag_frame = ttk.LabelFrame(keyword_frame, text="시차 효과 설정", padding="10")
        lag_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Label(lag_frame, text="분석할 시차 (일):").pack(side=tk.LEFT)
        self.lag_days = tk.StringVar(value="1,2,3,4,5,7,14,30,60,90")
        ttk.Entry(lag_frame, textvariable=self.lag_days, width=40).pack(side=tk.LEFT, padx=10)

        # 키워드 결과 표시
        result_frame = ttk.LabelFrame(keyword_frame, text="키워드 분석 결과", padding="10")
        result_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # 트리뷰 (테이블)
        columns = ('키워드', '상관계수', '가중치', '최적시차', 'P값')
        self.keyword_tree = ttk.Treeview(result_frame, columns=columns, show='headings', height=15)

        for col in columns:
            self.keyword_tree.heading(col, text=col)
            self.keyword_tree.column(col, width=100)

        keyword_scrollbar = ttk.Scrollbar(result_frame, orient=tk.VERTICAL, command=self.keyword_tree.yview)
        self.keyword_tree.configure(yscrollcommand=keyword_scrollbar.set)

        self.keyword_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        keyword_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 가중치 수정 버튼
        ttk.Button(result_frame, text="가중치 수정", command=self.modify_weights).pack(pady=5)

    def setup_model_tab(self):
        """모델 학습 탭 설정"""
        model_frame = ttk.Frame(self.notebook)
        self.notebook.add(model_frame, text="🤖 AI 모델")

        # 모델 선택 섹션
        selection_frame = ttk.LabelFrame(model_frame, text="모델 선택", padding="10")
        selection_frame.pack(fill=tk.X, padx=10, pady=5)

        # 모델 체크박스들
        self.model_vars = {}
        models = [
            ('Linear Regression', 'linear'),
            ('Ridge Regression', 'ridge'),
            ('Lasso Regression', 'lasso'),
            ('Random Forest', 'rf'),
            ('XGBoost', 'xgb'),
            ('LightGBM', 'lgb'),
            ('CatBoost', 'catboost'),
            ('Neural Network', 'nn'),
            ('SVR', 'svr')
        ]

        for i, (name, key) in enumerate(models):
            var = tk.BooleanVar(value=True)
            self.model_vars[key] = var
            ttk.Checkbutton(selection_frame, text=name, variable=var).grid(row=i//3, column=i%3, sticky=tk.W, padx=10, pady=2)

        # AutoML 옵션
        automl_frame = ttk.LabelFrame(model_frame, text="AutoML 옵션", padding="10")
        automl_frame.pack(fill=tk.X, padx=10, pady=5)

        self.use_automl = tk.BooleanVar()
        ttk.Checkbutton(automl_frame, text="AutoML 사용 (TPOT)", variable=self.use_automl).pack(side=tk.LEFT)

        # 학습 버튼
        ttk.Button(model_frame, text="모델 학습 시작", command=self.train_models).pack(pady=20)

        # 모델 결과 표시
        results_frame = ttk.LabelFrame(model_frame, text="모델 성능 비교", padding="10")
        results_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # 결과 트리뷰
        result_columns = ('모델명', 'MAE', 'RMSE', 'R²', '학습시간', '상태')
        self.model_tree = ttk.Treeview(results_frame, columns=result_columns, show='headings', height=10)

        for col in result_columns:
            self.model_tree.heading(col, text=col)
            self.model_tree.column(col, width=100)

        model_scrollbar = ttk.Scrollbar(results_frame, orient=tk.VERTICAL, command=self.model_tree.yview)
        self.model_tree.configure(yscrollcommand=model_scrollbar.set)

        self.model_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        model_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def setup_prediction_tab(self):
        """예측 탭 설정"""
        pred_frame = ttk.Frame(self.notebook)
        self.notebook.add(pred_frame, text="📈 예측")

        # 예측 설정
        settings_frame = ttk.LabelFrame(pred_frame, text="예측 설정", padding="10")
        settings_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Label(settings_frame, text="예측 날짜:").pack(side=tk.LEFT)
        self.pred_date_var = tk.StringVar(value=datetime.now().strftime('%Y-%m-%d'))
        ttk.Entry(settings_frame, textvariable=self.pred_date_var, width=15).pack(side=tk.LEFT, padx=10)

        ttk.Button(settings_frame, text="예측 실행", command=self.make_prediction).pack(side=tk.LEFT, padx=20)
        ttk.Button(settings_frame, text="실시간 예측", command=self.real_time_prediction).pack(side=tk.LEFT, padx=5)

        # 예측 결과 표시
        result_frame = ttk.LabelFrame(pred_frame, text="예측 결과", padding="10")
        result_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # 예측 결과 텍스트
        pred_text_frame = ttk.Frame(result_frame)
        pred_text_frame.pack(fill=tk.BOTH, expand=True)

        self.pred_text = tk.Text(pred_text_frame, wrap=tk.WORD, height=20)
        pred_scrollbar = ttk.Scrollbar(pred_text_frame, orient=tk.VERTICAL, command=self.pred_text.yview)
        self.pred_text.configure(yscrollcommand=pred_scrollbar.set)

        self.pred_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        pred_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def setup_analysis_tab(self):
        """결과 분석 탭 설정"""
        analysis_frame = ttk.Frame(self.notebook)
        self.notebook.add(analysis_frame, text="📊 분석 결과")

        # 차트 버튼들
        chart_frame = ttk.LabelFrame(analysis_frame, text="차트 생성", padding="10")
        chart_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Button(chart_frame, text="주가 차트", command=self.plot_stock_chart).pack(side=tk.LEFT, padx=5)
        ttk.Button(chart_frame, text="상관관계 히트맵", command=self.plot_correlation_heatmap).pack(side=tk.LEFT, padx=5)
        ttk.Button(chart_frame, text="모델 성능 비교", command=self.plot_model_comparison).pack(side=tk.LEFT, padx=5)
        ttk.Button(chart_frame, text="예측 vs 실제", command=self.plot_prediction_comparison).pack(side=tk.LEFT, padx=5)

        # 로그 표시
        log_frame = ttk.LabelFrame(analysis_frame, text="시스템 로그", padding="10")
        log_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        log_text_frame = ttk.Frame(log_frame)
        log_text_frame.pack(fill=tk.BOTH, expand=True)

        self.log_text = tk.Text(log_text_frame, wrap=tk.WORD, height=20)
        log_scrollbar = ttk.Scrollbar(log_text_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)

        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def log_message(self, message):
        """로그 메시지 출력"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        log_entry = f"{timestamp} - {message}\n"

        # 로그 텍스트에 추가
        if hasattr(self, 'log_text'):
            self.log_text.insert(tk.END, log_entry)
            self.log_text.see(tk.END)

        # 상태바 업데이트
        self.status_var.set(message)
        self.root.update_idletasks()

        print(log_entry.strip())  # 콘솔에도 출력

    # ==================== 데이터 로드 기능 ====================

    def select_stock_file(self):
        """주가 CSV 파일 선택"""
        file_path = filedialog.askopenfilename(
            title="주가 CSV 파일 선택",
            filetypes=[("CSV 파일", "*.csv"), ("모든 파일", "*.*")]
        )

        if file_path:
            self.file_path = file_path
            self.stock_file_label.config(text=os.path.basename(file_path))
            self.log_message(f"주가 파일 선택됨: {os.path.basename(file_path)}")

            # 백그라운드에서 파일 로드
            threading.Thread(target=self.load_stock_file, daemon=True).start()

    def select_weather_file(self):
        """날씨 CSV 파일 선택"""
        file_path = filedialog.askopenfilename(
            title="날씨 CSV 파일 선택",
            filetypes=[("CSV 파일", "*.csv"), ("모든 파일", "*.*")]
        )

        if file_path:
            self.weather_file_label.config(text=os.path.basename(file_path))
            self.log_message(f"날씨 파일 선택됨: {os.path.basename(file_path)}")

            # 백그라운드에서 파일 로드
            threading.Thread(target=self.load_weather_file, args=(file_path,), daemon=True).start()

    def load_stock_file(self):
        """주가 파일 로드"""
        try:
            self.log_message("주가 데이터 로딩 중...")

            # CSV 파일 읽기 (유연한 방식)
            self.stock_df = self.read_csv_ultimate(self.file_path)

            if self.stock_df is not None:
                self.log_message(f"✅ 주가 데이터 로드 완료: {self.stock_df.shape[0]}행 × {self.stock_df.shape[1]}열")

                # 기술적 지표 계산
                if self.is_stock_data():
                    self.calculate_technical_indicators()
                    self.log_message("✅ 기술적 지표 계산 완료")

                # 데이터 미리보기 업데이트
                self.update_data_preview()
            else:
                self.log_message("❌ 주가 파일 로드 실패")

        except Exception as e:
            self.log_message(f"❌ 주가 파일 로드 오류: {str(e)}")

    def load_weather_file(self, file_path):
        """날씨 파일 로드"""
        try:
            self.log_message("날씨 데이터 로딩 중...")

            self.weather_df = self.read_csv_ultimate(file_path)

            if self.weather_df is not None:
                self.log_message(f"✅ 날씨 데이터 로드 완료: {self.weather_df.shape[0]}행 × {self.weather_df.shape[1]}열")
                self.update_data_preview()
            else:
                self.log_message("❌ 날씨 파일 로드 실패")

        except Exception as e:
            self.log_message(f"❌ 날씨 파일 로드 오류: {str(e)}")

    def download_weather_data(self):
        """날씨 데이터 다운로드 (API 사용)"""
        try:
            self.log_message("날씨 데이터 다운로드 시작...")

            # 간단한 날씨 데이터 생성 (실제로는 API 사용)
            if self.stock_df is not None and isinstance(self.stock_df.index, pd.DatetimeIndex):
                dates = self.stock_df.index

                # 가상의 날씨 데이터 생성
                np.random.seed(42)
                weather_data = {
                    'date': dates,
                    'temperature': np.random.normal(20, 10, len(dates)),
                    'humidity': np.random.normal(60, 20, len(dates)),
                    'pressure': np.random.normal(1013, 20, len(dates)),
                    'wind_speed': np.random.exponential(5, len(dates)),
                    'precipitation': np.random.exponential(2, len(dates))
                }

                self.weather_df = pd.DataFrame(weather_data)
                self.weather_df.set_index('date', inplace=True)

                self.log_message("✅ 날씨 데이터 다운로드 완료 (가상 데이터)")
                self.weather_file_label.config(text="날씨 데이터 다운로드됨")
                self.update_data_preview()
            else:
                self.log_message("❌ 먼저 주가 데이터를 로드해주세요.")

        except Exception as e:
            self.log_message(f"❌ 날씨 데이터 다운로드 오류: {str(e)}")

    def update_data_preview(self):
        """데이터 미리보기 업데이트"""
        try:
            preview_text = ""

            if self.stock_df is not None:
                preview_text += "=== 주가 데이터 ===\n"
                preview_text += f"형태: {self.stock_df.shape}\n"
                preview_text += f"컬럼: {list(self.stock_df.columns)}\n"
                preview_text += f"기간: {self.stock_df.index.min()} ~ {self.stock_df.index.max()}\n\n"
                preview_text += str(self.stock_df.head()) + "\n\n"

            if self.weather_df is not None:
                preview_text += "=== 날씨 데이터 ===\n"
                preview_text += f"형태: {self.weather_df.shape}\n"
                preview_text += f"컬럼: {list(self.weather_df.columns)}\n"
                preview_text += str(self.weather_df.head()) + "\n\n"

            self.data_text.delete(1.0, tk.END)
            self.data_text.insert(tk.END, preview_text)

        except Exception as e:
            self.log_message(f"❌ 데이터 미리보기 업데이트 오류: {str(e)}")

    # ==================== CSV 읽기 기능 (기존 코드 재사용) ====================

    def read_csv_ultimate(self, file_path):
        """Ultimate CSV 읽기 함수"""
        try:
            # 1단계: 인코딩 감지 및 읽기
            df = self._read_with_encoding_detection(file_path)

            # 2단계: 컬럼명 정리 및 매핑
            df = self._clean_and_map_columns(df)

            # 3단계: 데이터 타입 자동 변환
            df = self._auto_convert_datatypes(df)

            # 4단계: 날짜 인덱스 안전 설정
            df = self._safe_set_datetime_index(df)

            # 5단계: 최종 정리
            df = self._final_cleanup(df)

            return df

        except Exception as e:
            self.log_message(f"❌ CSV 읽기 실패: {str(e)}")
            return None

    def _read_with_encoding_detection(self, file_path):
        """다양한 인코딩으로 파일 읽기 시도"""
        encodings = ['utf-8-sig', 'utf-8', 'cp949', 'euc-kr', 'latin-1', 'cp1252']

        for encoding in encodings:
            try:
                df = pd.read_csv(file_path, encoding=encoding, low_memory=False)
                self.encoding_used = encoding
                return df
            except (UnicodeDecodeError, UnicodeError):
                continue
            except Exception as e:
                if encoding == encodings[-1]:
                    raise e
                continue

        raise Exception("지원되는 인코딩을 찾을 수 없습니다.")

    def _clean_and_map_columns(self, df):
        """컬럼명 정리 및 표준화"""
        self.original_columns = df.columns.tolist()

        # 컬럼명 정리
        cleaned_columns = []
        for col in df.columns:
            clean_col = str(col).strip().replace('﻿', '')
            clean_col = re.sub(r'[^\w\s가-힣]', '', clean_col)
            clean_col = re.sub(r'\s+', '_', clean_col)
            cleaned_columns.append(clean_col)

        df.columns = cleaned_columns

        # 표준 컬럼명 매핑
        column_mapping = {}
        for col in df.columns:
            col_lower = col.lower()

            if any(keyword in col_lower for keyword in ['date', 'time', '날짜', '일자', '시간']):
                column_mapping[col] = 'date'
            elif any(keyword in col_lower for keyword in ['open', '시가', '시작가']):
                column_mapping[col] = 'open'
            elif any(keyword in col_lower for keyword in ['high', '고가', '최고가']):
                column_mapping[col] = 'high'
            elif any(keyword in col_lower for keyword in ['low', '저가', '최저가']):
                column_mapping[col] = 'low'
            elif any(keyword in col_lower for keyword in ['close', '종가', '마감가', '끝가']):
                column_mapping[col] = 'close'
            elif any(keyword in col_lower for keyword in ['volume', '거래량', '볼륨']):
                column_mapping[col] = 'volume'

        if column_mapping:
            df = df.rename(columns=column_mapping)

        return df

    def _auto_convert_datatypes(self, df):
        """데이터 타입 자동 변환"""
        for col in df.columns:
            if col == 'date':
                df[col] = self._convert_to_datetime(df[col])
            elif col in ['open', 'high', 'low', 'close', 'volume']:
                df[col] = self._convert_to_numeric(df[col])
            else:
                # 자동 감지 시도
                numeric_converted = self._convert_to_numeric(df[col], errors='ignore')
                if not numeric_converted.equals(df[col]):
                    df[col] = numeric_converted

        return df

    def _convert_to_datetime(self, series):
        """안전한 날짜 변환"""
        try:
            if series.dtype == 'object':
                cleaned = series.astype(str).str.strip()
                cleaned = cleaned.replace(['nan', 'NaN', 'null', 'NULL', ''], np.nan)
            else:
                cleaned = series

            converted = pd.to_datetime(cleaned, errors='coerce')
            success_rate = converted.notna().sum() / len(series)

            if success_rate < 0.3:
                return series

            return converted
        except:
            return series

    def _convert_to_numeric(self, series, errors='coerce'):
        """안전한 숫자 변환"""
        try:
            if series.dtype == 'object':
                cleaned = series.astype(str)
                cleaned = cleaned.str.replace(',', '')
                cleaned = cleaned.str.replace('"', '')
                cleaned = cleaned.str.replace("'", '')
                cleaned = cleaned.str.replace('(', '-')
                cleaned = cleaned.str.replace(')', '')
                cleaned = cleaned.str.replace(' ', '')
                cleaned = cleaned.str.replace(r'[^\d.-]', '', regex=True)
                cleaned = cleaned.replace(['', 'nan', 'NaN', 'null', 'NULL'], np.nan)
                converted = pd.to_numeric(cleaned, errors=errors)
            else:
                converted = pd.to_numeric(series, errors=errors)

            return converted
        except:
            return series

    def _safe_set_datetime_index(self, df):
        """안전한 날짜 인덱스 설정"""
        if 'date' in df.columns:
            try:
                if pd.api.types.is_datetime64_any_dtype(df['date']):
                    df = df.set_index('date')
                    df = df.sort_index()
                    df = df[~df.index.duplicated(keep='first')]
            except Exception as e:
                self.log_message(f"⚠️ 날짜 인덱스 설정 실패: {str(e)}")

        return df

    def _final_cleanup(self, df):
        """최종 정리"""
        # 빈 행 제거
        before_rows = len(df)
        df = df.dropna(how='all')
        after_rows = len(df)

        if before_rows != after_rows:
            self.log_message(f"🧹 빈 행 제거: {before_rows - after_rows}행")

        return df

    def is_stock_data(self):
        """주식 데이터인지 확인"""
        if self.stock_df is None:
            return False

        stock_columns = ['open', 'high', 'low', 'close']
        return all(col in self.stock_df.columns for col in stock_columns)

    def calculate_technical_indicators(self):
        """기술적 지표 계산"""
        if not self.is_stock_data():
            return

        try:
            # 이동평균선
            self.stock_df['ma5'] = self.stock_df['close'].rolling(window=5).mean()
            self.stock_df['ma20'] = self.stock_df['close'].rolling(window=20).mean()
            self.stock_df['ma60'] = self.stock_df['close'].rolling(window=60).mean()

            # 볼린저 밴드
            self.stock_df['bb_middle'] = self.stock_df['close'].rolling(window=20).mean()
            bb_std = self.stock_df['close'].rolling(window=20).std()
            self.stock_df['bb_upper'] = self.stock_df['bb_middle'] + (bb_std * 2)
            self.stock_df['bb_lower'] = self.stock_df['bb_middle'] - (bb_std * 2)

            # RSI
            delta = self.stock_df['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            self.stock_df['rsi'] = 100 - (100 / (1 + rs))

            # MACD
            exp1 = self.stock_df['close'].ewm(span=12).mean()
            exp2 = self.stock_df['close'].ewm(span=26).mean()
            self.stock_df['macd'] = exp1 - exp2
            self.stock_df['macd_signal'] = self.stock_df['macd'].ewm(span=9).mean()
            self.stock_df['macd_histogram'] = self.stock_df['macd'] - self.stock_df['macd_signal']

            # 일일 수익률
            self.stock_df['daily_return'] = self.stock_df['close'].pct_change()

            # 변동성
            self.stock_df['volatility'] = self.stock_df['daily_return'].rolling(window=20).std() * np.sqrt(252)

        except Exception as e:
            self.log_message(f"⚠️ 기술적 지표 계산 실패: {str(e)}")

    # ==================== 키워드 분석 기능 ====================

    def get_ai_keywords(self):
        """AI가 추천하는 키워드 생성"""
        try:
            self.log_message("AI 키워드 추천 시작...")

            # 주식 관련 기본 키워드들
            base_keywords = [
                "stock market", "economy", "inflation", "interest rate", "GDP",
                "unemployment", "earnings", "revenue", "profit", "loss",
                "merger", "acquisition", "IPO", "dividend", "buyback"
            ]

            # 산업별 키워드 (주가 데이터에 따라 동적 생성 가능)
            industry_keywords = [
                "technology", "healthcare", "finance", "energy", "retail",
                "manufacturing", "real estate", "telecommunications", "utilities"
            ]

            # 경제 지표 키워드
            economic_keywords = [
                "federal reserve", "central bank", "monetary policy", "fiscal policy",
                "trade war", "tariff", "currency", "oil price", "gold price"
            ]

            self.ai_keywords = base_keywords + industry_keywords + economic_keywords

            # 사용자 키워드 필드에 추가
            current_keywords = self.user_keywords_var.get()
            if current_keywords:
                all_keywords = current_keywords + ", " + ", ".join(self.ai_keywords)
            else:
                all_keywords = ", ".join(self.ai_keywords)

            self.user_keywords_var.set(all_keywords)

            self.log_message(f"✅ AI 키워드 {len(self.ai_keywords)}개 추천 완료")

        except Exception as e:
            self.log_message(f"❌ AI 키워드 추천 오류: {str(e)}")

    def analyze_keywords(self):
        """키워드 분석 시작"""
        try:
            self.log_message("키워드 분석 시작...")

            # 사용자 키워드 파싱
            keywords_text = self.user_keywords_var.get()
            if not keywords_text.strip():
                self.log_message("❌ 키워드를 입력해주세요.")
                return

            self.user_keywords = [kw.strip() for kw in keywords_text.split(',') if kw.strip()]

            # 백그라운드에서 키워드 분석 실행
            threading.Thread(target=self.analyze_keywords_thread, daemon=True).start()

        except Exception as e:
            self.log_message(f"❌ 키워드 분석 오류: {str(e)}")

    def analyze_keywords_thread(self):
        """키워드 분석 스레드"""
        try:
            # Google Trends 데이터 수집
            self.collect_google_trends()

            # 사이트 키워드 분석
            if self.site_url_var.get().strip():
                self.analyze_site_keywords()

            # 상관관계 분석
            self.analyze_correlations()

        except Exception as e:
            self.log_message(f"❌ 키워드 분석 스레드 오류: {str(e)}")

    def collect_google_trends(self):
        """Google Trends 데이터 수집"""
        try:
            self.log_message("Google Trends 데이터 수집 중...")

            if self.stock_df is None:
                self.log_message("❌ 먼저 주가 데이터를 로드해주세요.")
                return

            # 날짜 범위 설정
            start_date = self.stock_df.index.min().strftime('%Y-%m-%d')
            end_date = self.stock_df.index.max().strftime('%Y-%m-%d')

            keyword_data = {}

            # 키워드가 없으면 기본 키워드 사용
            if not self.user_keywords:
                self.log_message("⚠️ 키워드가 없어서 기본 키워드를 사용합니다.")
                self.user_keywords = ['stock market', 'economy', 'inflation']

            try:
                # PyTrends 초기화
                pytrends = TrendReq(hl='en-US', tz=360)

                for i, keyword in enumerate(self.user_keywords):
                    try:
                        self.log_message(f"키워드 '{keyword}' 분석 중... ({i+1}/{len(self.user_keywords)})")

                        # Google Trends 데이터 요청
                        pytrends.build_payload([keyword], timeframe=f'{start_date} {end_date}')
                        trends_df = pytrends.interest_over_time()

                        if not trends_df.empty and keyword in trends_df.columns:
                            # 날짜 인덱스 맞추기
                            trends_df.index = pd.to_datetime(trends_df.index)
                            keyword_data[keyword] = trends_df[keyword]

                            self.log_message(f"✅ '{keyword}' 데이터 수집 완료")
                        else:
                            self.log_message(f"⚠️ '{keyword}' 데이터 없음")

                        # API 호출 제한을 위한 대기
                        import time
                        time.sleep(1)

                    except Exception as e:
                        self.log_message(f"⚠️ '{keyword}' 수집 실패: {str(e)}")
                        continue

            except Exception as e:
                self.log_message(f"⚠️ Google Trends API 오류: {str(e)}")
                self.log_message("📝 가상 키워드 데이터를 생성합니다...")

                # Google Trends 실패 시 가상 데이터 생성
                self.create_dummy_keyword_data()
                return

            # 키워드 데이터프레임 생성
            if keyword_data:
                self.keyword_df = pd.DataFrame(keyword_data)
                self.log_message(f"✅ Google Trends 데이터 수집 완료: {len(keyword_data)}개 키워드")
            else:
                self.log_message("⚠️ Google Trends 데이터 수집 실패, 가상 데이터를 생성합니다...")
                self.create_dummy_keyword_data()

        except Exception as e:
            self.log_message(f"❌ Google Trends 수집 오류: {str(e)}")
            self.log_message("📝 가상 키워드 데이터를 생성합니다...")
            self.create_dummy_keyword_data()

    def create_dummy_keyword_data(self):
        """가상 키워드 데이터 생성 (Google Trends 실패 시)"""
        try:
            if self.stock_df is None:
                return

            # 주가 데이터와 같은 날짜 범위로 가상 키워드 데이터 생성
            dates = self.stock_df.index

            # 키워드가 없으면 기본 키워드 사용
            if not self.user_keywords:
                self.user_keywords = ['stock market', 'economy', 'inflation']

            keyword_data = {}
            np.random.seed(42)  # 재현 가능한 결과를 위해

            for keyword in self.user_keywords:
                # 주가와 약간의 상관관계를 가진 가상 데이터 생성
                base_trend = np.random.normal(50, 20, len(dates))

                # 주가 변화와 약간의 상관관계 추가
                if 'close' in self.stock_df.columns:
                    price_changes = self.stock_df['close'].pct_change().fillna(0)
                    correlation_factor = np.random.uniform(0.1, 0.3)
                    base_trend += price_changes * correlation_factor * 100

                # 0-100 범위로 정규화
                base_trend = np.clip(base_trend, 0, 100)
                keyword_data[keyword] = base_trend

            self.keyword_df = pd.DataFrame(keyword_data, index=dates)
            self.log_message(f"✅ 가상 키워드 데이터 생성 완료: {len(keyword_data)}개 키워드")

        except Exception as e:
            self.log_message(f"❌ 가상 키워드 데이터 생성 실패: {str(e)}")

    def analyze_site_keywords(self):
        """사이트 키워드 분석"""
        try:
            site_url = self.site_url_var.get().strip()
            if not site_url:
                return

            self.log_message(f"사이트 키워드 분석 중: {site_url}")

            # 웹 스크래핑
            response = requests.get(site_url, timeout=10)
            soup = BeautifulSoup(response.text, 'html.parser')

            # 텍스트 추출
            text = soup.get_text()

            # 키워드 빈도 분석
            words = re.findall(r'\b\w+\b', text.lower())
            word_freq = {}

            for keyword in self.user_keywords:
                keyword_lower = keyword.lower()
                count = words.count(keyword_lower)
                if count > 0:
                    word_freq[keyword] = count

            if word_freq:
                self.log_message(f"✅ 사이트 키워드 분석 완료: {len(word_freq)}개 키워드 발견")

                # 키워드 빈도를 시계열 데이터로 변환 (간단한 예시)
                if self.keyword_df is not None:
                    for keyword, freq in word_freq.items():
                        if keyword not in self.keyword_df.columns:
                            # 빈도를 기반으로 가상의 시계열 데이터 생성
                            self.keyword_df[f"{keyword}_site"] = freq
            else:
                self.log_message("⚠️ 사이트에서 키워드를 찾을 수 없습니다.")

        except Exception as e:
            self.log_message(f"❌ 사이트 키워드 분석 오류: {str(e)}")

    def analyze_correlations(self):
        """상관관계 분석"""
        try:
            self.log_message("상관관계 분석 시작...")

            # 주가 데이터 확인
            if self.stock_df is None:
                self.log_message("❌ 먼저 주가 데이터를 로드해주세요.")
                return

            # 키워드 데이터 확인 및 자동 수집
            if self.keyword_df is None:
                self.log_message("⚠️ 키워드 데이터가 없습니다. 키워드 분석을 먼저 실행합니다...")

                # 키워드가 입력되어 있는지 확인
                keywords_text = self.user_keywords_var.get().strip()
                if not keywords_text:
                    self.log_message("❌ 먼저 키워드를 입력하고 '키워드 분석 시작' 버튼을 클릭해주세요.")
                    return

                # 키워드 분석 자동 실행
                self.analyze_keywords()

                # 키워드 분석이 완료될 때까지 잠시 대기
                import time
                time.sleep(2)

                # 여전히 키워드 데이터가 없으면 오류
                if self.keyword_df is None:
                    self.log_message("❌ 키워드 데이터 수집에 실패했습니다. 키워드나 네트워크 연결을 확인해주세요.")
                    return

            # 시차 효과 분석
            lag_days_list = [int(x.strip()) for x in self.lag_days.get().split(',') if x.strip().isdigit()]

            correlation_results = []

            for keyword in self.keyword_df.columns:
                best_correlation = 0
                best_lag = 0
                best_pvalue = 1.0

                for lag in lag_days_list:
                    try:
                        # 키워드 데이터를 lag만큼 시프트
                        keyword_shifted = self.keyword_df[keyword].shift(lag)

                        # 공통 날짜 범위에서 상관관계 계산
                        common_dates = self.stock_df.index.intersection(keyword_shifted.index)

                        if len(common_dates) > 10:  # 최소 10개 데이터 포인트
                            stock_prices = self.stock_df.loc[common_dates, 'close']
                            keyword_values = keyword_shifted.loc[common_dates]

                            # NaN 제거
                            valid_mask = ~(stock_prices.isna() | keyword_values.isna())
                            if valid_mask.sum() > 10:
                                correlation, pvalue = pearsonr(
                                    stock_prices[valid_mask],
                                    keyword_values[valid_mask]
                                )

                                if abs(correlation) > abs(best_correlation):
                                    best_correlation = correlation
                                    best_lag = lag
                                    best_pvalue = pvalue

                    except Exception as e:
                        continue

                # 결과 저장
                if abs(best_correlation) > 0.1:  # 최소 상관관계 임계값
                    correlation_results.append({
                        'keyword': keyword,
                        'correlation': best_correlation,
                        'lag': best_lag,
                        'pvalue': best_pvalue,
                        'weight': 1.0  # 기본 가중치
                    })

                    # 데이터베이스에 저장
                    self.save_correlation_to_db(keyword, best_correlation, 1.0, best_lag)

            # 결과 표시
            self.display_correlation_results(correlation_results)

            self.log_message(f"✅ 상관관계 분석 완료: {len(correlation_results)}개 유의미한 상관관계 발견")

        except Exception as e:
            self.log_message(f"❌ 상관관계 분석 오류: {str(e)}")

    def save_correlation_to_db(self, keyword, correlation, weight, lag_days):
        """상관관계를 데이터베이스에 저장"""
        try:
            cursor = self.conn.cursor()

            # 기존 데이터 확인
            cursor.execute(
                "SELECT id FROM keyword_correlations WHERE keyword = ?",
                (keyword,)
            )
            existing = cursor.fetchone()

            if existing:
                # 업데이트
                cursor.execute("""
                    UPDATE keyword_correlations
                    SET correlation = ?, weight = ?, lag_days = ?, updated_date = CURRENT_TIMESTAMP
                    WHERE keyword = ?
                """, (correlation, weight, lag_days, keyword))
            else:
                # 새로 삽입
                cursor.execute("""
                    INSERT INTO keyword_correlations (keyword, correlation, weight, lag_days)
                    VALUES (?, ?, ?, ?)
                """, (keyword, correlation, weight, lag_days))

            self.conn.commit()

        except Exception as e:
            self.log_message(f"⚠️ DB 저장 오류: {str(e)}")

    def display_correlation_results(self, results):
        """상관관계 결과 표시"""
        try:
            # 기존 결과 삭제
            for item in self.keyword_tree.get_children():
                self.keyword_tree.delete(item)

            # 새 결과 추가
            for result in sorted(results, key=lambda x: abs(x['correlation']), reverse=True):
                self.keyword_tree.insert('', 'end', values=(
                    result['keyword'],
                    f"{result['correlation']:.4f}",
                    f"{result['weight']:.2f}",
                    f"{result['lag']}일",
                    f"{result['pvalue']:.4f}"
                ))

        except Exception as e:
            self.log_message(f"❌ 결과 표시 오류: {str(e)}")

    def modify_weights(self):
        """가중치 수정"""
        try:
            selected_item = self.keyword_tree.selection()
            if not selected_item:
                messagebox.showwarning("경고", "수정할 키워드를 선택해주세요.")
                return

            # 선택된 항목의 값 가져오기
            item_values = self.keyword_tree.item(selected_item[0])['values']
            keyword = item_values[0]
            current_weight = float(item_values[2])

            # 새 가중치 입력 받기
            new_weight = tk.simpledialog.askfloat(
                "가중치 수정",
                f"'{keyword}'의 새 가중치를 입력하세요:",
                initialvalue=current_weight,
                minvalue=0.0,
                maxvalue=10.0
            )

            if new_weight is not None:
                # 트리뷰 업데이트
                new_values = list(item_values)
                new_values[2] = f"{new_weight:.2f}"
                self.keyword_tree.item(selected_item[0], values=new_values)

                # 데이터베이스 업데이트
                cursor = self.conn.cursor()
                cursor.execute(
                    "UPDATE keyword_correlations SET weight = ?, updated_date = CURRENT_TIMESTAMP WHERE keyword = ?",
                    (new_weight, keyword)
                )
                self.conn.commit()

                self.log_message(f"✅ '{keyword}' 가중치를 {new_weight}로 수정했습니다.")

        except Exception as e:
            self.log_message(f"❌ 가중치 수정 오류: {str(e)}")

    # ==================== 모델 학습 기능 ====================

    def train_models(self):
        """모델 학습 시작"""
        try:
            self.log_message("모델 학습 시작...")

            if self.stock_df is None:
                self.log_message("❌ 먼저 주가 데이터를 로드해주세요.")
                return

            # 백그라운드에서 모델 학습 실행
            threading.Thread(target=self.train_models_thread, daemon=True).start()

        except Exception as e:
            self.log_message(f"❌ 모델 학습 오류: {str(e)}")

    def train_models_thread(self):
        """모델 학습 스레드"""
        try:
            # 데이터 준비
            self.prepare_training_data()

            # 선택된 모델들 학습
            self.train_selected_models()

            # AutoML 실행 (선택된 경우)
            if self.use_automl.get():
                self.train_automl_models()

            self.log_message("✅ 모델 학습 완료")

        except Exception as e:
            self.log_message(f"❌ 모델 학습 스레드 오류: {str(e)}")

    def prepare_training_data(self):
        """학습 데이터 준비"""
        try:
            self.log_message("학습 데이터 준비 중...")

            # 기본 주가 데이터
            features = []
            feature_names = []

            # 기술적 지표 추가
            tech_indicators = ['ma5', 'ma20', 'ma60', 'rsi', 'macd', 'volatility']
            for indicator in tech_indicators:
                if indicator in self.stock_df.columns:
                    features.append(self.stock_df[indicator])
                    feature_names.append(indicator)

            # 날씨 데이터 추가
            if self.weather_df is not None:
                weather_cols = ['temperature', 'humidity', 'pressure', 'wind_speed', 'precipitation']
                for col in weather_cols:
                    if col in self.weather_df.columns:
                        # 날짜 맞춤
                        weather_aligned = self.weather_df[col].reindex(self.stock_df.index, method='ffill')
                        features.append(weather_aligned)
                        feature_names.append(f"weather_{col}")

            # 키워드 데이터 추가
            if self.keyword_df is not None:
                for col in self.keyword_df.columns:
                    keyword_aligned = self.keyword_df[col].reindex(self.stock_df.index, method='ffill')
                    features.append(keyword_aligned)
                    feature_names.append(f"keyword_{col}")

            # 데이터프레임 생성
            if features:
                self.combined_df = pd.concat(features, axis=1)
                self.combined_df.columns = feature_names

                # 타겟 변수 (다음 날 종가)
                self.combined_df['target'] = self.stock_df['close'].shift(-1)

                # NaN 제거
                self.combined_df = self.combined_df.dropna()

                self.log_message(f"✅ 학습 데이터 준비 완료: {self.combined_df.shape[0]}행 × {self.combined_df.shape[1]}열")
            else:
                raise Exception("학습에 사용할 특성이 없습니다.")

        except Exception as e:
            self.log_message(f"❌ 학습 데이터 준비 오류: {str(e)}")
            raise

    def train_selected_models(self):
        """선택된 모델들 학습"""
        try:
            if self.combined_df is None:
                raise Exception("학습 데이터가 준비되지 않았습니다.")

            # 특성과 타겟 분리
            X = self.combined_df.drop('target', axis=1)
            y = self.combined_df['target']

            # 데이터 분할
            X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

            # 데이터 스케일링
            scaler = StandardScaler()
            X_train_scaled = scaler.fit_transform(X_train)
            X_test_scaled = scaler.transform(X_test)

            # 모델 정의
            model_configs = {
                'linear': LinearRegression(),
                'ridge': Ridge(alpha=1.0),
                'lasso': Lasso(alpha=0.1),
                'rf': RandomForestRegressor(n_estimators=100, random_state=42),
                'xgb': xgb.XGBRegressor(random_state=42),
                'lgb': lgb.LGBMRegressor(random_state=42),
                'catboost': CatBoostRegressor(random_state=42, verbose=False),
                'nn': MLPRegressor(hidden_layer_sizes=(100, 50), random_state=42, max_iter=500),
                'svr': SVR(kernel='rbf')
            }

            # 기존 결과 삭제
            for item in self.model_tree.get_children():
                self.model_tree.delete(item)

            # 각 모델 학습
            for model_name, model in model_configs.items():
                if self.model_vars[model_name].get():
                    try:
                        self.log_message(f"{model_name} 모델 학습 중...")

                        start_time = datetime.now()

                        # 모델 학습
                        if model_name in ['linear', 'ridge', 'lasso', 'nn', 'svr']:
                            model.fit(X_train_scaled, y_train)
                            y_pred = model.predict(X_test_scaled)
                        else:
                            model.fit(X_train, y_train)
                            y_pred = model.predict(X_test)

                        end_time = datetime.now()
                        training_time = (end_time - start_time).total_seconds()

                        # 성능 평가
                        mae = mean_absolute_error(y_test, y_pred)
                        rmse = np.sqrt(mean_squared_error(y_test, y_pred))
                        r2 = r2_score(y_test, y_pred)

                        # 결과 저장
                        self.models[model_name] = model
                        self.model_results[model_name] = {
                            'mae': mae,
                            'rmse': rmse,
                            'r2': r2,
                            'training_time': training_time
                        }

                        # 결과 표시
                        self.model_tree.insert('', 'end', values=(
                            model_name,
                            f"{mae:.4f}",
                            f"{rmse:.4f}",
                            f"{r2:.4f}",
                            f"{training_time:.2f}s",
                            "완료"
                        ))

                        # 데이터베이스에 저장
                        self.save_model_result_to_db(model_name, mae, r2)

                        self.log_message(f"✅ {model_name} 학습 완료 (R²: {r2:.4f})")

                    except Exception as e:
                        self.log_message(f"❌ {model_name} 학습 실패: {str(e)}")
                        self.model_tree.insert('', 'end', values=(
                            model_name, "오류", "오류", "오류", "0s", "실패"
                        ))

        except Exception as e:
            self.log_message(f"❌ 모델 학습 오류: {str(e)}")

    def train_automl_models(self):
        """AutoML 모델 학습"""
        try:
            if not TPOT_AVAILABLE:
                self.log_message("⚠️ TPOT가 설치되지 않았습니다.")
                return

            self.log_message("AutoML (TPOT) 학습 시작...")

            X = self.combined_df.drop('target', axis=1)
            y = self.combined_df['target']

            X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

            # TPOT 모델
            tpot = TPOTRegressor(
                generations=5,
                population_size=20,
                verbosity=0,
                random_state=42,
                n_jobs=-1
            )

            start_time = datetime.now()
            tpot.fit(X_train, y_train)
            end_time = datetime.now()

            y_pred = tpot.predict(X_test)

            mae = mean_absolute_error(y_test, y_pred)
            rmse = np.sqrt(mean_squared_error(y_test, y_pred))
            r2 = r2_score(y_test, y_pred)
            training_time = (end_time - start_time).total_seconds()

            # 결과 저장
            self.models['automl'] = tpot
            self.model_results['automl'] = {
                'mae': mae,
                'rmse': rmse,
                'r2': r2,
                'training_time': training_time
            }

            # 결과 표시
            self.model_tree.insert('', 'end', values=(
                "AutoML (TPOT)",
                f"{mae:.4f}",
                f"{rmse:.4f}",
                f"{r2:.4f}",
                f"{training_time:.2f}s",
                "완료"
            ))

            self.log_message(f"✅ AutoML 학습 완료 (R²: {r2:.4f})")

        except Exception as e:
            self.log_message(f"❌ AutoML 학습 오류: {str(e)}")

    def save_model_result_to_db(self, model_name, mae, r2_score):
        """모델 결과를 데이터베이스에 저장"""
        try:
            cursor = self.conn.cursor()
            cursor.execute("""
                INSERT INTO prediction_results (model_name, mae, r2_score, prediction_date)
                VALUES (?, ?, ?, ?)
            """, (model_name, mae, r2_score, datetime.now().date()))
            self.conn.commit()

        except Exception as e:
            self.log_message(f"⚠️ 모델 결과 DB 저장 오류: {str(e)}")

    # ==================== 예측 기능 ====================

    def make_prediction(self):
        """예측 실행"""
        try:
            self.log_message("예측 실행 중...")

            if not self.models:
                self.log_message("❌ 먼저 모델을 학습해주세요.")
                return

            # 백그라운드에서 예측 실행
            threading.Thread(target=self.make_prediction_thread, daemon=True).start()

        except Exception as e:
            self.log_message(f"❌ 예측 오류: {str(e)}")

    def make_prediction_thread(self):
        """예측 스레드"""
        try:
            pred_date = self.pred_date_var.get()
            self.log_message(f"{pred_date} 주가 예측 중...")

            # 예측 날짜에 맞는 특성 데이터 생성
            prediction_features = self.generate_prediction_features(pred_date)

            if prediction_features is None:
                self.log_message("❌ 예측 특성 생성 실패")
                return

            predictions = {}

            for model_name, model in self.models.items():
                try:
                    if model_name in ['linear', 'ridge', 'lasso', 'nn', 'svr']:
                        # 스케일링 필요한 모델
                        scaler = StandardScaler()
                        scaler.fit(self.combined_df.drop('target', axis=1))
                        features_scaled = scaler.transform(prediction_features)
                        pred = model.predict(features_scaled)[0]
                    else:
                        pred = model.predict(prediction_features)[0]

                    predictions[model_name] = pred

                except Exception as e:
                    self.log_message(f"⚠️ {model_name} 예측 실패: {str(e)}")

            # 예측 결과 표시
            self.display_predictions(pred_date, predictions)

        except Exception as e:
            self.log_message(f"❌ 예측 스레드 오류: {str(e)}")

    def generate_prediction_features(self, pred_date):
        """예측 날짜에 맞는 특성 데이터 생성"""
        try:
            pred_datetime = pd.to_datetime(pred_date)
            self.log_message(f"📊 {pred_date} 특성 데이터 생성 중...")

            # 기본 특성 딕셔너리
            features = {}

            # 1. 시계열 특성 생성
            features.update(self.generate_temporal_features(pred_datetime))

            # 2. 기술적 지표 예측 (최신 데이터 기반)
            features.update(self.generate_technical_features())

            # 3. 날씨 특성 예측/생성
            features.update(self.generate_weather_features(pred_datetime))

            # 4. 키워드 특성 예측/생성
            features.update(self.generate_keyword_features(pred_datetime))

            # 5. 경제 지표 특성 (트렌드 기반)
            features.update(self.generate_economic_features(pred_datetime))

            # DataFrame으로 변환
            feature_df = pd.DataFrame([features])

            # 학습 데이터와 동일한 컬럼 순서로 정렬
            training_columns = self.combined_df.drop('target', axis=1).columns
            missing_cols = set(training_columns) - set(feature_df.columns)

            # 누락된 컬럼은 0으로 채움
            for col in missing_cols:
                feature_df[col] = 0

            # 컬럼 순서 맞춤
            feature_df = feature_df[training_columns]

            self.log_message(f"✅ 특성 데이터 생성 완료: {len(features)}개 특성")
            return feature_df

        except Exception as e:
            self.log_message(f"❌ 특성 데이터 생성 오류: {str(e)}")
            return None

    def generate_temporal_features(self, pred_datetime):
        """시계열 특성 생성"""
        features = {}

        # 날짜 특성
        features['year'] = pred_datetime.year
        features['month'] = pred_datetime.month
        features['day'] = pred_datetime.day
        features['dayofweek'] = pred_datetime.dayofweek
        features['dayofyear'] = pred_datetime.dayofyear
        features['quarter'] = pred_datetime.quarter

        # 주기적 특성 (사인/코사인 변환)
        features['month_sin'] = np.sin(2 * np.pi * pred_datetime.month / 12)
        features['month_cos'] = np.cos(2 * np.pi * pred_datetime.month / 12)
        features['dayofweek_sin'] = np.sin(2 * np.pi * pred_datetime.dayofweek / 7)
        features['dayofweek_cos'] = np.cos(2 * np.pi * pred_datetime.dayofweek / 7)

        # 계절 특성
        features['is_weekend'] = 1 if pred_datetime.dayofweek >= 5 else 0
        features['is_month_end'] = 1 if pred_datetime.day >= 28 else 0
        features['is_quarter_end'] = 1 if pred_datetime.month in [3, 6, 9, 12] else 0

        return features

    def generate_technical_features(self):
        """기술적 지표 특성 생성 (최신 데이터 기반 트렌드 예측)"""
        features = {}

        if self.stock_df is None or len(self.stock_df) < 20:
            return features

        try:
            # 최근 데이터 기반 트렌드 계산
            recent_data = self.stock_df.tail(20)

            # 이동평균 트렌드
            if 'ma5' in recent_data.columns:
                ma5_trend = (recent_data['ma5'].iloc[-1] - recent_data['ma5'].iloc[-5]) / recent_data['ma5'].iloc[-5]
                features['ma5'] = recent_data['ma5'].iloc[-1] * (1 + ma5_trend)

            if 'ma20' in recent_data.columns:
                ma20_trend = (recent_data['ma20'].iloc[-1] - recent_data['ma20'].iloc[-10]) / recent_data['ma20'].iloc[-10]
                features['ma20'] = recent_data['ma20'].iloc[-1] * (1 + ma20_trend)

            # RSI (최근 값 사용)
            if 'rsi' in recent_data.columns:
                features['rsi'] = recent_data['rsi'].iloc[-1]

            # MACD 트렌드
            if 'macd' in recent_data.columns:
                features['macd'] = recent_data['macd'].iloc[-1]
                features['macd_signal'] = recent_data['macd_signal'].iloc[-1]

            # 변동성 (최근 평균)
            if 'volatility' in recent_data.columns:
                features['volatility'] = recent_data['volatility'].tail(5).mean()

        except Exception as e:
            self.log_message(f"⚠️ 기술적 지표 특성 생성 오류: {str(e)}")

        return features

    def generate_weather_features(self, pred_datetime):
        """날씨 특성 생성"""
        features = {}

        try:
            if self.weather_df is not None and len(self.weather_df) > 0:
                # 같은 날짜의 과거 데이터가 있는지 확인
                same_date_data = self.weather_df[
                    (self.weather_df.index.month == pred_datetime.month) &
                    (self.weather_df.index.day == pred_datetime.day)
                ]

                if len(same_date_data) > 0:
                    # 과거 동일 날짜 평균 사용
                    for col in ['temperature', 'humidity', 'pressure', 'wind_speed', 'precipitation']:
                        if col in same_date_data.columns:
                            features[f'weather_{col}'] = same_date_data[col].mean()
                else:
                    # 계절별 평균 사용
                    season_data = self.weather_df[self.weather_df.index.month == pred_datetime.month]
                    if len(season_data) > 0:
                        for col in ['temperature', 'humidity', 'pressure', 'wind_speed', 'precipitation']:
                            if col in season_data.columns:
                                features[f'weather_{col}'] = season_data[col].mean()

            # 날씨 데이터가 없으면 계절별 기본값 사용
            if not features:
                season_defaults = {
                    'spring': {'temperature': 15, 'humidity': 60, 'pressure': 1013, 'wind_speed': 3, 'precipitation': 2},
                    'summer': {'temperature': 25, 'humidity': 70, 'pressure': 1010, 'wind_speed': 2, 'precipitation': 5},
                    'autumn': {'temperature': 10, 'humidity': 65, 'pressure': 1015, 'wind_speed': 4, 'precipitation': 3},
                    'winter': {'temperature': 0, 'humidity': 55, 'pressure': 1020, 'wind_speed': 5, 'precipitation': 1}
                }

                if pred_datetime.month in [3, 4, 5]:
                    season = 'spring'
                elif pred_datetime.month in [6, 7, 8]:
                    season = 'summer'
                elif pred_datetime.month in [9, 10, 11]:
                    season = 'autumn'
                else:
                    season = 'winter'

                for key, value in season_defaults[season].items():
                    features[f'weather_{key}'] = value

        except Exception as e:
            self.log_message(f"⚠️ 날씨 특성 생성 오류: {str(e)}")

        return features

    def generate_keyword_features(self, pred_datetime):
        """키워드 특성 생성"""
        features = {}

        try:
            if self.keyword_df is not None and len(self.keyword_df) > 0:
                # 최근 트렌드 기반 예측
                for col in self.keyword_df.columns:
                    recent_values = self.keyword_df[col].tail(30).dropna()
                    if len(recent_values) > 5:
                        # 선형 트렌드 계산
                        x = np.arange(len(recent_values))
                        y = recent_values.values
                        trend = np.polyfit(x, y, 1)[0]

                        # 미래 값 예측 (단순 선형 외삽)
                        days_ahead = (pred_datetime - self.keyword_df.index[-1]).days
                        predicted_value = recent_values.iloc[-1] + (trend * days_ahead)

                        # 0-100 범위로 제한
                        predicted_value = max(0, min(100, predicted_value))
                        features[f'keyword_{col}'] = predicted_value
                    else:
                        # 데이터가 부족하면 최근 평균 사용
                        features[f'keyword_{col}'] = recent_values.mean() if len(recent_values) > 0 else 50

        except Exception as e:
            self.log_message(f"⚠️ 키워드 특성 생성 오류: {str(e)}")

        return features

    def generate_economic_features(self, pred_datetime):
        """경제 지표 특성 생성"""
        features = {}

        try:
            # 현재 날짜와 예측 날짜 간의 차이
            current_date = datetime.now()
            days_diff = (pred_datetime - current_date).days

            # 경제 사이클 특성 (단순화된 버전)
            features['days_from_now'] = days_diff
            features['is_future'] = 1 if days_diff > 0 else 0
            features['weeks_from_now'] = days_diff / 7
            features['months_from_now'] = days_diff / 30

            # 시장 사이클 특성 (가상)
            market_cycle = np.sin(2 * np.pi * pred_datetime.dayofyear / 365)
            features['market_cycle'] = market_cycle

        except Exception as e:
            self.log_message(f"⚠️ 경제 특성 생성 오류: {str(e)}")

        return features

    def display_predictions(self, pred_date, predictions):
        """예측 결과 표시"""
        try:
            result_text = f"=== {pred_date} 주가 예측 결과 ===\n\n"

            # 예측 결과 정렬 (R² 기준)
            sorted_predictions = []
            for model_name, pred_price in predictions.items():
                r2 = self.model_results.get(model_name, {}).get('r2', 0)
                sorted_predictions.append((model_name, pred_price, r2))

            sorted_predictions.sort(key=lambda x: x[2], reverse=True)

            for model_name, pred_price, r2 in sorted_predictions:
                result_text += f"{model_name:15}: {pred_price:8.2f} (R²: {r2:.4f})\n"

            # 앙상블 예측 (가중 평균)
            if len(predictions) > 1:
                weights = []
                values = []
                for model_name, pred_price in predictions.items():
                    r2 = self.model_results.get(model_name, {}).get('r2', 0)
                    if r2 > 0:  # 양의 R² 값만 사용
                        weights.append(r2)
                        values.append(pred_price)

                if weights:
                    ensemble_pred = np.average(values, weights=weights)
                    result_text += f"\n{'앙상블 예측':15}: {ensemble_pred:8.2f}\n"

            # 현재 가격과 비교
            current_price = self.stock_df['close'].iloc[-1]
            result_text += f"\n{'현재 가격':15}: {current_price:8.2f}\n"

            if 'ensemble_pred' in locals():
                change = ensemble_pred - current_price
                change_pct = (change / current_price) * 100
                result_text += f"{'예상 변화':15}: {change:+8.2f} ({change_pct:+.2f}%)\n"

            # 예측 결과 텍스트에 표시
            self.pred_text.delete(1.0, tk.END)
            self.pred_text.insert(tk.END, result_text)

            self.log_message("✅ 예측 완료")

        except Exception as e:
            self.log_message(f"❌ 예측 결과 표시 오류: {str(e)}")

    def real_time_prediction(self):
        """실시간 예측"""
        try:
            self.log_message("실시간 예측 기능은 개발 중입니다.")
            # 실제로는 실시간 데이터 수집 및 예측 로직 구현

        except Exception as e:
            self.log_message(f"❌ 실시간 예측 오류: {str(e)}")

    # ==================== 차트 기능 ====================

    def plot_stock_chart(self):
        """주가 차트 그리기"""
        if self.stock_df is None:
            self.log_message("❌ 먼저 주가 데이터를 로드해주세요.")
            return

        try:
            self.log_message("주가 차트 생성 중...")

            fig, axes = plt.subplots(2, 1, figsize=(15, 10), height_ratios=[3, 1])

            # 주가 차트
            ax1 = axes[0]
            ax1.plot(self.stock_df.index, self.stock_df['close'], label='종가', linewidth=1.5)

            if 'ma20' in self.stock_df.columns:
                ax1.plot(self.stock_df.index, self.stock_df['ma20'], label='MA20', alpha=0.7)

            ax1.set_title('주가 차트')
            ax1.set_ylabel('가격')
            ax1.legend()
            ax1.grid(True, alpha=0.3)

            # 거래량 차트
            if 'volume' in self.stock_df.columns:
                ax2 = axes[1]
                ax2.bar(self.stock_df.index, self.stock_df['volume'], alpha=0.7)
                ax2.set_title('거래량')
                ax2.set_ylabel('거래량')
                ax2.grid(True, alpha=0.3)

            plt.tight_layout()
            plt.show()

            self.log_message("✅ 주가 차트 생성 완료")

        except Exception as e:
            self.log_message(f"❌ 주가 차트 생성 오류: {str(e)}")

    def plot_correlation_heatmap(self):
        """상관관계 히트맵"""
        if self.combined_df is None:
            self.log_message("❌ 먼저 모델 학습을 실행해주세요.")
            return

        try:
            self.log_message("상관관계 히트맵 생성 중...")

            # 상관관계 계산
            corr_matrix = self.combined_df.corr()

            plt.figure(figsize=(12, 10))
            sns.heatmap(corr_matrix, annot=True, cmap='coolwarm', center=0,
                       square=True, fmt='.2f')
            plt.title('특성 간 상관관계')
            plt.tight_layout()
            plt.show()

            self.log_message("✅ 상관관계 히트맵 생성 완료")

        except Exception as e:
            self.log_message(f"❌ 상관관계 히트맵 생성 오류: {str(e)}")

    def plot_model_comparison(self):
        """모델 성능 비교 차트"""
        if not self.model_results:
            self.log_message("❌ 먼저 모델 학습을 실행해주세요.")
            return

        try:
            self.log_message("모델 성능 비교 차트 생성 중...")

            models = list(self.model_results.keys())
            r2_scores = [self.model_results[model]['r2'] for model in models]
            mae_scores = [self.model_results[model]['mae'] for model in models]

            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

            # R² 점수
            ax1.bar(models, r2_scores)
            ax1.set_title('모델별 R² 점수')
            ax1.set_ylabel('R² 점수')
            ax1.tick_params(axis='x', rotation=45)

            # MAE 점수
            ax2.bar(models, mae_scores)
            ax2.set_title('모델별 MAE')
            ax2.set_ylabel('MAE')
            ax2.tick_params(axis='x', rotation=45)

            plt.tight_layout()
            plt.show()

            self.log_message("✅ 모델 성능 비교 차트 생성 완료")

        except Exception as e:
            self.log_message(f"❌ 모델 성능 비교 차트 생성 오류: {str(e)}")

    def plot_prediction_comparison(self):
        """예측 vs 실제 비교 차트"""
        self.log_message("예측 vs 실제 비교 차트는 개발 중입니다.")

    def run(self):
        """애플리케이션 실행"""
        self.log_message("🚀 고도화된 주가 예측 AI 시스템 시작")
        self.root.mainloop()

def main():
    """메인 함수"""
    try:
        app = AdvancedStockPredictionSystem()
        app.run()
    except Exception as e:
        print(f"애플리케이션 실행 중 오류 발생: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
