"""
통합 CSV 처리 및 주식 데이터 분석 도구
- 모든 인코딩 지원
- DatetimeIndex 오류 완전 방지
- 자동 데이터 타입 변환
- 주식 데이터 분석 및 시각화
- 기술적 지표 계산
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import re
from datetime import datetime
import warnings
import os
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading

warnings.filterwarnings('ignore')

# 한글 폰트 설정
plt.rcParams['font.family'] = 'Malgun Gothic'
plt.rcParams['axes.unicode_minus'] = False

class IntegratedCSVAnalyzer:
    """통합 CSV 처리 및 분석 도구"""

    def __init__(self):
        self.df = None
        self.original_columns = None
        self.encoding_used = None
        self.file_path = None

        # GUI 초기화
        self.root = tk.Tk()
        self.root.title("통합 CSV 처리 및 주식 데이터 분석 도구")
        self.root.geometry("1200x800")

        self.setup_gui()

    def setup_gui(self):
        """GUI 설정"""
        # 메인 프레임
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 파일 선택 프레임
        file_frame = ttk.LabelFrame(main_frame, text="파일 선택", padding="10")
        file_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))

        ttk.Button(file_frame, text="CSV 파일 선택", command=self.select_file).grid(row=0, column=0, padx=(0, 10))
        self.file_label = ttk.Label(file_frame, text="파일이 선택되지 않았습니다.")
        self.file_label.grid(row=0, column=1, sticky=tk.W)

        # 분석 옵션 프레임
        options_frame = ttk.LabelFrame(main_frame, text="분석 옵션", padding="10")
        options_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))

        # 기본 분석 버튼들
        ttk.Button(options_frame, text="데이터 정보 보기", command=self.show_data_info).grid(row=0, column=0, sticky=(tk.W, tk.E), pady=2)
        ttk.Button(options_frame, text="데이터 미리보기", command=self.preview_data).grid(row=1, column=0, sticky=(tk.W, tk.E), pady=2)
        ttk.Button(options_frame, text="기본 통계", command=self.show_basic_stats).grid(row=2, column=0, sticky=(tk.W, tk.E), pady=2)

        # 주식 분석 버튼들
        ttk.Separator(options_frame, orient='horizontal').grid(row=3, column=0, sticky=(tk.W, tk.E), pady=10)
        ttk.Label(options_frame, text="주식 데이터 분석", font=('Arial', 10, 'bold')).grid(row=4, column=0, pady=2)

        ttk.Button(options_frame, text="가격 차트", command=self.plot_price_chart).grid(row=5, column=0, sticky=(tk.W, tk.E), pady=2)
        ttk.Button(options_frame, text="거래량 분석", command=self.plot_volume_analysis).grid(row=6, column=0, sticky=(tk.W, tk.E), pady=2)
        ttk.Button(options_frame, text="수익률 분석", command=self.analyze_returns).grid(row=7, column=0, sticky=(tk.W, tk.E), pady=2)
        ttk.Button(options_frame, text="매매 신호", command=self.show_trading_signals).grid(row=8, column=0, sticky=(tk.W, tk.E), pady=2)

        # 날짜 필터링 프레임
        date_frame = ttk.LabelFrame(options_frame, text="날짜 필터링", padding="5")
        date_frame.grid(row=9, column=0, sticky=(tk.W, tk.E), pady=10)

        ttk.Label(date_frame, text="시작일:").grid(row=0, column=0, sticky=tk.W)
        self.start_date_var = tk.StringVar(value="2024-01-01")
        ttk.Entry(date_frame, textvariable=self.start_date_var, width=12).grid(row=0, column=1, padx=5)

        ttk.Label(date_frame, text="종료일:").grid(row=1, column=0, sticky=tk.W)
        self.end_date_var = tk.StringVar()
        ttk.Entry(date_frame, textvariable=self.end_date_var, width=12).grid(row=1, column=1, padx=5)

        ttk.Button(date_frame, text="필터 적용", command=self.apply_date_filter).grid(row=2, column=0, columnspan=2, pady=5)

        # 결과 표시 프레임
        result_frame = ttk.LabelFrame(main_frame, text="결과", padding="10")
        result_frame.grid(row=1, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 텍스트 위젯과 스크롤바
        text_frame = ttk.Frame(result_frame)
        text_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        self.result_text = tk.Text(text_frame, wrap=tk.WORD, width=60, height=30)
        scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=self.result_text.yview)
        self.result_text.configure(yscrollcommand=scrollbar.set)

        self.result_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))

        # 그리드 가중치 설정
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(1, weight=1)
        result_frame.columnconfigure(0, weight=1)
        result_frame.rowconfigure(0, weight=1)
        text_frame.columnconfigure(0, weight=1)
        text_frame.rowconfigure(0, weight=1)

        # 상태바
        self.status_var = tk.StringVar(value="준비")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))

    def log_message(self, message):
        """결과 창에 메시지 출력"""
        self.result_text.insert(tk.END, f"{datetime.now().strftime('%H:%M:%S')} - {message}\n")
        self.result_text.see(tk.END)
        self.root.update_idletasks()

    def select_file(self):
        """파일 선택"""
        file_path = filedialog.askopenfilename(
            title="CSV 파일 선택",
            filetypes=[
                ("CSV 파일", "*.csv"),
                ("모든 파일", "*.*")
            ]
        )

        if file_path:
            self.file_path = file_path
            self.file_label.config(text=os.path.basename(file_path))
            self.log_message(f"파일 선택됨: {os.path.basename(file_path)}")

            # 백그라운드에서 파일 로드
            threading.Thread(target=self.load_file, daemon=True).start()

    def load_file(self):
        """파일 로드 (백그라운드)"""
        try:
            self.status_var.set("파일 로딩 중...")
            self.log_message("CSV 파일 로딩 시작...")

            self.df = self.read_csv_ultimate(self.file_path)

            if self.df is not None:
                self.log_message(f"✅ 파일 로드 완료: {self.df.shape[0]}행 × {self.df.shape[1]}열")
                self.status_var.set("파일 로드 완료")

                # 기술적 지표 계산 (주식 데이터인 경우)
                if self.is_stock_data():
                    self.calculate_technical_indicators()
                    self.log_message("✅ 기술적 지표 계산 완료")
            else:
                self.log_message("❌ 파일 로드 실패")
                self.status_var.set("파일 로드 실패")

        except Exception as e:
            self.log_message(f"❌ 오류 발생: {str(e)}")
            self.status_var.set("오류 발생")

    def read_csv_ultimate(self, file_path):
        """Ultimate CSV 읽기 함수"""
        try:
            self.log_message("📁 CSV 파일 읽기 시작...")

            # 1단계: 인코딩 감지 및 읽기
            df = self._read_with_encoding_detection(file_path)

            # 2단계: 컬럼명 정리 및 매핑
            df = self._clean_and_map_columns(df)

            # 3단계: 데이터 타입 자동 변환
            df = self._auto_convert_datatypes(df)

            # 4단계: 날짜 인덱스 안전 설정
            df = self._safe_set_datetime_index(df)

            # 5단계: 최종 정리
            df = self._final_cleanup(df)

            return df

        except Exception as e:
            self.log_message(f"❌ CSV 읽기 실패: {str(e)}")
            return None

    def _read_with_encoding_detection(self, file_path):
        """다양한 인코딩으로 파일 읽기 시도"""
        encodings = ['utf-8-sig', 'utf-8', 'cp949', 'euc-kr', 'latin-1', 'cp1252']

        for encoding in encodings:
            try:
                df = pd.read_csv(file_path, encoding=encoding, low_memory=False)
                self.encoding_used = encoding
                self.log_message(f"✅ 인코딩 성공: {encoding}")
                return df
            except (UnicodeDecodeError, UnicodeError):
                continue
            except Exception as e:
                if encoding == encodings[-1]:
                    raise e
                continue

        raise Exception("지원되는 인코딩을 찾을 수 없습니다.")

    def _clean_and_map_columns(self, df):
        """컬럼명 정리 및 표준화"""
        self.original_columns = df.columns.tolist()

        # 컬럼명 정리
        cleaned_columns = []
        for col in df.columns:
            clean_col = str(col).strip().replace('﻿', '')
            clean_col = re.sub(r'[^\w\s가-힣]', '', clean_col)
            clean_col = re.sub(r'\s+', '_', clean_col)
            cleaned_columns.append(clean_col)

        df.columns = cleaned_columns

        # 표준 컬럼명 매핑
        column_mapping = {}
        for col in df.columns:
            col_lower = col.lower()

            if any(keyword in col_lower for keyword in ['date', 'time', '날짜', '일자', '시간']):
                column_mapping[col] = 'date'
            elif any(keyword in col_lower for keyword in ['open', '시가', '시작가']):
                column_mapping[col] = 'open'
            elif any(keyword in col_lower for keyword in ['high', '고가', '최고가']):
                column_mapping[col] = 'high'
            elif any(keyword in col_lower for keyword in ['low', '저가', '최저가']):
                column_mapping[col] = 'low'
            elif any(keyword in col_lower for keyword in ['close', '종가', '마감가', '끝가']):
                column_mapping[col] = 'close'
            elif any(keyword in col_lower for keyword in ['volume', '거래량', '볼륨']):
                column_mapping[col] = 'volume'

        if column_mapping:
            df = df.rename(columns=column_mapping)
            self.log_message(f"📝 컬럼 매핑 완료: {len(column_mapping)}개")

        return df

    def _auto_convert_datatypes(self, df):
        """데이터 타입 자동 변환"""
        for col in df.columns:
            if col == 'date':
                df[col] = self._convert_to_datetime(df[col])
            elif col in ['open', 'high', 'low', 'close', 'volume']:
                df[col] = self._convert_to_numeric(df[col])
            else:
                # 자동 감지 시도
                numeric_converted = self._convert_to_numeric(df[col], errors='ignore')
                if not numeric_converted.equals(df[col]):
                    df[col] = numeric_converted

        return df

    def _convert_to_datetime(self, series):
        """안전한 날짜 변환"""
        try:
            if series.dtype == 'object':
                cleaned = series.astype(str).str.strip()
                cleaned = cleaned.replace(['nan', 'NaN', 'null', 'NULL', ''], np.nan)
            else:
                cleaned = series

            converted = pd.to_datetime(cleaned, errors='coerce')
            success_rate = converted.notna().sum() / len(series)

            if success_rate < 0.3:
                return series

            return converted
        except:
            return series

    def _convert_to_numeric(self, series, errors='coerce'):
        """안전한 숫자 변환"""
        try:
            if series.dtype == 'object':
                cleaned = series.astype(str)
                cleaned = cleaned.str.replace(',', '')
                cleaned = cleaned.str.replace('"', '')
                cleaned = cleaned.str.replace("'", '')
                cleaned = cleaned.str.replace('(', '-')
                cleaned = cleaned.str.replace(')', '')
                cleaned = cleaned.str.replace(' ', '')
                cleaned = cleaned.str.replace(r'[^\d.-]', '', regex=True)
                cleaned = cleaned.replace(['', 'nan', 'NaN', 'null', 'NULL'], np.nan)
                converted = pd.to_numeric(cleaned, errors=errors)
            else:
                converted = pd.to_numeric(series, errors=errors)

            return converted
        except:
            return series

    def _safe_set_datetime_index(self, df):
        """안전한 날짜 인덱스 설정"""
        if 'date' in df.columns:
            try:
                if pd.api.types.is_datetime64_any_dtype(df['date']):
                    df = df.set_index('date')
                    df = df.sort_index()
                    df = df[~df.index.duplicated(keep='first')]
                    self.log_message("📅 날짜 인덱스 설정 완료")
            except Exception as e:
                self.log_message(f"⚠️ 날짜 인덱스 설정 실패: {str(e)}")

        return df

    def _final_cleanup(self, df):
        """최종 정리"""
        # 빈 행 제거
        before_rows = len(df)
        df = df.dropna(how='all')
        after_rows = len(df)

        if before_rows != after_rows:
            self.log_message(f"🧹 빈 행 제거: {before_rows - after_rows}행")

        return df

    def is_stock_data(self):
        """주식 데이터인지 확인"""
        if self.df is None:
            return False

        stock_columns = ['open', 'high', 'low', 'close']
        return all(col in self.df.columns for col in stock_columns)

    def calculate_technical_indicators(self):
        """기술적 지표 계산"""
        if not self.is_stock_data():
            return

        try:
            # 이동평균선
            self.df['ma5'] = self.df['close'].rolling(window=5).mean()
            self.df['ma20'] = self.df['close'].rolling(window=20).mean()
            self.df['ma60'] = self.df['close'].rolling(window=60).mean()

            # 볼린저 밴드
            self.df['bb_middle'] = self.df['close'].rolling(window=20).mean()
            bb_std = self.df['close'].rolling(window=20).std()
            self.df['bb_upper'] = self.df['bb_middle'] + (bb_std * 2)
            self.df['bb_lower'] = self.df['bb_middle'] - (bb_std * 2)

            # RSI
            delta = self.df['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            self.df['rsi'] = 100 - (100 / (1 + rs))

            # MACD
            exp1 = self.df['close'].ewm(span=12).mean()
            exp2 = self.df['close'].ewm(span=26).mean()
            self.df['macd'] = exp1 - exp2
            self.df['macd_signal'] = self.df['macd'].ewm(span=9).mean()
            self.df['macd_histogram'] = self.df['macd'] - self.df['macd_signal']

            # 일일 수익률
            self.df['daily_return'] = self.df['close'].pct_change()

            # 변동성
            self.df['volatility'] = self.df['daily_return'].rolling(window=20).std() * np.sqrt(252)

        except Exception as e:
            self.log_message(f"⚠️ 기술적 지표 계산 실패: {str(e)}")

    def safe_date_filter(self, start_date=None, end_date=None):
        """안전한 날짜 필터링"""
        if self.df is None:
            return None

        try:
            if not isinstance(self.df.index, pd.DatetimeIndex):
                self.log_message("⚠️ 날짜 인덱스가 아닙니다.")
                return self.df

            if start_date:
                start_date = pd.to_datetime(start_date)
            if end_date:
                end_date = pd.to_datetime(end_date)

            if not self.df.index.is_monotonic_increasing:
                self.df = self.df.sort_index()

            if start_date and end_date:
                mask = (self.df.index >= start_date) & (self.df.index <= end_date)
                result = self.df[mask]
            elif start_date:
                result = self.df[self.df.index >= start_date]
            elif end_date:
                result = self.df[self.df.index <= end_date]
            else:
                result = self.df

            self.log_message(f"📅 날짜 필터링: {len(self.df)} → {len(result)}행")
            return result

        except Exception as e:
            self.log_message(f"⚠️ 날짜 필터링 실패: {str(e)}")
            return self.df

    # GUI 이벤트 핸들러들
    def show_data_info(self):
        """데이터 정보 표시"""
        if self.df is None:
            self.log_message("❌ 먼저 파일을 선택해주세요.")
            return

        try:
            info_text = f"""
=== 데이터 정보 ===
파일: {os.path.basename(self.file_path) if self.file_path else 'N/A'}
인코딩: {self.encoding_used}
형태: {self.df.shape[0]}행 × {self.df.shape[1]}열
메모리: {self.df.memory_usage(deep=True).sum() / 1024 / 1024:.2f} MB

=== 컬럼 정보 ===
원본 컬럼: {self.original_columns}
현재 컬럼: {list(self.df.columns)}

=== 데이터 타입 ===
"""
            for col, dtype in self.df.dtypes.items():
                info_text += f"{col}: {dtype}\n"

            # 결측값 정보
            missing = self.df.isnull().sum()
            missing_info = missing[missing > 0]
            if len(missing_info) > 0:
                info_text += f"\n=== 결측값 ===\n"
                for col, count in missing_info.items():
                    info_text += f"{col}: {count}개\n"

            # 날짜 범위 정보
            if isinstance(self.df.index, pd.DatetimeIndex):
                info_text += f"\n=== 날짜 범위 ===\n"
                info_text += f"시작: {self.df.index.min()}\n"
                info_text += f"종료: {self.df.index.max()}\n"
                info_text += f"기간: {len(self.df)}일\n"

            self.log_message(info_text)

        except Exception as e:
            self.log_message(f"❌ 데이터 정보 표시 실패: {str(e)}")

    def preview_data(self):
        """데이터 미리보기"""
        if self.df is None:
            self.log_message("❌ 먼저 파일을 선택해주세요.")
            return

        try:
            self.log_message("=== 데이터 미리보기 (첫 10행) ===")
            self.log_message(str(self.df.head(10)))

            self.log_message("\n=== 데이터 미리보기 (마지막 5행) ===")
            self.log_message(str(self.df.tail(5)))

        except Exception as e:
            self.log_message(f"❌ 데이터 미리보기 실패: {str(e)}")

    def show_basic_stats(self):
        """기본 통계 표시"""
        if self.df is None:
            self.log_message("❌ 먼저 파일을 선택해주세요.")
            return

        try:
            self.log_message("=== 기본 통계 ===")
            self.log_message(str(self.df.describe()))

            if self.is_stock_data():
                self.log_message("\n=== 주식 데이터 요약 ===")
                summary = self.get_stock_summary()
                for key, value in summary.items():
                    self.log_message(f"{key}: {value}")

        except Exception as e:
            self.log_message(f"❌ 기본 통계 표시 실패: {str(e)}")

    def get_stock_summary(self):
        """주식 데이터 요약"""
        if not self.is_stock_data():
            return {}

        try:
            summary = {
                '시작가': f"{self.df['close'].iloc[0]:.2f}",
                '종료가': f"{self.df['close'].iloc[-1]:.2f}",
                '최고가': f"{self.df['high'].max():.2f}",
                '최저가': f"{self.df['low'].min():.2f}",
                '총 수익률': f"{((self.df['close'].iloc[-1] / self.df['close'].iloc[0]) - 1) * 100:.2f}%"
            }

            if 'daily_return' in self.df.columns:
                summary['평균 일일 수익률'] = f"{self.df['daily_return'].mean() * 100:.2f}%"
                summary['변동성(연환산)'] = f"{self.df['daily_return'].std() * np.sqrt(252) * 100:.2f}%"

            if 'volume' in self.df.columns and not self.df['volume'].isna().all():
                summary['평균 거래량'] = f"{self.df['volume'].mean():.0f}"

            return summary

        except Exception as e:
            self.log_message(f"⚠️ 주식 요약 계산 실패: {str(e)}")
            return {}

    def apply_date_filter(self):
        """날짜 필터 적용"""
        if self.df is None:
            self.log_message("❌ 먼저 파일을 선택해주세요.")
            return

        start_date = self.start_date_var.get() if self.start_date_var.get() else None
        end_date = self.end_date_var.get() if self.end_date_var.get() else None

        if not start_date and not end_date:
            self.log_message("⚠️ 시작일 또는 종료일을 입력해주세요.")
            return

        filtered_df = self.safe_date_filter(start_date, end_date)

        if filtered_df is not None:
            # 임시로 필터링된 데이터를 저장
            self.filtered_df = filtered_df
            self.log_message("✅ 날짜 필터 적용 완료. 차트 기능에서 필터링된 데이터를 사용합니다.")

    def plot_price_chart(self):
        """가격 차트 그리기"""
        if self.df is None:
            self.log_message("❌ 먼저 파일을 선택해주세요.")
            return

        if not self.is_stock_data():
            self.log_message("❌ 주식 데이터가 아닙니다.")
            return

        try:
            # 필터링된 데이터가 있으면 사용
            plot_df = getattr(self, 'filtered_df', self.df)

            self.log_message("📊 가격 차트를 생성하고 있습니다...")

            fig, axes = plt.subplots(3, 1, figsize=(15, 10), height_ratios=[3, 1, 1])

            # 1. 가격 차트
            ax1 = axes[0]
            ax1.plot(plot_df.index, plot_df['close'], label='종가', linewidth=1.5, color='black')

            if 'ma5' in plot_df.columns:
                ax1.plot(plot_df.index, plot_df['ma5'], label='MA5', alpha=0.7, color='red')
                ax1.plot(plot_df.index, plot_df['ma20'], label='MA20', alpha=0.7, color='blue')
                ax1.plot(plot_df.index, plot_df['ma60'], label='MA60', alpha=0.7, color='green')

            if 'bb_upper' in plot_df.columns:
                ax1.fill_between(plot_df.index, plot_df['bb_upper'], plot_df['bb_lower'],
                               alpha=0.2, color='gray', label='볼린저 밴드')

            ax1.set_title('주가 차트', fontsize=16, fontweight='bold')
            ax1.set_ylabel('가격')
            ax1.legend()
            ax1.grid(True, alpha=0.3)

            # 2. RSI
            if 'rsi' in plot_df.columns:
                ax2 = axes[1]
                ax2.plot(plot_df.index, plot_df['rsi'], color='purple', linewidth=1)
                ax2.axhline(y=70, color='r', linestyle='--', alpha=0.7)
                ax2.axhline(y=30, color='b', linestyle='--', alpha=0.7)
                ax2.set_title('RSI')
                ax2.set_ylabel('RSI')
                ax2.set_ylim(0, 100)
                ax2.grid(True, alpha=0.3)

            # 3. MACD
            if 'macd' in plot_df.columns:
                ax3 = axes[2]
                ax3.plot(plot_df.index, plot_df['macd'], label='MACD', color='blue')
                ax3.plot(plot_df.index, plot_df['macd_signal'], label='Signal', color='red')
                ax3.bar(plot_df.index, plot_df['macd_histogram'], alpha=0.6, color='gray', width=1)
                ax3.set_title('MACD')
                ax3.set_ylabel('MACD')
                ax3.set_xlabel('날짜')
                ax3.legend()
                ax3.grid(True, alpha=0.3)

            plt.tight_layout()
            plt.show()

            self.log_message("✅ 가격 차트 생성 완료")

        except Exception as e:
            self.log_message(f"❌ 가격 차트 생성 실패: {str(e)}")

    def plot_volume_analysis(self):
        """거래량 분석 차트"""
        if self.df is None or not self.is_stock_data():
            self.log_message("❌ 주식 데이터를 먼저 로드해주세요.")
            return

        if 'volume' not in self.df.columns or self.df['volume'].isna().all():
            self.log_message("❌ 거래량 데이터가 없습니다.")
            return

        try:
            plot_df = getattr(self, 'filtered_df', self.df)

            self.log_message("📊 거래량 분석 차트를 생성하고 있습니다...")

            fig, axes = plt.subplots(2, 1, figsize=(15, 8), height_ratios=[2, 1])

            # 1. 가격
            ax1 = axes[0]
            ax1.plot(plot_df.index, plot_df['close'], color='black', linewidth=1.5)
            ax1.set_title('주가와 거래량')
            ax1.set_ylabel('가격')
            ax1.grid(True, alpha=0.3)

            # 2. 거래량
            ax2 = axes[1]
            colors = ['red' if close >= open_price else 'blue'
                     for close, open_price in zip(plot_df['close'], plot_df['open'])]
            ax2.bar(plot_df.index, plot_df['volume'], color=colors, alpha=0.7, width=1)
            ax2.set_ylabel('거래량')
            ax2.set_xlabel('날짜')
            ax2.grid(True, alpha=0.3)

            plt.tight_layout()
            plt.show()

            self.log_message("✅ 거래량 분석 차트 생성 완료")

        except Exception as e:
            self.log_message(f"❌ 거래량 분석 차트 생성 실패: {str(e)}")

    def analyze_returns(self):
        """수익률 분석"""
        if self.df is None or not self.is_stock_data():
            self.log_message("❌ 주식 데이터를 먼저 로드해주세요.")
            return

        if 'daily_return' not in self.df.columns:
            self.log_message("❌ 수익률 데이터가 없습니다.")
            return

        try:
            self.log_message("📊 수익률 분석을 생성하고 있습니다...")

            fig, axes = plt.subplots(2, 2, figsize=(15, 10))

            # 1. 일일 수익률 히스토그램
            axes[0, 0].hist(self.df['daily_return'].dropna(), bins=50, alpha=0.7, color='blue')
            axes[0, 0].set_title('일일 수익률 분포')
            axes[0, 0].set_xlabel('수익률')
            axes[0, 0].set_ylabel('빈도')
            axes[0, 0].grid(True, alpha=0.3)

            # 2. 누적 수익률
            cumulative_returns = (1 + self.df['daily_return']).cumprod()
            axes[0, 1].plot(self.df.index, cumulative_returns, color='green', linewidth=1.5)
            axes[0, 1].set_title('누적 수익률')
            axes[0, 1].set_ylabel('누적 수익률')
            axes[0, 1].grid(True, alpha=0.3)

            # 3. 변동성 추이
            if 'volatility' in self.df.columns:
                axes[1, 0].plot(self.df.index, self.df['volatility'], color='red', linewidth=1)
                axes[1, 0].set_title('변동성 추이')
                axes[1, 0].set_ylabel('변동성')
                axes[1, 0].grid(True, alpha=0.3)

            # 4. 월별 수익률 박스플롯
            monthly_returns = self.df['daily_return'].resample('M').apply(lambda x: (1 + x).prod() - 1)
            axes[1, 1].boxplot([monthly_returns.dropna().values])
            axes[1, 1].set_title('월별 수익률 분포')
            axes[1, 1].set_ylabel('월별 수익률')
            axes[1, 1].grid(True, alpha=0.3)

            plt.tight_layout()
            plt.show()

            self.log_message("✅ 수익률 분석 완료")

        except Exception as e:
            self.log_message(f"❌ 수익률 분석 실패: {str(e)}")

    def show_trading_signals(self):
        """매매 신호 분석"""
        if self.df is None or not self.is_stock_data():
            self.log_message("❌ 주식 데이터를 먼저 로드해주세요.")
            return

        try:
            signals = self.get_recent_signals(30)

            if signals:
                self.log_message("=== 최근 30일 매매 신호 ===")
                for signal in signals[-10:]:  # 최근 10개만 표시
                    self.log_message(f"{signal['날짜']}: {signal['신호']} (종가: {signal['종가']:.2f})")
            else:
                self.log_message("최근 매매 신호가 없습니다.")

        except Exception as e:
            self.log_message(f"❌ 매매 신호 분석 실패: {str(e)}")

    def get_recent_signals(self, days=30):
        """최근 매매 신호 분석"""
        if not self.is_stock_data():
            return []

        try:
            recent_df = self.df.tail(days)
            signals = []

            for i, (date, row) in enumerate(recent_df.iterrows()):
                signal_list = []

                # RSI 신호
                if 'rsi' in row and not pd.isna(row['rsi']):
                    if row['rsi'] > 70:
                        signal_list.append("RSI 과매수")
                    elif row['rsi'] < 30:
                        signal_list.append("RSI 과매도")

                # MACD 신호
                if i > 0 and 'macd' in row and 'macd_signal' in row:
                    prev_row = recent_df.iloc[i-1]
                    if (not pd.isna(row['macd']) and not pd.isna(row['macd_signal']) and
                        not pd.isna(prev_row['macd']) and not pd.isna(prev_row['macd_signal'])):

                        if (row['macd'] > row['macd_signal'] and
                            prev_row['macd'] <= prev_row['macd_signal']):
                            signal_list.append("MACD 골든크로스")
                        elif (row['macd'] < row['macd_signal'] and
                              prev_row['macd'] >= prev_row['macd_signal']):
                            signal_list.append("MACD 데드크로스")

                # 볼린저 밴드 신호
                if ('bb_upper' in row and 'bb_lower' in row and
                    not pd.isna(row['bb_upper']) and not pd.isna(row['bb_lower'])):
                    if row['close'] > row['bb_upper']:
                        signal_list.append("볼린저 밴드 상단 돌파")
                    elif row['close'] < row['bb_lower']:
                        signal_list.append("볼린저 밴드 하단 이탈")

                if signal_list:
                    signals.append({
                        '날짜': date.strftime('%Y-%m-%d'),
                        '종가': row['close'],
                        '신호': ', '.join(signal_list)
                    })

            return signals

        except Exception as e:
            self.log_message(f"⚠️ 신호 분석 중 오류: {str(e)}")
            return []

    def run(self):
        """애플리케이션 실행"""
        self.log_message("🚀 통합 CSV 처리 및 주식 데이터 분석 도구 시작")
        self.log_message("📁 파일 선택 버튼을 클릭하여 CSV 파일을 선택하세요.")
        self.root.mainloop()

def main():
    """메인 함수"""
    try:
        app = IntegratedCSVAnalyzer()
        app.run()
    except Exception as e:
        print(f"애플리케이션 실행 중 오류 발생: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
