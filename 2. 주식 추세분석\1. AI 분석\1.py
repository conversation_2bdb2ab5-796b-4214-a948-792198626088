"""
통합 CSV 처리 및 주식 데이터 분석 도구
- 모든 인코딩 지원
- DatetimeIndex 오류 완전 방지
- 자동 데이터 타입 변환
- 주식 데이터 분석 및 시각화
- 기술적 지표 계산
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import re
from datetime import datetime
import warnings
import os
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading

warnings.filterwarnings('ignore')

# 한글 폰트 설정
plt.rcParams['font.family'] = 'Malgun Gothic'
plt.rcParams['axes.unicode_minus'] = False

class IntegratedCSVAnalyzer:
    """통합 CSV 처리 및 분석 도구"""

    def __init__(self):
        self.df = None
        self.original_columns = None
        self.encoding_used = None
        self.file_path = None

        # GUI 초기화
        self.root = tk.Tk()
        self.root.title("통합 CSV 처리 및 주식 데이터 분석 도구")
        self.root.geometry("1200x800")

        self.setup_gui()

    def setup_gui(self):
        """GUI 설정"""
        # 메인 프레임
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 파일 선택 프레임
        file_frame = ttk.LabelFrame(main_frame, text="파일 선택", padding="10")
        file_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))

        ttk.Button(file_frame, text="CSV 파일 선택", command=self.select_file).grid(row=0, column=0, padx=(0, 10))
        self.file_label = ttk.Label(file_frame, text="파일이 선택되지 않았습니다.")
        self.file_label.grid(row=0, column=1, sticky=tk.W)

        # 분석 옵션 프레임
        options_frame = ttk.LabelFrame(main_frame, text="분석 옵션", padding="10")
        options_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))

        # 기본 분석 버튼들
        ttk.Button(options_frame, text="데이터 정보 보기", command=self.show_data_info).grid(row=0, column=0, sticky=(tk.W, tk.E), pady=2)
        ttk.Button(options_frame, text="데이터 미리보기", command=self.preview_data).grid(row=1, column=0, sticky=(tk.W, tk.E), pady=2)
        ttk.Button(options_frame, text="기본 통계", command=self.show_basic_stats).grid(row=2, column=0, sticky=(tk.W, tk.E), pady=2)

        # 주식 분석 버튼들
        ttk.Separator(options_frame, orient='horizontal').grid(row=3, column=0, sticky=(tk.W, tk.E), pady=10)
        ttk.Label(options_frame, text="주식 데이터 분석", font=('Arial', 10, 'bold')).grid(row=4, column=0, pady=2)

        ttk.Button(options_frame, text="가격 차트", command=self.plot_price_chart).grid(row=5, column=0, sticky=(tk.W, tk.E), pady=2)
        ttk.Button(options_frame, text="거래량 분석", command=self.plot_volume_analysis).grid(row=6, column=0, sticky=(tk.W, tk.E), pady=2)
        ttk.Button(options_frame, text="수익률 분석", command=self.analyze_returns).grid(row=7, column=0, sticky=(tk.W, tk.E), pady=2)
        ttk.Button(options_frame, text="매매 신호", command=self.show_trading_signals).grid(row=8, column=0, sticky=(tk.W, tk.E), pady=2)

        # 날짜 필터링 프레임
        date_frame = ttk.LabelFrame(options_frame, text="날짜 필터링", padding="5")
        date_frame.grid(row=9, column=0, sticky=(tk.W, tk.E), pady=10)

        ttk.Label(date_frame, text="시작일:").grid(row=0, column=0, sticky=tk.W)
        self.start_date_var = tk.StringVar(value="2024-01-01")
        ttk.Entry(date_frame, textvariable=self.start_date_var, width=12).grid(row=0, column=1, padx=5)

        ttk.Label(date_frame, text="종료일:").grid(row=1, column=0, sticky=tk.W)
        self.end_date_var = tk.StringVar()
        ttk.Entry(date_frame, textvariable=self.end_date_var, width=12).grid(row=1, column=1, padx=5)

        ttk.Button(date_frame, text="필터 적용", command=self.apply_date_filter).grid(row=2, column=0, columnspan=2, pady=5)

        # 결과 표시 프레임
        result_frame = ttk.LabelFrame(main_frame, text="결과", padding="10")
        result_frame.grid(row=1, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 텍스트 위젯과 스크롤바
        text_frame = ttk.Frame(result_frame)
        text_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        self.result_text = tk.Text(text_frame, wrap=tk.WORD, width=60, height=30)
        scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=self.result_text.yview)
        self.result_text.configure(yscrollcommand=scrollbar.set)

        self.result_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))

        # 그리드 가중치 설정
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(1, weight=1)
        result_frame.columnconfigure(0, weight=1)
        result_frame.rowconfigure(0, weight=1)
        text_frame.columnconfigure(0, weight=1)
        text_frame.rowconfigure(0, weight=1)

        # 상태바
        self.status_var = tk.StringVar(value="준비")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))

    def log_message(self, message):
        """결과 창에 메시지 출력"""
        self.result_text.insert(tk.END, f"{datetime.now().strftime('%H:%M:%S')} - {message}\n")
        self.result_text.see(tk.END)
        self.root.update_idletasks()

    def select_file(self):
        """파일 선택"""
        file_path = filedialog.askopenfilename(
            title="CSV 파일 선택",
            filetypes=[
                ("CSV 파일", "*.csv"),
                ("모든 파일", "*.*")
            ]
        )

        if file_path:
            self.file_path = file_path
            self.file_label.config(text=os.path.basename(file_path))
            self.log_message(f"파일 선택됨: {os.path.basename(file_path)}")

            # 백그라운드에서 파일 로드
            threading.Thread(target=self.load_file, daemon=True).start()

    def load_file(self):
        """파일 로드 (백그라운드)"""
        try:
            self.status_var.set("파일 로딩 중...")
            self.log_message("CSV 파일 로딩 시작...")

            self.df = self.read_csv_ultimate(self.file_path)

            if self.df is not None:
                self.log_message(f"✅ 파일 로드 완료: {self.df.shape[0]}행 × {self.df.shape[1]}열")
                self.status_var.set("파일 로드 완료")

                # 기술적 지표 계산 (주식 데이터인 경우)
                if self.is_stock_data():
                    self.calculate_technical_indicators()
                    self.log_message("✅ 기술적 지표 계산 완료")
            else:
                self.log_message("❌ 파일 로드 실패")
                self.status_var.set("파일 로드 실패")

        except Exception as e:
            self.log_message(f"❌ 오류 발생: {str(e)}")
            self.status_var.set("오류 발생")

    def read_csv_ultimate(self, file_path):
        """Ultimate CSV 읽기 함수"""
        try:
            self.log_message("📁 CSV 파일 읽기 시작...")

            # 1단계: 인코딩 감지 및 읽기
            df = self._read_with_encoding_detection(file_path)

            # 2단계: 컬럼명 정리 및 매핑
            df = self._clean_and_map_columns(df)

            # 3단계: 데이터 타입 자동 변환
            df = self._auto_convert_datatypes(df)

            # 4단계: 날짜 인덱스 안전 설정
            df = self._safe_set_datetime_index(df)

            # 5단계: 최종 정리
            df = self._final_cleanup(df)

            return df

        except Exception as e:
            self.log_message(f"❌ CSV 읽기 실패: {str(e)}")
            return None

    def _read_with_encoding_detection(self, file_path):
        """다양한 인코딩으로 파일 읽기 시도"""
        encodings = ['utf-8-sig', 'utf-8', 'cp949', 'euc-kr', 'latin-1', 'cp1252']

        for encoding in encodings:
            try:
                df = pd.read_csv(file_path, encoding=encoding, low_memory=False)
                self.encoding_used = encoding
                self.log_message(f"✅ 인코딩 성공: {encoding}")
                return df
            except (UnicodeDecodeError, UnicodeError):
                continue
            except Exception as e:
                if encoding == encodings[-1]:
                    raise e
                continue

        raise Exception("지원되는 인코딩을 찾을 수 없습니다.")

    def _clean_and_map_columns(self, df):
        """컬럼명 정리 및 표준화"""
        self.original_columns = df.columns.tolist()

        # 컬럼명 정리
        cleaned_columns = []
        for col in df.columns:
            clean_col = str(col).strip().replace('﻿', '')
            clean_col = re.sub(r'[^\w\s가-힣]', '', clean_col)
            clean_col = re.sub(r'\s+', '_', clean_col)
            cleaned_columns.append(clean_col)

        df.columns = cleaned_columns

        # 표준 컬럼명 매핑
        column_mapping = {}
        for col in df.columns:
            col_lower = col.lower()

            if any(keyword in col_lower for keyword in ['date', 'time', '날짜', '일자', '시간']):
                column_mapping[col] = 'date'
            elif any(keyword in col_lower for keyword in ['open', '시가', '시작가']):
                column_mapping[col] = 'open'
            elif any(keyword in col_lower for keyword in ['high', '고가', '최고가']):
                column_mapping[col] = 'high'
            elif any(keyword in col_lower for keyword in ['low', '저가', '최저가']):
                column_mapping[col] = 'low'
            elif any(keyword in col_lower for keyword in ['close', '종가', '마감가', '끝가']):
                column_mapping[col] = 'close'
            elif any(keyword in col_lower for keyword in ['volume', '거래량', '볼륨']):
                column_mapping[col] = 'volume'

        if column_mapping:
            df = df.rename(columns=column_mapping)
            self.log_message(f"📝 컬럼 매핑 완료: {len(column_mapping)}개")

        return df

    def _auto_convert_datatypes(self, df):
        """데이터 타입 자동 변환"""
        for col in df.columns:
            if col == 'date':
                df[col] = self._convert_to_datetime(df[col])
            elif col in ['open', 'high', 'low', 'close', 'volume']:
                df[col] = self._convert_to_numeric(df[col])
            else:
                # 자동 감지 시도
                numeric_converted = self._convert_to_numeric(df[col], errors='ignore')
                if not numeric_converted.equals(df[col]):
                    df[col] = numeric_converted

        return df

    def _convert_to_datetime(self, series):
        """안전한 날짜 변환"""
        try:
            if series.dtype == 'object':
                cleaned = series.astype(str).str.strip()
                cleaned = cleaned.replace(['nan', 'NaN', 'null', 'NULL', ''], np.nan)
            else:
                cleaned = series

            converted = pd.to_datetime(cleaned, errors='coerce')
            success_rate = converted.notna().sum() / len(series)

            if success_rate < 0.3:
                return series

            return converted
        except:
            return series

    def _convert_to_numeric(self, series, errors='coerce'):
        """안전한 숫자 변환"""
        try:
            if series.dtype == 'object':
                cleaned = series.astype(str)
                cleaned = cleaned.str.replace(',', '')
                cleaned = cleaned.str.replace('"', '')
                cleaned = cleaned.str.replace("'", '')
                cleaned = cleaned.str.replace('(', '-')
                cleaned = cleaned.str.replace(')', '')
                cleaned = cleaned.str.replace(' ', '')
                cleaned = cleaned.str.replace(r'[^\d.-]', '', regex=True)
                cleaned = cleaned.replace(['', 'nan', 'NaN', 'null', 'NULL'], np.nan)
                converted = pd.to_numeric(cleaned, errors=errors)
            else:
                converted = pd.to_numeric(series, errors=errors)

            return converted
        except:
            return series

    def _safe_set_datetime_index(self, df):
        """안전한 날짜 인덱스 설정"""
        if 'date' in df.columns:
            try:
                if pd.api.types.is_datetime64_any_dtype(df['date']):
                    df = df.set_index('date')
                    df = df.sort_index()
                    df = df[~df.index.duplicated(keep='first')]
                    self.log_message("📅 날짜 인덱스 설정 완료")
            except Exception as e:
                self.log_message(f"⚠️ 날짜 인덱스 설정 실패: {str(e)}")

        return df

    def _final_cleanup(self, df):
        """최종 정리"""
        # 빈 행 제거
        before_rows = len(df)
        df = df.dropna(how='all')
        after_rows = len(df)

        if before_rows != after_rows:
            self.log_message(f"🧹 빈 행 제거: {before_rows - after_rows}행")

        return df

    def is_stock_data(self):
        """주식 데이터인지 확인"""
        if self.df is None:
            return False

        stock_columns = ['open', 'high', 'low', 'close']
        return all(col in self.df.columns for col in stock_columns)

    def calculate_technical_indicators(self):
        """기술적 지표 계산"""
        if not self.is_stock_data():
            return

        try:
            # 이동평균선
            self.df['ma5'] = self.df['close'].rolling(window=5).mean()
            self.df['ma20'] = self.df['close'].rolling(window=20).mean()
            self.df['ma60'] = self.df['close'].rolling(window=60).mean()

            # 볼린저 밴드
            self.df['bb_middle'] = self.df['close'].rolling(window=20).mean()
            bb_std = self.df['close'].rolling(window=20).std()
            self.df['bb_upper'] = self.df['bb_middle'] + (bb_std * 2)
            self.df['bb_lower'] = self.df['bb_middle'] - (bb_std * 2)

            # RSI
            delta = self.df['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            self.df['rsi'] = 100 - (100 / (1 + rs))

            # MACD
            exp1 = self.df['close'].ewm(span=12).mean()
            exp2 = self.df['close'].ewm(span=26).mean()
            self.df['macd'] = exp1 - exp2
            self.df['macd_signal'] = self.df['macd'].ewm(span=9).mean()
            self.df['macd_histogram'] = self.df['macd'] - self.df['macd_signal']

            # 일일 수익률
            self.df['daily_return'] = self.df['close'].pct_change()

            # 변동성
            self.df['volatility'] = self.df['daily_return'].rolling(window=20).std() * np.sqrt(252)

        except Exception as e:
            self.log_message(f"⚠️ 기술적 지표 계산 실패: {str(e)}")

    def safe_date_filter(self, start_date=None, end_date=None):
        """안전한 날짜 필터링"""
        if self.df is None:
            return None

        try:
            if not isinstance(self.df.index, pd.DatetimeIndex):
                self.log_message("⚠️ 날짜 인덱스가 아닙니다.")
                return self.df

            if start_date:
                start_date = pd.to_datetime(start_date)
            if end_date:
                end_date = pd.to_datetime(end_date)

            if not self.df.index.is_monotonic_increasing:
                self.df = self.df.sort_index()

            if start_date and end_date:
                mask = (self.df.index >= start_date) & (self.df.index <= end_date)
                result = self.df[mask]
            elif start_date:
                result = self.df[self.df.index >= start_date]
            elif end_date:
                result = self.df[self.df.index <= end_date]
            else:
                result = self.df

            self.log_message(f"📅 날짜 필터링: {len(self.df)} → {len(result)}행")
            return result

        except Exception as e:
            self.log_message(f"⚠️ 날짜 필터링 실패: {str(e)}")
            return self.df
    try:
        url = "https://www.reuters.com/business/energy/"
        response = requests.get(url)
        soup = BeautifulSoup(response.text, 'html.parser')
        titles = [tag.text for tag in soup.find_all("h3")]
        keywords = []
        for title in titles:
            words = title.split()
            for word in words:
                if word.lower() not in keywords and len(word) > 3:
                    keywords.append(word.lower())
        return keywords[:10]  # 상위 10개만
    except:
        return []

# 기본 키워드
def fetch_trending_keywords():
    return ["natural gas", "LNG", "pipeline", "russia energy", "energy crisis"]

# 트렌드 수집
def get_trend(pytrends, keyword, start, end):
    pytrends.build_payload([keyword], timeframe=f"{start} {end}")
    df = pytrends.interest_over_time()
    return df[[keyword]] if 'isPartial' in df.columns else df

# 주요 처리 함수
def run_pipeline(price_file, start_date, end_date, custom_keywords=None, selected_models=None):
    print("\n[1] 데이터 로딩 중...")
    df = pd.read_csv(price_file)

    # 열 이름을 소문자로 표준화
    df.columns = [col.strip().lower() for col in df.columns]

    if 'date' not in df.columns or 'close' not in df.columns:
        raise ValueError("CSV 파일에 'date' 및 'close' 열이 포함되어 있어야 합니다.")

    # 쉼표 제거 후 숫자형 변환 시도
    for col in df.columns:
        if df[col].dtype == object:
            df[col] = df[col].str.replace(',', '', regex=False)
            try:
                df[col] = df[col].astype(float)
            except:
                pass

    # 날짜 파싱
    df['date'] = pd.to_datetime(df['date'], format='%m/%d/%Y', errors='coerce')
    df.dropna(subset=['date'], inplace=True)

    df.rename(columns={"close": "price"}, inplace=True)
    df.set_index("date", inplace=True)
    gas_prices = df.loc[start_date:end_date]

    # 기술 지표 추가
    gas_prices["MA_5"] = gas_prices["price"].rolling(window=5).mean()
    gas_prices["MA_10"] = gas_prices["price"].rolling(window=10).mean()
    gas_prices["Volatility"] = gas_prices["price"].rolling(window=5).std()
    gas_prices.dropna(inplace=True)

    print("[2] 키워드 수집 및 트렌드 분석...")
    keywords = fetch_trending_keywords()
    keywords += fetch_news_keywords()
    if custom_keywords:
        keywords.extend(custom_keywords)
    keywords = list(set(keywords))  # 중복 제거

    pytrends = TrendReq(hl='en-US', tz=360)

    data = gas_prices.copy()
    impact_scores = []

    for kw in keywords:
        try:
            df_kw = get_trend(pytrends, kw, start_date, end_date)
            df_kw = df_kw.rename(columns={kw: kw.replace(' ', '_')})
            df_kw = df_kw.loc[data.index]
            if len(df_kw) == len(data):
                corr, _ = pearsonr(df_kw.iloc[:, 0], data["price"])
                score = abs(corr) * 100
                if score > 15:
                    impact_scores.append((kw, score))
                    data = data.join(df_kw)
        except Exception as e:
            print(f"{kw} 실패: {e}")

    impact_df = pd.DataFrame(impact_scores, columns=["Keyword", "ImpactScore"])
    impact_df = impact_df.sort_values("ImpactScore", ascending=False)
    impact_df.to_csv(f"{output_dir}/impact_scores.csv", index=False)

    print("[3] 상관관계 시각화...")
    if data.shape[1] > 1:
        corr = data.drop(columns="price").corr()
        sns.heatmap(corr, annot=True, cmap="coolwarm")
        plt.title("Keyword Correlation Matrix")
        plt.savefig(f"{output_dir}/correlation_matrix.png")
        plt.close()

    print("[4] 예측 모델 학습 및 평가...")
    X = data.drop(columns="price")
    y = data["price"]

    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)
    X_train, X_test, y_train, y_test = train_test_split(X_scaled, y, test_size=0.2, random_state=42)

    all_models = {
        "LinearRegression": LinearRegression(),
        "Ridge": Ridge(alpha=1.0),
        "Lasso": Lasso(alpha=0.1),
        "RandomForest": RandomForestRegressor(n_estimators=100, random_state=42)
    }

    if selected_models is None:
        selected_models = list(all_models.keys())

    models = {name: all_models[name] for name in selected_models}
    results = {}
    all_predictions = {}

    for name, model in models.items():
        model.fit(X_train, y_train)
        y_pred = model.predict(X_test)
        y_all = model.predict(X_scaled)
        mae = mean_absolute_error(y_test, y_pred)
        r2 = r2_score(y_test, y_pred)
        results[name] = {"MAE": mae, "R2": r2}
        all_predictions[name] = y_all
        print(f"{name} - MAE: {mae:.3f}, R^2: {r2:.3f}")

    pred_df = data[["price"]].copy()
    for name, preds in all_predictions.items():
        pred_df[f"predicted_{name}"] = preds

    pred_df.to_csv(f"{output_dir}/predicted_prices.csv")
    print("\n예측 완료. 결과는 output 디렉토리를 확인하세요.")

    best_model = max(results.items(), key=lambda x: x[1]['R2'])[0]
    print(f"\n💡 가장 높은 R^2를 기록한 모델: {best_model}")

    return impact_df, pred_df, results, best_model


# Streamlit 대시보드
if __name__ == "__main__":
    st.set_page_config(layout="wide")
    st.title("📈 천연가스 가격 예측 대시보드")

    st.sidebar.header("🔧 분석 설정")
    uploaded_file = st.sidebar.file_uploader("가스 가격 CSV 파일 업로드", type=["csv"])
    if uploaded_file is not None:
        start_date = st.sidebar.date_input("시작일", value=datetime.date(2022, 1, 1))
        end_date = st.sidebar.date_input("종료일", value=datetime.date(2022, 1, 31))
        custom_keywords = st.sidebar.text_input("사용자 추가 키워드 (쉼표 구분)")
        custom_keywords = [k.strip() for k in custom_keywords.split(",") if k.strip()] if custom_keywords else []

        all_models = ["LinearRegression", "Ridge", "Lasso", "RandomForest"]
        selected_models = st.sidebar.multiselect("사용할 모델 선택", all_models, default=all_models)

        if st.button("분석 실행 및 예측 갱신"):
            temp_path = os.path.join(output_dir, "temp_uploaded.csv")
            with open(temp_path, "wb") as f:
                f.write(uploaded_file.read())

            impact_df, pred_df, results, best_model = run_pipeline(temp_path, str(start_date), str(end_date), custom_keywords, selected_models)

            st.subheader("영향력 높은 키워드")
            st.dataframe(impact_df)

            if not impact_df.empty:
                top_keywords = impact_df.head(10)
                fig = px.bar(top_keywords, x="ImpactScore", y="Keyword", orientation='h', title="Top 10 영향력 키워드", color="ImpactScore", color_continuous_scale="viridis")
                st.plotly_chart(fig)

            heatmap_path = f"{output_dir}/correlation_matrix.png"
            if os.path.exists(heatmap_path):
                st.subheader("키워드 상관관계 히트맵")
                st.image(Image.open(heatmap_path), caption="Keyword Correlation Matrix", use_column_width=True)

            st.subheader("모델별 예측 결과")
            st.line_chart(pred_df)

            st.subheader("📊 실제 가격 vs 모델 예측 비교")
            fig = go.Figure()
            fig.add_trace(go.Scatter(x=pred_df.index, y=pred_df["price"], mode='lines', name='실제 가격'))
            for col in pred_df.columns:
                if col.startswith("predicted_"):
                    fig.add_trace(go.Scatter(x=pred_df.index, y=pred_df[col], mode='lines', name=col))
            fig.update_layout(title="가격 예측 비교", xaxis_title="날짜", yaxis_title="가격", height=500)
            st.plotly_chart(fig)

            st.subheader("모델 평가 지표")
            st.json(results)

            st.success(f"가장 우수한 모델은: {best_model}")
    else:
        st.info("왼쪽에서 가스 가격 CSV 파일을 업로드하고 설정을 지정하세요.")

# 스케줄러 설정
scheduler = BlockingScheduler()
@scheduler.scheduled_job('cron', hour=8)
def scheduled_daily_run():
    print(f"\n[{datetime.datetime.now()}] 자동 실행 시작")
    run_pipeline("/mnt/data/gas_prices.csv", "2022-01-01", "2022-01-31")
