import numpy as np
import cv2
import matplotlib.pyplot as plt
from matplotlib.path import Path
import matplotlib.patches as patches
from PIL import Image
import os
import tkinter as tk
from tkinter import filedialog, ttk, Scale, HORIZONTAL
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import threading
import time
from pathlib import Path as PathLib
import json

class StringArtGenerator:
    def __init__(self, master=None):
        """
        스트링 아트 생성기 초기화

        Args:
            master: tkinter 루트 윈도우
        """
        self.master = master
        self.image_path = None
        self.num_pins = 200
        self.min_pin_distance = 20
        self.num_lines = 2000
        self.pins = []
        self.lines = []
        self.canvas = None
        self.original_image = None
        self.processed_image = None
        self.result_folder = None
        self.pin_shape = "circle"  # circle 또는 square
        self.is_generating = False
        self.progress = 0
        self.dark_mode = False
        self.image_scale = 1.0  # 이미지 크기 조정 비율
        self.custom_pins = []  # 사용자가 추가한 핀
        self.restricted_areas = []  # 제한된 영역

        if master:
            self.setup_ui()

    def setup_ui(self):
        """GUI setup (English, scrollable menu, image scale in preview)"""
        self.master.title("String Art Generator")
        self.master.geometry("1280x720")
        self.master.minsize(1000, 600)

        self.style = ttk.Style()
        self.style.theme_use('clam')
        self.set_light_mode()

        # Main frame
        self.main_frame = ttk.Frame(self.master)
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # --- Scrollable left control panel ---
        self.control_canvas = tk.Canvas(self.main_frame, borderwidth=0, highlightthickness=0)
        self.control_scrollbar = ttk.Scrollbar(self.main_frame, orient="vertical", command=self.control_canvas.yview)
        self.control_frame = ttk.Frame(self.control_canvas)
        self.control_frame.bind(
            "<Configure>",
            lambda e: self.control_canvas.configure(scrollregion=self.control_canvas.bbox("all"))
        )
        self.control_canvas.create_window((0, 0), window=self.control_frame, anchor="nw")
        self.control_canvas.configure(yscrollcommand=self.control_scrollbar.set)
        self.control_canvas.pack(side=tk.LEFT, fill=tk.Y, padx=10, pady=10, expand=False)
        self.control_scrollbar.pack(side=tk.LEFT, fill=tk.Y)

        # Step 1: Image
        ttk.Label(self.control_frame, text="Step 1: Select Image", font=("Arial", 12, "bold")).pack(pady=(0, 5), anchor=tk.W)
        self.load_button = ttk.Button(self.control_frame, text="Load Image", command=self.load_image_dialog)
        self.load_button.pack(fill=tk.X, pady=5)

        # Step 2: Settings
        ttk.Label(self.control_frame, text="Step 2: Settings", font=("Arial", 12, "bold")).pack(pady=(15, 5), anchor=tk.W)

        # Image processing options
        process_frame = ttk.LabelFrame(self.control_frame, text="Image Processing")
        process_frame.pack(fill=tk.X, pady=5, padx=5)

        # Edge detection option
        self.edge_detection_var = tk.BooleanVar(value=False)
        edge_check = ttk.Checkbutton(process_frame, text="Enable Edge Detection",
                                   variable=self.edge_detection_var,
                                   command=self.update_edge_detection)
        edge_check.pack(fill=tk.X, pady=2)

        # Image inversion option
        self.invert_var = tk.BooleanVar(value=False)
        invert_check = ttk.Checkbutton(process_frame, text="Invert Image Colors",
                                     variable=self.invert_var,
                                     command=self.update_image_inversion)
        invert_check.pack(fill=tk.X, pady=2)

        # Pin shape selection
        pin_shape_frame = ttk.Frame(self.control_frame)
        pin_shape_frame.pack(fill=tk.X, pady=5)
        ttk.Label(pin_shape_frame, text="Pin Layout:").pack(side=tk.LEFT)
        self.pin_shape_var = tk.StringVar(value="circle")

        shapes = {"circle": "Circle", "square": "Square"}
        for shape, label in shapes.items():
            rb = ttk.Radiobutton(pin_shape_frame, text=label, value=shape, variable=self.pin_shape_var)
            rb.pack(side=tk.LEFT, padx=5)

        # Pin count slider
        pins_frame = ttk.Frame(self.control_frame)
        pins_frame.pack(fill=tk.X, pady=5)
        ttk.Label(pins_frame, text="Number of Pins:").pack(side=tk.LEFT)
        self.pins_var = tk.IntVar(value=self.num_pins)
        self.pins_entry = ttk.Entry(pins_frame, textvariable=self.pins_var, width=5)
        self.pins_entry.pack(side=tk.RIGHT)
        self.pins_scale = Scale(pins_frame, from_=50, to=400, orient=HORIZONTAL,
                              variable=self.pins_var, length=150)
        self.pins_scale.pack(side=tk.RIGHT, padx=5)

        # Minimum pin distance slider
        min_dist_frame = ttk.Frame(self.control_frame)
        min_dist_frame.pack(fill=tk.X, pady=5)
        ttk.Label(min_dist_frame, text="Minimum Pin Distance:").pack(side=tk.LEFT)
        self.min_dist_var = tk.IntVar(value=self.min_pin_distance)
        self.min_dist_entry = ttk.Entry(min_dist_frame, textvariable=self.min_dist_var, width=5)
        self.min_dist_entry.pack(side=tk.RIGHT)
        self.min_dist_scale = Scale(min_dist_frame, from_=5, to=50, orient=HORIZONTAL,
                                  variable=self.min_dist_var, length=150)
        self.min_dist_scale.pack(side=tk.RIGHT, padx=5)

        # Line count slider
        lines_frame = ttk.Frame(self.control_frame)
        lines_frame.pack(fill=tk.X, pady=5)
        ttk.Label(lines_frame, text="Maximum Lines:").pack(side=tk.LEFT)
        self.lines_var = tk.IntVar(value=self.num_lines)
        self.lines_entry = ttk.Entry(lines_frame, textvariable=self.lines_var, width=5)
        self.lines_entry.pack(side=tk.RIGHT)
        self.lines_scale = Scale(lines_frame, from_=100, to=50000, orient=HORIZONTAL,
                               variable=self.lines_var, length=150)
        self.lines_scale.pack(side=tk.RIGHT, padx=5)

        # Image scale slider
        scale_frame = ttk.Frame(self.control_frame)
        scale_frame.pack(fill=tk.X, pady=5)
        ttk.Label(scale_frame, text="Image Scale:").pack(side=tk.LEFT)
        self.scale_var = tk.DoubleVar(value=1.0)
        self.scale_entry = ttk.Entry(scale_frame, textvariable=self.scale_var, width=5)
        self.scale_entry.pack(side=tk.RIGHT)
        self.scale_scale = Scale(scale_frame, from_=0.1, to=2.0, orient=HORIZONTAL,
                               variable=self.scale_var, length=150, resolution=0.1,
                               command=lambda x: self.apply_image_scale())
        self.scale_scale.pack(side=tk.RIGHT, padx=5)

        # Line thickness slider
        thickness_frame = ttk.Frame(self.control_frame)
        thickness_frame.pack(fill=tk.X, pady=5)
        ttk.Label(thickness_frame, text="Line Thickness:").pack(side=tk.LEFT)
        self.thickness_var = tk.DoubleVar(value=1.0)
        self.thickness_entry = ttk.Entry(thickness_frame, textvariable=self.thickness_var, width=5)
        self.thickness_entry.pack(side=tk.RIGHT)
        self.thickness_scale = Scale(thickness_frame, from_=0.001, to=4.0, orient=HORIZONTAL,
                                   variable=self.thickness_var, length=150, resolution=0.001,
                                   command=lambda x: self.update_line_thickness())
        self.thickness_scale.pack(side=tk.RIGHT, padx=5)

        # Pin weight slider (for custom pins)
        weight_frame = ttk.Frame(self.control_frame)
        weight_frame.pack(fill=tk.X, pady=5)
        ttk.Label(weight_frame, text="Custom Pin Weight:").pack(side=tk.LEFT)
        self.pin_weight_var = tk.IntVar(value=128)
        self.pin_weight_entry = ttk.Entry(weight_frame, textvariable=self.pin_weight_var, width=5)
        self.pin_weight_entry.pack(side=tk.RIGHT)
        self.pin_weight_scale = Scale(weight_frame, from_=1, to=255, orient=HORIZONTAL,
                                    variable=self.pin_weight_var, length=150)
        self.pin_weight_scale.pack(side=tk.RIGHT, padx=5)

        # Pin addition mode toggle
        self.add_pin_button = ttk.Button(self.control_frame, text="Add Pin Mode",
                                       command=self.toggle_add_pin_mode)
        self.add_pin_button.pack(fill=tk.X, pady=5)

        # Restricted area mode toggle
        self.restrict_area_button = ttk.Button(self.control_frame, text="Restricted Area Mode",
                                             command=self.toggle_restrict_area_mode)
        self.restrict_area_button.pack(fill=tk.X, pady=5)

        # Generate button
        ttk.Label(self.control_frame, text="Step 3: Generate", font=("Arial", 12, "bold")).pack(pady=(15, 5), anchor=tk.W)
        self.generate_button = ttk.Button(self.control_frame, text="Generate String Art",
                                        command=self.start_generation_thread)
        self.generate_button.pack(fill=tk.X, pady=5)
        self.generate_button["state"] = "disabled"

        # Progress display
        self.progress_frame = ttk.Frame(self.control_frame)
        self.progress_frame.pack(fill=tk.X, pady=5)
        self.progress_bar = ttk.Progressbar(self.progress_frame, orient=tk.HORIZONTAL, length=100, mode='determinate')
        self.progress_bar.pack(fill=tk.X)
        self.progress_label = ttk.Label(self.progress_frame, text="")
        self.progress_label.pack(anchor=tk.W)

        # Save section
        ttk.Label(self.control_frame, text="Step 4: Save Results", font=("Arial", 12, "bold")).pack(pady=(15, 5), anchor=tk.W)
        self.save_button = ttk.Button(self.control_frame, text="Save Results", command=self.save_results)
        self.save_button.pack(fill=tk.X, pady=5)
        self.save_button["state"] = "disabled"

        # Print section
        ttk.Label(self.control_frame, text="Step 5: Print", font=("Arial", 12, "bold")).pack(pady=(15, 5), anchor=tk.W)
        print_frame = ttk.Frame(self.control_frame)
        print_frame.pack(fill=tk.X, pady=5)

        self.print_preview_button = ttk.Button(print_frame, text="Print Preview",
                                             command=lambda: self.print_tab("preview"))
        self.print_preview_button.pack(side=tk.LEFT, expand=True, padx=2)

        self.print_result_button = ttk.Button(print_frame, text="Print Result",
                                            command=lambda: self.print_tab("result"))
        self.print_result_button.pack(side=tk.LEFT, expand=True, padx=2)

        self.print_pins_button = ttk.Button(print_frame, text="Print Pin Layout",
                                          command=lambda: self.print_tab("pins"))
        self.print_pins_button.pack(side=tk.LEFT, expand=True, padx=2)

        # Theme toggle
        self.theme_button = ttk.Button(self.control_frame, text="Toggle Dark Mode", command=self.toggle_theme)
        self.theme_button.pack(fill=tk.X, pady=(20, 5))

        # Right visualization frame
        self.viz_frame = ttk.Frame(self.main_frame)
        self.viz_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Image preview and result tabs
        self.tab_control = ttk.Notebook(self.viz_frame)
        self.tab_control.pack(fill=tk.BOTH, expand=True)

        # Preview tab
        self.preview_tab = ttk.Frame(self.tab_control)
        self.tab_control.add(self.preview_tab, text="Image Preview")

        # Result tab
        self.result_tab = ttk.Frame(self.tab_control)
        self.tab_control.add(self.result_tab, text="String Art Result")

        # Pin layout tab
        self.pins_tab = ttk.Frame(self.tab_control)
        self.tab_control.add(self.pins_tab, text="Pin Layout")

        # Guide tab
        self.guide_tab = ttk.Frame(self.tab_control)
        self.tab_control.add(self.guide_tab, text="Guide")

        # Initialize tab displays
        self.setup_preview_tab()
        self.setup_result_tab()
        self.setup_pins_tab()
        self.setup_guide_tab()

        # Status bar
        self.status_bar = ttk.Label(self.master, text="Please load an image", relief=tk.SUNKEN, anchor=tk.W)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)

    def set_light_mode(self):
        """라이트 모드 스타일 설정"""
        self.dark_mode = False
        bg_color = "#f0f0f0"
        fg_color = "#333333"

        self.master.configure(background=bg_color)
        self.style.configure("TFrame", background=bg_color)
        self.style.configure("TLabel", background=bg_color, foreground=fg_color)
        self.style.configure("TButton", background=bg_color, foreground=fg_color)
        self.style.configure("TRadiobutton", background=bg_color, foreground=fg_color)
        self.style.configure("TNotebook", background=bg_color)
        self.style.configure("TNotebook.Tab", background=bg_color, foreground=fg_color)

    def set_dark_mode(self):
        """다크 모드 스타일 설정"""
        self.dark_mode = True
        bg_color = "#333333"
        fg_color = "#f0f0f0"

        self.master.configure(background=bg_color)
        self.style.configure("TFrame", background=bg_color)
        self.style.configure("TLabel", background=bg_color, foreground=fg_color)
        self.style.configure("TButton", background=bg_color, foreground=fg_color)
        self.style.configure("TRadiobutton", background=bg_color, foreground=fg_color)
        self.style.configure("TNotebook", background=bg_color)
        self.style.configure("TNotebook.Tab", background=bg_color, foreground=fg_color)

    def toggle_theme(self):
        """테마 토글 (라이트/다크 모드)"""
        if self.dark_mode:
            self.set_light_mode()
        else:
            self.set_dark_mode()

        # 모든 그래프 업데이트
        if self.original_image is not None:
            self.update_preview_tab()
        if self.canvas is not None:
            self.update_result_tab()
        if self.pins:
            self.update_pins_tab()

    def setup_preview_tab(self):
        """미리보기 탭 설정"""
        fig = plt.Figure(figsize=(10, 6), dpi=100)
        self.preview_canvas = FigureCanvasTkAgg(fig, self.preview_tab)
        self.preview_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

        self.preview_ax = fig.add_subplot(111)
        self.preview_ax.set_title("이미지를 불러와 주세요")
        self.preview_ax.axis('off')

        fig.tight_layout()
        self.preview_canvas.draw()

    def setup_result_tab(self):
        """결과 탭 설정"""
        fig = plt.Figure(figsize=(10, 6), dpi=100)
        self.result_canvas = FigureCanvasTkAgg(fig, self.result_tab)
        self.result_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

        self.result_ax = fig.add_subplot(111)
        self.result_ax.set_title("스트링 아트 결과가 여기에 표시됩니다")
        self.result_ax.axis('off')

        fig.tight_layout()
        self.result_canvas.draw()

    def setup_pins_tab(self):
        """핀 배치도 탭 설정"""
        fig = plt.Figure(figsize=(10, 6), dpi=100)
        self.pins_canvas = FigureCanvasTkAgg(fig, self.pins_tab)
        self.pins_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

        self.pins_ax = fig.add_subplot(111)
        self.pins_ax.set_title("핀 배치도")
        self.pins_ax.axis('off')

        fig.tight_layout()
        self.pins_canvas.draw()

    def setup_guide_tab(self):
        """제작 안내 탭 설정"""
        # 상단 안내 프레임
        guide_info_frame = ttk.Frame(self.guide_tab)
        guide_info_frame.pack(fill=tk.X, padx=10, pady=10)

        ttk.Label(guide_info_frame, text="제작 안내", font=("Arial", 14, "bold")).pack(anchor=tk.W)
        ttk.Label(guide_info_frame, text="아래 목록에 따라 핀을 순서대로 연결하세요.").pack(anchor=tk.W, pady=(0, 10))

        # 연결 목록 프레임
        self.guide_frame = ttk.Frame(self.guide_tab)
        self.guide_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 스크롤바 추가
        scrollbar = ttk.Scrollbar(self.guide_frame)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 연결 목록 텍스트 박스
        self.guide_text = tk.Text(self.guide_frame, yscrollcommand=scrollbar.set)
        self.guide_text.pack(fill=tk.BOTH, expand=True)
        scrollbar.config(command=self.guide_text.yview)

        # 초기 안내 텍스트
        self.guide_text.insert(tk.END, "스트링 아트 생성 후 연결 순서가 여기에 표시됩니다.")
        self.guide_text.config(state=tk.DISABLED)

    def load_image_dialog(self):
        """이미지 파일 선택 대화상자"""
        filetypes = [
            ("이미지 파일", "*.jpg *.jpeg *.png *.bmp *.gif"),
            ("JPEG 파일", "*.jpg *.jpeg"),
            ("PNG 파일", "*.png"),
            ("모든 파일", "*.*")
        ]

        image_path = filedialog.askopenfilename(
            title="이미지 파일 선택",
            filetypes=filetypes
        )

        if image_path:
            self.image_path = image_path
            self.load_and_process_image()
            self.generate_button["state"] = "normal"
            self.status_bar.config(text=f"이미지를 불러왔습니다: {os.path.basename(image_path)}")

    def load_and_process_image(self):
        """Load and process image while maintaining aspect ratio"""
        try:
            # Load image
            img = cv2.imread(self.image_path)
            if img is None:
                raise ValueError(f"Cannot load image: {self.image_path}")

            # Convert to grayscale
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

            # Get canvas size
            canvas_size = 800  # Fixed canvas size

            # Calculate scaling factor to fit image within canvas while maintaining aspect ratio
            h, w = gray.shape
            scale = min(canvas_size / w, canvas_size / h)

            # Calculate new dimensions
            new_w = int(w * scale)
            new_h = int(h * scale)

            # Resize image
            resized = cv2.resize(gray, (new_w, new_h))

            # Create a blank canvas
            self.original_image = np.ones((canvas_size, canvas_size), dtype=np.uint8) * 255

            # Calculate position to center the image
            x_offset = (canvas_size - new_w) // 2
            y_offset = (canvas_size - new_h) // 2

            # Place the resized image on the canvas
            self.original_image[y_offset:y_offset+new_h, x_offset:x_offset+new_w] = resized

            # Apply image inversion if enabled
            if self.invert_var.get():
                self.original_image = 255 - self.original_image

            # Apply edge detection if enabled
            if self.edge_detection_var.get():
                # Blur to reduce noise
                blurred = cv2.GaussianBlur(self.original_image, (5, 5), 0)
                # Edge detection
                self.processed_image = cv2.Canny(blurred, 50, 150)
            else:
                self.processed_image = self.original_image.copy()

            # Create empty canvas for string art
            self.canvas = np.ones((canvas_size, canvas_size), dtype=np.uint8) * 255

            # Update preview
            self.update_preview_tab()

            # Generate pins
            self.generate_pins()

            # Update all tabs
            self.update_all_tabs()

            return True

        except Exception as e:
            self.status_bar.config(text=f"Error: {str(e)}")
            return False

    def apply_image_scale(self):
        """이미지 크기 조정 적용 (확대 및 축소 모두 지원)"""
        if self.original_image is None:
            return

        scale = self.scale_var.get()
        if scale == self.image_scale:
            return

        self.image_scale = scale

        # 원본 이미지 로드 (이미지 파일이 있는 경우)
        if self.image_path and os.path.exists(self.image_path):
            # 원본 이미지 다시 로드
            img = cv2.imread(self.image_path)
            if img is not None:
                # 그레이스케일로 변환
                gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

                # 캔버스 크기
                canvas_size = 800

                # 원본 이미지의 비율을 유지하면서 캔버스에 맞게 크기 조정
                h, w = gray.shape
                base_scale = min(canvas_size / w, canvas_size / h)

                # 사용자 지정 스케일 적용
                final_scale = base_scale * scale

                # 새 크기 계산
                new_w = int(w * final_scale)
                new_h = int(h * final_scale)

                # 이미지 크기 조정
                resized = cv2.resize(gray, (new_w, new_h), interpolation=cv2.INTER_AREA if scale < 1 else cv2.INTER_LINEAR)

                # 빈 캔버스 생성
                self.original_image = np.ones((canvas_size, canvas_size), dtype=np.uint8) * 255

                # 이미지를 중앙에 배치하기 위한 오프셋 계산
                x_offset = (canvas_size - new_w) // 2
                y_offset = (canvas_size - new_h) // 2

                # 크기가 조정된 이미지를 캔버스에 배치
                if x_offset >= 0 and y_offset >= 0 and new_w > 0 and new_h > 0:
                    # 이미지가 캔버스보다 작은 경우
                    self.original_image[y_offset:y_offset+new_h, x_offset:x_offset+new_w] = resized
                else:
                    # 이미지가 캔버스보다 큰 경우, 중앙 부분만 잘라서 사용
                    crop_x_offset = max(0, -x_offset)
                    crop_y_offset = max(0, -y_offset)
                    crop_width = min(new_w, canvas_size)
                    crop_height = min(new_h, canvas_size)

                    canvas_x_offset = max(0, x_offset)
                    canvas_y_offset = max(0, y_offset)

                    # 캔버스에 맞게 이미지 자르기
                    self.original_image[canvas_y_offset:canvas_y_offset+crop_height,
                                       canvas_x_offset:canvas_x_offset+crop_width] = \
                        resized[crop_y_offset:crop_y_offset+crop_height,
                               crop_x_offset:crop_x_offset+crop_width]

                # 이미지 반전 적용 (설정된 경우)
                if self.invert_var.get():
                    self.original_image = 255 - self.original_image

        # 이미지 처리 업데이트
        if self.edge_detection_var.get():
            # 블러 처리 후 가장자리 감지
            blurred = cv2.GaussianBlur(self.original_image, (5, 5), 0)
            self.processed_image = cv2.Canny(blurred, 50, 150)
        else:
            self.processed_image = self.original_image.copy()

        # 모든 탭 업데이트
        self.update_all_tabs()

    def update_preview_tab(self):
        """미리보기 탭 업데이트"""
        if self.original_image is None or self.processed_image is None:
            return

        self.preview_ax.clear()

        # 원본 이미지와 처리된 이미지를 나란히 표시
        self.preview_ax.imshow(self.original_image, cmap='gray')
        self.preview_ax.set_title("원본 이미지 및 처리된 이미지")
        self.preview_ax.axis('off')

        # 사용자 추가 핀 표시
        for x, y in self.custom_pins:
            self.preview_ax.plot(x, y, 'ro', markersize=5)

        # 제한 영역 표시
        for x1, y1, x2, y2 in self.restricted_areas:
            rect = patches.Rectangle((x1, y1), x2-x1, y2-y1,
                                   fill=True, color='red', alpha=0.3)
            self.preview_ax.add_patch(rect)

        # 오른쪽 하단에 처리된 이미지를 작게 표시
        small_ax = self.preview_ax.inset_axes([0.65, 0.65, 0.3, 0.3])
        small_ax.imshow(self.processed_image, cmap='gray')
        small_ax.set_title("가장자리 감지")
        small_ax.axis('off')

        self.preview_canvas.draw()

    def update_result_tab(self):
        """결과 탭 업데이트"""
        if self.canvas is None:
            return

        self.result_ax.clear()

        # 결과 이미지 표시
        self.result_ax.imshow(self.canvas, cmap='gray')
        self.result_ax.set_title(f"스트링 아트 결과 (핀: {self.num_pins}, 선: {len(self.lines)})")
        self.result_ax.axis('off')

        # 마지막 10개 선을 빨간색으로 표시 (진행상황을 보여주기 위해)
        if len(self.lines) > 10:
            for i in range(max(0, len(self.lines)-10), len(self.lines)):
                start_pin, end_pin = self.lines[i]
                self.result_ax.plot([self.pins[start_pin][0], self.pins[end_pin][0]],
                                   [self.pins[start_pin][1], self.pins[end_pin][1]],
                                   'r-', linewidth=0.5, alpha=0.7)

        self.result_canvas.draw()

    def update_pins_tab(self):
        """Update pin layout tab with continuous numbering including custom pins"""
        if not self.pins:
            return

        self.pins_ax.clear()

        # Background image (faded)
        if self.original_image is not None:
            self.pins_ax.imshow(self.original_image, cmap='gray', alpha=0.2)

        # Draw pins
        pin_x = [p[0] for p in self.pins]
        pin_y = [p[1] for p in self.pins]
        self.pins_ax.plot(pin_x, pin_y, 'bo', markersize=3)

        # Show pin numbers (continuous numbering)
        for i, (x, y) in enumerate(self.pins):
            self.pins_ax.text(x, y, str(i+1),
                            fontsize=8, color='blue',
                            ha='center', va='center')

        # Highlight custom pins with different color
        regular_pin_count = self.num_pins
        if len(self.pins) > regular_pin_count:
            for i in range(regular_pin_count, len(self.pins)):
                x, y = self.pins[i]
                self.pins_ax.plot(x, y, 'co', markersize=5)  # Cyan color for custom pins
                self.pins_ax.text(x, y, str(i+1),
                                fontsize=8, color='blue',
                                ha='center', va='center')

        # Show restricted areas in red
        for x1, y1, x2, y2 in self.restricted_areas:
            rect = patches.Rectangle((x1, y1), x2-x1, y2-y1,
                                   fill=True, color='red', alpha=0.3)
            self.pins_ax.add_patch(rect)

        # Draw pin layout shape
        if self.pin_shape == "circle":
            circle = plt.Circle((400, 400), 390, fill=False, color='blue', linestyle='-', alpha=0.7)
            self.pins_ax.add_patch(circle)
        elif self.pin_shape == "square":
            square = patches.Rectangle((10, 10), 780, 780, fill=False, color='blue', linestyle='-', alpha=0.7)
            self.pins_ax.add_patch(square)

        self.pins_ax.set_title(f"Pin Layout (Total: {len(self.pins)}, Custom: {len(self.custom_pins)})")
        self.pins_ax.axis('off')

        self.pins_canvas.draw()

    def update_guide_tab(self):
        """제작 안내 탭 업데이트"""
        if not self.lines:
            return

        # 텍스트 박스 업데이트
        self.guide_text.config(state=tk.NORMAL)
        self.guide_text.delete(1.0, tk.END)

        self.guide_text.insert(tk.END, f"스트링 아트 제작 지침\n")
        self.guide_text.insert(tk.END, f"====================\n\n")
        self.guide_text.insert(tk.END, f"핀 개수: {self.num_pins}\n")
        self.guide_text.insert(tk.END, f"선 개수: {len(self.lines)}\n\n")
        self.guide_text.insert(tk.END, "연결 순서:\n")

        for i, (start, end) in enumerate(self.lines):
            self.guide_text.insert(tk.END, f"{i+1}. 핀 #{start+1} → 핀 #{end+1}\n")

            # 20개 연결마다 구분선 추가
            if (i+1) % 20 == 0:
                self.guide_text.insert(tk.END, f"------------- {i+1}개 완료 -------------\n")

        self.guide_text.config(state=tk.DISABLED)

    def generate_pins(self):
        """Generate pin positions including custom pins with weights"""
        self.pins = []
        self.num_pins = self.pins_var.get()

        if self.pin_shape == "circle":
            # Circular layout
            r = 390  # radius (for 800x800 image)
            center = (400, 400)  # center point

            for i in range(self.num_pins):
                angle = 2 * np.pi * i / self.num_pins
                x = int(center[0] + r * np.cos(angle))
                y = int(center[1] + r * np.sin(angle))
                self.pins.append((x, y))

        elif self.pin_shape == "square":
            # Square layout
            pins_per_side = self.num_pins // 4
            extra_pins = self.num_pins % 4

            # Top edge
            for i in range(pins_per_side + (1 if extra_pins > 0 else 0)):
                x = 10 + int(i * 780 / (pins_per_side - 1)) if pins_per_side > 1 else 400
                y = 10
                self.pins.append((x, y))

            # Right edge
            for i in range(pins_per_side + (1 if extra_pins > 1 else 0)):
                x = 790
                y = 10 + int(i * 780 / (pins_per_side - 1)) if pins_per_side > 1 else 400
                self.pins.append((x, y))

            # Bottom edge
            for i in range(pins_per_side + (1 if extra_pins > 2 else 0)):
                x = 790 - int(i * 780 / (pins_per_side - 1)) if pins_per_side > 1 else 400
                y = 790
                self.pins.append((x, y))

            # Left edge
            for i in range(pins_per_side + (1 if extra_pins > 3 else 0)):
                x = 10
                y = 790 - int(i * 780 / (pins_per_side - 1)) if pins_per_side > 1 else 400
                self.pins.append((x, y))

        # Add custom pins with weights (they will be numbered after regular pins)
        # Custom pins are included in the path calculation with their weights
        custom_pin_count = 0
        for x, y, weight in self.custom_pins:
            # Store the pin position for continuous numbering
            # The weight is used in calculate_line_intensity for path priority
            self.pins.append((x, y))  # Add to pins list for continuous numbering
            custom_pin_count += 1

        # Log the number of custom pins added (for debugging)
        if custom_pin_count > 0:
            print(f"Added {custom_pin_count} custom pins with weights to the path calculation")

        return self.pins

    def calculate_line_intensity(self, start_pin, end_pin):
        """Calculate line intensity considering custom pin weights"""
        # Generate points along the line
        num_points = 100
        xs = np.linspace(start_pin[0], end_pin[0], num_points, dtype=int)
        ys = np.linspace(start_pin[1], end_pin[1], num_points, dtype=int)

        # Calculate intensity along the line
        intensity = 0
        for x, y in zip(xs, ys):
            if 0 <= x < self.processed_image.shape[1] and 0 <= y < self.processed_image.shape[0]:
                # Skip white pixels (255)
                if self.processed_image[y, x] != 255:
                    intensity += 1

        # Add weight if either pin is a custom pin - give priority to custom pins
        for i, (cx, cy, weight) in enumerate(self.custom_pins):
            if (start_pin[0] == cx and start_pin[1] == cy) or \
               (end_pin[0] == cx and end_pin[1] == cy):
                # Add the weight as a priority factor
                intensity += weight
                break

        return intensity

    def generate_string_art(self):
        """스트링 아트 생성 - 사용자 추가 핀을 경로에 포함"""
        self.lines = []
        current_pin = 0
        min_distance = self.min_pin_distance  # 같은 핀이나 인접한 핀으로의 연결 방지
        max_lines = self.num_lines  # 최대 선 개수
        visited_lines = set()  # 이미 사용된 선 저장
        line_thickness = max(1, int(self.thickness_var.get()))  # Get current line thickness

        # 빈 캔버스 생성 (흰색 배경)
        self.canvas = np.ones((800, 800), dtype=np.uint8) * 255

        # 핀 위치 업데이트 (사용자 추가 핀 포함)
        self.pin_shape = self.pin_shape_var.get()
        self.generate_pins()

        # 핀 개수 확인 (사용자 추가 핀 포함)
        total_pins = len(self.pins)

        for i in range(max_lines):
            best_pin = -1
            best_intensity = -1

            # 모든 핀을 검사하여 가장 좋은 다음 핀 찾기
            for next_pin in range(total_pins):
                # 자기 자신이나 인접한 핀은 건너뛰기
                if abs(next_pin - current_pin) < min_distance or \
                   abs(next_pin - current_pin) > total_pins - min_distance:
                    continue

                # 이미 사용된 선이면 건너뛰기
                line_key = tuple(sorted([current_pin, next_pin]))
                if line_key in visited_lines:
                    continue

                # 제한 영역을 통과하는 선은 건너뛰기
                if self.is_line_in_restricted_area(self.pins[current_pin], self.pins[next_pin]):
                    continue

                # 두 핀 사이 직선의 강도 계산 (사용자 추가 핀의 가중치 포함)
                intensity = self.calculate_line_intensity(self.pins[current_pin], self.pins[next_pin])

                # 사용자 추가 핀에 우선순위 부여
                if intensity > best_intensity:
                    best_intensity = intensity
                    best_pin = next_pin

            # 적합한 핀을 찾았으면 선 추가
            if best_pin != -1:
                self.lines.append((current_pin, best_pin))
                visited_lines.add(tuple(sorted([current_pin, best_pin])))

                # 선 그리기 (현재 설정된 두께로)
                cv2.line(self.canvas, self.pins[current_pin], self.pins[best_pin], 0, line_thickness)

                # 다음 시작점 업데이트
                current_pin = best_pin

                # 진행 상황 업데이트 (10% 단위)
                progress_pct = (i + 1) / max_lines * 100
                if int(progress_pct) % 10 == 0:
                    self.progress = progress_pct
                    # 결과 탭 업데이트 (30줄마다)
                    if (i + 1) % 30 == 0:
                        self.update_ui()
            else:
                # 더 이상 선을 추가할 수 없으면 종료
                break

        # 최종 업데이트
        self.progress = 100
        self.is_generating = False
        self.update_ui()

        return self.canvas, self.lines

    def update_ui(self):
        """UI 업데이트 (스레드 안전하게)"""
        if not self.master:
            return

        self.master.after(0, self._update_ui_in_main_thread)

    def _update_ui_in_main_thread(self):
        """메인 스레드에서 UI 업데이트"""
        # 진행 상황 업데이트
        self.progress_bar["value"] = self.progress
        self.progress_label.config(text=f"진행 상황: {int(self.progress)}%")

        # 결과 탭 업데이트
        self.update_result_tab()

        # 생성이 완료된 경우
        if self.progress >= 100:
            self.generate_button["state"] = "normal"
            self.save_button["state"] = "normal"
            self.status_bar.config(text=f"스트링 아트 생성 완료: {len(self.lines)}개의 선 생성됨")

            # 핀 배치도 업데이트
            self.update_pins_tab()

            # 제작 안내 업데이트
            self.update_guide_tab()

            # 결과 탭으로 전환
            self.tab_control.select(1)  # 결과 탭 (인덱스 1)

    def start_generation_thread(self):
        """스트링 아트 생성 스레드 시작"""
        if self.original_image is None:
            self.status_bar.config(text="먼저 이미지를 불러와주세요.")
            return

        if self.is_generating:
            self.status_bar.config(text="이미 생성 중입니다. 완료될 때까지 기다려주세요.")
            return

        # 설정 값 업데이트
        self.num_pins = self.pins_var.get()
        self.num_lines = self.lines_var.get()
        self.min_pin_distance = self.min_dist_var.get()
        self.pin_shape = self.pin_shape_var.get()

        # UI 상태 업데이트
        self.generate_button["state"] = "disabled"
        self.save_button["state"] = "disabled"
        self.is_generating = True
        self.progress = 0
        self.progress_bar["value"] = 0
        self.progress_label.config(text="생성 준비 중...")
        self.status_bar.config(text="스트링 아트 생성 중...")

        # 스레드 시작
        thread = threading.Thread(target=self.generate_string_art)
        thread.daemon = True
        thread.start()

    def save_results(self):
        """결과물 저장"""
        if not self.lines or not self.pins:
            self.status_bar.config(text="저장할 결과가 없습니다.")
            return

        # 저장 폴더 선택
        folder = filedialog.askdirectory(title="결과를 저장할 폴더 선택")
        if not folder:
            return

        try:
            # 폴더 생성 (이미 있는 경우 무시)
            folder_path = os.path.join(folder, "string_art_result")
            os.makedirs(folder_path, exist_ok=True)

            # 1. 스트링 아트 결과 이미지 저장
            result_img_path = os.path.join(folder_path, "string_art_result.png")
            cv2.imwrite(result_img_path, self.canvas)

            # 2. 핀 배치도 이미지 저장
            fig = plt.Figure(figsize=(10, 10), dpi=150)
            ax = fig.add_subplot(111)

            # 핀 배치 그리기
            pin_x = [p[0] for p in self.pins]
            pin_y = [p[1] for p in self.pins]
            ax.plot(pin_x, pin_y, 'ro', markersize=2)

            # 핀 번호 표시 (10개 간격)
            for i in range(0, len(self.pins), 10):
                ax.text(self.pins[i][0], self.pins[i][1], str(i+1),
                       fontsize=8, color='blue',
                       ha='center', va='center')

            # 핀 배치 형태 테두리 그리기
            if self.pin_shape == "circle":
                circle = plt.Circle((400, 400), 390, fill=False, color='blue', linestyle='-', alpha=0.7)
                ax.add_patch(circle)
            elif self.pin_shape == "square":
                square = patches.Rectangle((10, 10), 780, 780, fill=False, color='blue', linestyle='-', alpha=0.7)
                ax.add_patch(square)

            ax.set_title(f"핀 배치도 (총 {len(self.pins)}개)")
            ax.axis('off')
            ax.set_xlim(0, 800)
            ax.set_ylim(0, 800)

            pins_img_path = os.path.join(folder_path, "pin_layout.png")
            fig.tight_layout()
            fig.savefig(pins_img_path, dpi=300, bbox_inches='tight')

            # 3. 제작 지침 저장
            instructions_path = os.path.join(folder_path, "instructions.txt")
            with open(instructions_path, 'w', encoding='utf-8') as f:
                f.write(f"스트링 아트 제작 지침\n")
                f.write(f"====================\n\n")
                f.write(f"핀 개수: {self.num_pins}\n")
                f.write(f"선 개수: {len(self.lines)}\n\n")
                f.write("연결 순서:\n")

                for i, (start, end) in enumerate(self.lines):
                    f.write(f"{i+1}. 핀 #{start+1} → 핀 #{end+1}\n")

                    # 20개 연결마다 구분선 추가
                    if (i+1) % 20 == 0:
                        f.write(f"------------- {i+1}개 완료 -------------\n")

            # 4. 핀 좌표 및 연결 정보 JSON 저장 (후속 작업을 위함)
            data = {
                "pins": self.pins,
                "lines": self.lines,
                "settings": {
                    "num_pins": self.num_pins,
                    "min_pin_distance": self.min_pin_distance,
                    "num_lines": self.num_lines,
                    "pin_shape": self.pin_shape
                }
            }

            json_path = os.path.join(folder_path, "string_art_data.json")
            with open(json_path, 'w') as f:
                json.dump(data, f)

            # 5. 단계별 이미지 생성 (선 20개마다 이미지 저장)
            steps_folder = os.path.join(folder_path, "steps")
            os.makedirs(steps_folder, exist_ok=True)

            if len(self.lines) > 0:
                # 주요 단계에서만 이미지 저장 (선이 많을 수 있으므로)
                step_counts = min(20, len(self.lines))  # 최대 20개 단계 이미지
                step_size = max(1, len(self.lines) // step_counts)

                for step in range(0, len(self.lines), step_size):
                    if step == 0:
                        continue  # 첫 단계는 빈 이미지이므로 건너뜀

                    # 해당 단계까지의 이미지 생성
                    step_canvas = np.ones((800, 800), dtype=np.uint8) * 255

                    for i in range(min(step, len(self.lines))):
                        start_pin, end_pin = self.lines[i]
                        cv2.line(step_canvas, self.pins[start_pin], self.pins[end_pin], 0, 1)

                    # 이미지 저장
                    step_img_path = os.path.join(steps_folder, f"step_{step}.png")
                    cv2.imwrite(step_img_path, step_canvas)

            self.status_bar.config(text=f"결과가 저장되었습니다: {folder_path}")

        except Exception as e:
            self.status_bar.config(text=f"저장 중 오류 발생: {str(e)}")

    def is_line_in_restricted_area(self, start_pin, end_pin):
        """선이 제한 영역을 통과하는지 확인 (제한 영역은 경로에서 제외)"""
        # 제한 영역이 없으면 빠르게 반환
        if not self.restricted_areas:
            return False

        for x1, y1, x2, y2 in self.restricted_areas:
            # 선분과 사각형의 교차 여부 확인
            if self.line_intersects_rect(start_pin, end_pin, (x1, y1, x2, y2)):
                return True  # 제한 영역과 교차하면 True 반환 (경로에서 제외)
        return False  # 모든 제한 영역과 교차하지 않으면 False 반환

    def line_intersects_rect(self, start, end, rect):
        """선분과 사각형의 교차 여부 확인"""
        x1, y1, x2, y2 = rect

        # 선분의 양 끝점
        x3, y3 = start
        x4, y4 = end

        # 선분이 사각형 내부에 있는지 확인
        if (x1 <= x3 <= x2 and y1 <= y3 <= y2) or \
           (x1 <= x4 <= x2 and y1 <= y4 <= y2):
            return True

        # 선분이 사각형의 변과 교차하는지 확인
        lines = [
            ((x1, y1), (x2, y1)),  # 상단
            ((x2, y1), (x2, y2)),  # 우측
            ((x2, y2), (x1, y2)),  # 하단
            ((x1, y2), (x1, y1))   # 좌측
        ]

        for line in lines:
            if self.line_intersects_line((x3, y3), (x4, y4), line[0], line[1]):
                return True

        return False

    def line_intersects_line(self, p1, p2, p3, p4):
        """두 선분의 교차 여부 확인"""
        def ccw(A, B, C):
            return (C[1]-A[1]) * (B[0]-A[0]) > (B[1]-A[1]) * (C[0]-A[0])

        return ccw(p1, p3, p4) != ccw(p2, p3, p4) and \
               ccw(p1, p2, p3) != ccw(p1, p2, p4)

    def toggle_add_pin_mode(self):
        """Toggle pin addition mode with preview"""
        if self.add_pin_button.cget("text") == "Add Pin Mode":
            self.add_pin_button.configure(text="Add Pin Mode (Active)")
            self.preview_canvas.mpl_connect('button_press_event', self.on_canvas_click)
            self.preview_canvas.mpl_connect('motion_notify_event', self.on_canvas_motion)
        else:
            self.add_pin_button.configure(text="Add Pin Mode")
            self.preview_canvas.mpl_disconnect('button_press_event')
            self.preview_canvas.mpl_disconnect('motion_notify_event')
            # Clear preview
            self.update_preview_tab()

    def on_canvas_motion(self, event):
        """Handle mouse motion for pin preview with yellow cursor"""
        if event.xdata is None or event.ydata is None:
            return

        # Update preview with yellow dot at mouse position
        self.preview_ax.clear()
        self.preview_ax.imshow(self.original_image, cmap='gray')
        self.preview_ax.set_title("Original Image - Add Pin Mode")
        self.preview_ax.axis('off')

        # Show all pins (regular pins)
        pin_x = [p[0] for p in self.pins]
        pin_y = [p[1] for p in self.pins]
        self.preview_ax.plot(pin_x, pin_y, 'bo', markersize=3, alpha=0.3)

        # Show existing custom pins with weight indicator
        for i, (x, y, weight) in enumerate(self.custom_pins):
            # Adjust marker size based on weight (normalized)
            marker_size = 5 + (weight / 255) * 3
            self.preview_ax.plot(x, y, 'bo', markersize=marker_size)
            self.preview_ax.text(x, y, str(i+1), color='blue', fontsize=8, ha='center', va='center')

        # Show preview pin in yellow
        self.preview_ax.plot(event.xdata, event.ydata, 'yo', markersize=6)

        # Add text showing the current weight
        current_weight = self.pin_weight_var.get()
        self.preview_ax.text(event.xdata, event.ydata-10, f"Weight: {current_weight}",
                           color='yellow', fontsize=9, ha='center', va='center',
                           bbox=dict(facecolor='black', alpha=0.5))

        # Show restricted areas
        for x1, y1, x2, y2 in self.restricted_areas:
            rect = patches.Rectangle((x1, y1), x2-x1, y2-y1,
                                   fill=True, color='red', alpha=0.3)
            self.preview_ax.add_patch(rect)

        # Show edge detection result if enabled
        if self.edge_detection_var.get() and self.processed_image is not None:
            small_ax = self.preview_ax.inset_axes([0.65, 0.65, 0.3, 0.3])
            small_ax.imshow(self.processed_image, cmap='gray')
            small_ax.set_title("Edge Detection")
            small_ax.axis('off')

        self.preview_canvas.draw()

    def on_canvas_click(self, event):
        """Handle canvas click for pin addition with light blue color"""
        if event.xdata is None or event.ydata is None:
            return

        x, y = int(event.xdata), int(event.ydata)

        # Add pin with weight
        weight = self.pin_weight_var.get()
        self.custom_pins.append((x, y, weight))

        # Update pin layout
        self.update_pins_tab()

        # Update preview with blue dot at clicked position
        self.preview_ax.plot(x, y, 'bo', markersize=5)
        self.preview_ax.text(x, y, str(len(self.custom_pins)), color='blue', fontsize=8, ha='center', va='center')
        self.preview_canvas.draw()

        # Update status
        self.status_bar.config(text=f"Pin added at ({x}, {y}) with weight {weight}")

    def toggle_restrict_area_mode(self):
        """Toggle restricted area mode with preview"""
        if self.restrict_area_button.cget("text") == "Restricted Area Mode":
            self.restrict_area_button.configure(text="Restricted Area Mode (Active)")
            self.preview_canvas.mpl_connect('button_press_event', self.on_restrict_area_click)
            self.preview_canvas.mpl_connect('motion_notify_event', self.on_restrict_area_motion)
        else:
            self.restrict_area_button.configure(text="Restricted Area Mode")
            self.preview_canvas.mpl_disconnect('button_press_event')
            self.preview_canvas.mpl_disconnect('motion_notify_event')
            # Clear preview
            self.update_preview_tab()

    def on_restrict_area_motion(self, event):
        """Handle mouse motion for restricted area preview with orange cursor"""
        if event.xdata is None or event.ydata is None:
            return

        # Update preview with orange preview rectangle
        self.preview_ax.clear()
        self.preview_ax.imshow(self.original_image, cmap='gray')
        self.preview_ax.set_title("Original Image - Restricted Area Mode")
        self.preview_ax.axis('off')

        # Show all pins (regular pins)
        pin_x = [p[0] for p in self.pins]
        pin_y = [p[1] for p in self.pins]
        self.preview_ax.plot(pin_x, pin_y, 'bo', markersize=3, alpha=0.3)

        # Show existing pins with weight indicator
        for i, (x, y, weight) in enumerate(self.custom_pins):
            # Adjust marker size based on weight (normalized)
            marker_size = 5 + (weight / 255) * 3
            self.preview_ax.plot(x, y, 'bo', markersize=marker_size)
            self.preview_ax.text(x, y, str(i+1), color='blue', fontsize=8, ha='center', va='center')

        # Show existing restricted areas in red
        for x1, y1, x2, y2 in self.restricted_areas:
            rect = patches.Rectangle((x1, y1), x2-x1, y2-y1,
                                   fill=True, color='red', alpha=0.3)
            self.preview_ax.add_patch(rect)

        # Show preview restricted area in orange
        preview_rect = patches.Rectangle((event.xdata-5, event.ydata-5), 10, 10,
                                       fill=True, color='orange', alpha=0.5)
        self.preview_ax.add_patch(preview_rect)

        self.preview_canvas.draw()

    def on_restrict_area_click(self, event):
        """Handle restricted area click with red display"""
        if event.xdata is None or event.ydata is None:
            return

        x, y = int(event.xdata), int(event.ydata)

        # Add restricted area (10x10 pixel area)
        self.restricted_areas.append((x-5, y-5, x+5, y+5))

        # Update preview to show the new restricted area in red
        self.preview_ax.clear()
        self.preview_ax.imshow(self.original_image, cmap='gray')
        self.preview_ax.set_title("Original Image - Restricted Area Mode")
        self.preview_ax.axis('off')

        # Show all pins
        pin_x = [p[0] for p in self.pins]
        pin_y = [p[1] for p in self.pins]
        self.preview_ax.plot(pin_x, pin_y, 'bo', markersize=3, alpha=0.3)

        # Show custom pins
        for i, (x, y, weight) in enumerate(self.custom_pins):
            marker_size = 5 + (weight / 255) * 3
            self.preview_ax.plot(x, y, 'bo', markersize=marker_size)
            self.preview_ax.text(x, y, str(i+1), color='blue', fontsize=8, ha='center', va='center')

        # Show all restricted areas in red
        for x1, y1, x2, y2 in self.restricted_areas:
            rect = patches.Rectangle((x1, y1), x2-x1, y2-y1,
                                   fill=True, color='red', alpha=0.3)
            self.preview_ax.add_patch(rect)

        self.preview_canvas.draw()

        # Update pins tab to show restricted areas
        self.update_pins_tab()

        # Update status
        self.status_bar.config(text=f"Restricted area added at ({x}, {y})")

    def update_edge_detection(self):
        """Update edge detection based on user selection"""
        if self.original_image is None:
            return

        if self.edge_detection_var.get():
            # Apply edge detection
            blurred = cv2.GaussianBlur(self.original_image, (5, 5), 0)
            self.processed_image = cv2.Canny(blurred, 50, 150)
        else:
            # Use original image
            self.processed_image = self.original_image.copy()

        # Update all tabs
        self.update_all_tabs()

    def update_image_inversion(self):
        """Update image inversion based on user selection"""
        if self.original_image is None:
            return

        # Reload and process image with new inversion setting
        self.load_and_process_image()

    def update_line_thickness(self):
        """Update line thickness in string art result in real-time for all paths"""
        if self.canvas is None or not self.lines:
            return

        # Clear canvas
        self.canvas = np.ones((800, 800), dtype=np.uint8) * 255

        # Redraw all lines with new thickness
        thickness = self.thickness_var.get()  # Get current line thickness as float
        # Ensure minimum thickness of 0.001
        if thickness < 0.001:
            thickness = 0.001
            self.thickness_var.set(0.001)

        # Convert to integer for cv2.line but ensure minimum of 1 pixel
        line_width = max(1, int(thickness))

        # Redraw all lines with the new thickness
        for start_pin, end_pin in self.lines:
            # Get pin coordinates
            start_coords = self.pins[start_pin]
            end_coords = self.pins[end_pin]

            # Draw the line with the new thickness
            cv2.line(self.canvas, start_coords, end_coords, 0, line_width)

        # Update result tab immediately to show changes in real-time
        self.update_result_tab()

    def update_all_tabs(self):
        """Update all visualization tabs"""
        self.update_preview_tab()
        self.update_result_tab()
        self.update_pins_tab()
        self.update_guide_tab()

    def print_tab(self, tab_name):
        """Print the specified tab content"""
        if tab_name == "preview":
            fig = self.preview_canvas.figure
        elif tab_name == "result":
            fig = self.result_canvas.figure
        elif tab_name == "pins":
            fig = self.pins_canvas.figure
        else:
            return

        # Create a temporary file
        temp_file = "temp_print.png"
        fig.savefig(temp_file, dpi=300, bbox_inches='tight')

        # Open the image with the default image viewer
        os.startfile(temp_file)

        # Schedule file deletion after a delay
        self.master.after(1000, lambda: os.remove(temp_file) if os.path.exists(temp_file) else None)

def main():
    root = tk.Tk()
    StringArtGenerator(root)  # Create the app instance
    root.mainloop()

if __name__ == "__main__":
    main()