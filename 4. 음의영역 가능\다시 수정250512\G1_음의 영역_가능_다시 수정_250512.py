import numpy as np
import cv2
import matplotlib.pyplot as plt
from matplotlib.path import Path
import matplotlib.patches as patches
from PIL import Image
import os
import tkinter as tk
from tkinter import filedialog, ttk, Scale, HORIZONTAL
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import threading
import time
from pathlib import Path as PathLib
import json

class StringArtGenerator:
    def __init__(self, master=None):
        """
        String Art Generator Initialization
        
        Args:
            master: tkinter root window
        """
        self.master = master
        self.image_path = None
        self.num_pins = 200
        self.min_pin_distance = 20
        self.num_lines = 2000
        self.pins = []
        self.lines = []
        self.canvas = None
        self.original_image = None
        self.processed_image = None
        self.result_folder = None
        self.pin_shape = "circle"  # circle or square
        self.is_generating = False
        self.progress = 0
        self.dark_mode = False
        self.image_scale = 1.0  # image scale ratio
        self.custom_pins = []  # user added pins
        self.restricted_areas = []  # restricted areas
        self.add_pin_mode = False
        self.restrict_area_mode = False
        
        if master:
            self.setup_ui()
    
    def setup_ui(self):
        """GUI setup (English, scrollable menu, image scale in preview)"""
        self.master.title("String Art Generator")
        self.master.geometry("1280x720")
        self.master.minsize(1000, 600)

        self.style = ttk.Style()
        self.style.theme_use('clam')
        self.set_light_mode()

        # Main frame
        self.main_frame = ttk.Frame(self.master)
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # --- Scrollable left control panel ---
        self.control_canvas = tk.Canvas(self.main_frame, borderwidth=0, highlightthickness=0)
        self.control_scrollbar = ttk.Scrollbar(self.main_frame, orient="vertical", command=self.control_canvas.yview)
        self.control_frame = ttk.Frame(self.control_canvas)
        self.control_frame.bind(
            "<Configure>",
            lambda e: self.control_canvas.configure(scrollregion=self.control_canvas.bbox("all"))
        )
        self.control_canvas.create_window((0, 0), window=self.control_frame, anchor="nw")
        self.control_canvas.configure(yscrollcommand=self.control_scrollbar.set)
        self.control_canvas.pack(side=tk.LEFT, fill=tk.Y, padx=10, pady=10, expand=False)
        self.control_scrollbar.pack(side=tk.LEFT, fill=tk.Y)

        # Step 1: Image
        ttk.Label(self.control_frame, text="Step 1: Select Image", font=("Arial", 12, "bold")).pack(pady=(0, 5), anchor=tk.W)
        self.load_button = ttk.Button(self.control_frame, text="Load Image", command=self.load_image_dialog)
        self.load_button.pack(fill=tk.X, pady=5)

        # Step 2: Settings
        ttk.Label(self.control_frame, text="Step 2: Settings", font=("Arial", 12, "bold")).pack(pady=(15, 5), anchor=tk.W)

        # Edge detection option
        edge_frame = ttk.Frame(self.control_frame)
        edge_frame.pack(fill=tk.X, pady=5)
        self.edge_detection_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(edge_frame, text="Enable Edge Detection", variable=self.edge_detection_var).pack(side=tk.LEFT)

        # Pin shape selection
        pin_shape_frame = ttk.Frame(self.control_frame)
        pin_shape_frame.pack(fill=tk.X, pady=5)
        ttk.Label(pin_shape_frame, text="Pin Layout:").pack(side=tk.LEFT)
        self.pin_shape_var = tk.StringVar(value="circle")
        shapes = {"circle": "Circular", "square": "Square"}
        for shape, label in shapes.items():
            rb = ttk.Radiobutton(pin_shape_frame, text=label, value=shape, variable=self.pin_shape_var)
            rb.pack(side=tk.LEFT, padx=5)

        # Pin count slider
        pins_frame = ttk.Frame(self.control_frame)
        pins_frame.pack(fill=tk.X, pady=5)
        ttk.Label(pins_frame, text="Number of Pins:").pack(side=tk.LEFT)
        self.pins_var = tk.IntVar(value=self.num_pins)
        self.pins_entry = ttk.Entry(pins_frame, textvariable=self.pins_var, width=5)
        self.pins_entry.pack(side=tk.RIGHT)
        self.pins_scale = Scale(pins_frame, from_=50, to=400, orient=HORIZONTAL, 
                              variable=self.pins_var, length=150)
        self.pins_scale.pack(side=tk.RIGHT, padx=5)

        # Minimum pin distance slider
        min_dist_frame = ttk.Frame(self.control_frame)
        min_dist_frame.pack(fill=tk.X, pady=5)
        ttk.Label(min_dist_frame, text="Min Pin Distance:").pack(side=tk.LEFT)
        self.min_dist_var = tk.IntVar(value=self.min_pin_distance)
        self.min_dist_entry = ttk.Entry(min_dist_frame, textvariable=self.min_dist_var, width=5)
        self.min_dist_entry.pack(side=tk.RIGHT)
        self.min_dist_scale = Scale(min_dist_frame, from_=5, to=50, orient=HORIZONTAL, 
                                  variable=self.min_dist_var, length=150)
        self.min_dist_scale.pack(side=tk.RIGHT, padx=5)

        # Line count slider
        lines_frame = ttk.Frame(self.control_frame)
        lines_frame.pack(fill=tk.X, pady=5)
        ttk.Label(lines_frame, text="Max Lines:").pack(side=tk.LEFT)
        self.lines_var = tk.IntVar(value=self.num_lines)
        self.lines_entry = ttk.Entry(lines_frame, textvariable=self.lines_var, width=5)
        self.lines_entry.pack(side=tk.RIGHT)
        self.lines_scale = Scale(lines_frame, from_=500, to=5000, orient=HORIZONTAL, 
                               variable=self.lines_var, length=150)
        self.lines_scale.pack(side=tk.RIGHT, padx=5)

        # Image scale slider
        scale_frame = ttk.Frame(self.control_frame)
        scale_frame.pack(fill=tk.X, pady=5)
        ttk.Label(scale_frame, text="Image Scale:").pack(side=tk.LEFT)
        self.scale_var = tk.DoubleVar(value=1.0)
        self.scale_entry = ttk.Entry(scale_frame, textvariable=self.scale_var, width=5)
        self.scale_entry.pack(side=tk.RIGHT)
        self.scale_scale = Scale(scale_frame, from_=0.1, to=1.0, orient=HORIZONTAL,
                               variable=self.scale_var, length=150, resolution=0.1,
                               command=lambda x: self.apply_image_scale())
        self.scale_scale.pack(side=tk.RIGHT, padx=5)

        # Line style settings
        style_frame = ttk.LabelFrame(self.control_frame, text="Line Style")
        style_frame.pack(fill=tk.X, pady=5)

        # Line thickness
        thickness_frame = ttk.Frame(style_frame)
        thickness_frame.pack(fill=tk.X, pady=2)
        ttk.Label(thickness_frame, text="Thickness:").pack(side=tk.LEFT)
        self.line_thickness_var = tk.DoubleVar(value=1.0)
        self.thickness_scale = Scale(thickness_frame, from_=0.01, to=4.0, orient=HORIZONTAL,
                                   variable=self.line_thickness_var, length=150, resolution=0.01)
        self.thickness_scale.pack(side=tk.RIGHT, padx=5)

        # Line color
        color_frame = ttk.Frame(style_frame)
        color_frame.pack(fill=tk.X, pady=2)
        ttk.Label(color_frame, text="Color:").pack(side=tk.LEFT)
        self.line_color_var = tk.StringVar(value="black")
        colors = ["black", "red", "blue", "green"]
        for color in colors:
            rb = ttk.Radiobutton(color_frame, text=color.capitalize(), value=color,
                                variable=self.line_color_var)
            rb.pack(side=tk.LEFT, padx=5)

        # Add pin mode toggle button
        self.add_pin_button = ttk.Button(self.control_frame, text="Add Pin Mode", 
                                       command=self.toggle_add_pin_mode)
        self.add_pin_button.pack(fill=tk.X, pady=5)

        # Restricted area mode toggle button
        self.restrict_area_button = ttk.Button(self.control_frame, text="Restricted Area Mode", 
                                             command=self.toggle_restrict_area_mode)
        self.restrict_area_button.pack(fill=tk.X, pady=5)

        # Step 3: Generate
        ttk.Label(self.control_frame, text="Step 3: Generate", font=("Arial", 12, "bold")).pack(pady=(15, 5), anchor=tk.W)
        self.generate_button = ttk.Button(self.control_frame, text="Generate String Art", 
                                         command=self.start_generation_thread)
        self.generate_button.pack(fill=tk.X, pady=5)
        self.generate_button["state"] = "disabled"

        # Progress display
        self.progress_frame = ttk.Frame(self.control_frame)
        self.progress_frame.pack(fill=tk.X, pady=5)
        self.progress_bar = ttk.Progressbar(self.progress_frame, orient=tk.HORIZONTAL, length=100, mode='determinate')
        self.progress_bar.pack(fill=tk.X)
        self.progress_label = ttk.Label(self.progress_frame, text="")
        self.progress_label.pack(anchor=tk.W)

        # Step 4: Save Results
        ttk.Label(self.control_frame, text="Step 4: Save Results", font=("Arial", 12, "bold")).pack(pady=(15, 5), anchor=tk.W)
        self.save_button = ttk.Button(self.control_frame, text="Save Results", command=self.save_results)
        self.save_button.pack(fill=tk.X, pady=5)
        self.save_button["state"] = "disabled"

        # Print button
        self.print_button = ttk.Button(self.control_frame, text="Print Results", command=self.print_results)
        self.print_button.pack(fill=tk.X, pady=5)
        self.print_button["state"] = "disabled"

        # Theme toggle
        self.theme_button = ttk.Button(self.control_frame, text="Toggle Dark Mode", command=self.toggle_theme)
        self.theme_button.pack(fill=tk.X, pady=(20, 5))

        # Right visualization frame
        self.viz_frame = ttk.Frame(self.main_frame)
        self.viz_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Image preview and result tabs
        self.tab_control = ttk.Notebook(self.viz_frame)
        self.tab_control.pack(fill=tk.BOTH, expand=True)

        # Preview tab
        self.preview_tab = ttk.Frame(self.tab_control)
        self.tab_control.add(self.preview_tab, text="Image Preview")

        # Result tab
        self.result_tab = ttk.Frame(self.tab_control)
        self.tab_control.add(self.result_tab, text="String Art Result")

        # Pin layout tab
        self.pins_tab = ttk.Frame(self.tab_control)
        self.tab_control.add(self.pins_tab, text="Pin Layout")

        # Guide tab
        self.guide_tab = ttk.Frame(self.tab_control)
        self.tab_control.add(self.guide_tab, text="Construction Guide")

        # Initialize tab displays
        self.setup_preview_tab()
        self.setup_result_tab()
        self.setup_pins_tab()
        self.setup_guide_tab()

        # Status bar
        self.status_bar = ttk.Label(self.master, text="Please load an image", relief=tk.SUNKEN, anchor=tk.W)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
    
    def set_light_mode(self):
        """라이트 모드 스타일 설정"""
        self.dark_mode = False
        bg_color = "#f0f0f0"
        fg_color = "#333333"
        
        self.master.configure(background=bg_color)
        self.style.configure("TFrame", background=bg_color)
        self.style.configure("TLabel", background=bg_color, foreground=fg_color)
        self.style.configure("TButton", background=bg_color, foreground=fg_color)
        self.style.configure("TRadiobutton", background=bg_color, foreground=fg_color)
        self.style.configure("TNotebook", background=bg_color)
        self.style.configure("TNotebook.Tab", background=bg_color, foreground=fg_color)
    
    def set_dark_mode(self):
        """다크 모드 스타일 설정"""
        self.dark_mode = True
        bg_color = "#333333"
        fg_color = "#f0f0f0"
        
        self.master.configure(background=bg_color)
        self.style.configure("TFrame", background=bg_color)
        self.style.configure("TLabel", background=bg_color, foreground=fg_color)
        self.style.configure("TButton", background=bg_color, foreground=fg_color)
        self.style.configure("TRadiobutton", background=bg_color, foreground=fg_color) 
        self.style.configure("TNotebook", background=bg_color)
        self.style.configure("TNotebook.Tab", background=bg_color, foreground=fg_color)
    
    def toggle_theme(self):
        """테마 토글 (라이트/다크 모드)"""
        if self.dark_mode:
            self.set_light_mode()
        else:
            self.set_dark_mode()
        
        # 모든 그래프 업데이트
        if self.original_image is not None:
            self.update_preview_tab()
        if self.canvas is not None:
            self.update_result_tab()
        if self.pins:
            self.update_pins_tab()
        
    def setup_preview_tab(self):
        """미리보기 탭 설정"""
        fig = plt.Figure(figsize=(10, 6), dpi=100)
        self.preview_canvas = FigureCanvasTkAgg(fig, self.preview_tab)
        self.preview_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        self.preview_ax = fig.add_subplot(111)
        self.preview_ax.set_title("이미지를 불러와 주세요")
        self.preview_ax.axis('off')
        
        fig.tight_layout()
        self.preview_canvas.draw()
    
    def setup_result_tab(self):
        """결과 탭 설정"""
        fig = plt.Figure(figsize=(10, 6), dpi=100)
        self.result_canvas = FigureCanvasTkAgg(fig, self.result_tab)
        self.result_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        self.result_ax = fig.add_subplot(111)
        self.result_ax.set_title("스트링 아트 결과가 여기에 표시됩니다")
        self.result_ax.axis('off')
        
        fig.tight_layout()
        self.result_canvas.draw()
    
    def setup_pins_tab(self):
        """핀 배치도 탭 설정"""
        fig = plt.Figure(figsize=(10, 6), dpi=100)
        self.pins_canvas = FigureCanvasTkAgg(fig, self.pins_tab)
        self.pins_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        self.pins_ax = fig.add_subplot(111)
        self.pins_ax.set_title("핀 배치도")
        self.pins_ax.axis('off')
        
        fig.tight_layout()
        self.pins_canvas.draw()
    
    def setup_guide_tab(self):
        """제작 안내 탭 설정"""
        # 상단 안내 프레임
        guide_info_frame = ttk.Frame(self.guide_tab)
        guide_info_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Label(guide_info_frame, text="제작 안내", font=("Arial", 14, "bold")).pack(anchor=tk.W)
        ttk.Label(guide_info_frame, text="아래 목록에 따라 핀을 순서대로 연결하세요.").pack(anchor=tk.W, pady=(0, 10))
        
        # 연결 목록 프레임
        self.guide_frame = ttk.Frame(self.guide_tab)
        self.guide_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 스크롤바 추가
        scrollbar = ttk.Scrollbar(self.guide_frame)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 연결 목록 텍스트 박스
        self.guide_text = tk.Text(self.guide_frame, yscrollcommand=scrollbar.set)
        self.guide_text.pack(fill=tk.BOTH, expand=True)
        scrollbar.config(command=self.guide_text.yview)
        
        # 초기 안내 텍스트
        self.guide_text.insert(tk.END, "스트링 아트 생성 후 연결 순서가 여기에 표시됩니다.")
        self.guide_text.config(state=tk.DISABLED)
    
    def load_image_dialog(self):
        """이미지 파일 선택 대화상자"""
        filetypes = [
            ("이미지 파일", "*.jpg *.jpeg *.png *.bmp *.gif"),
            ("JPEG 파일", "*.jpg *.jpeg"),
            ("PNG 파일", "*.png"),
            ("모든 파일", "*.*")
        ]
        
        image_path = filedialog.askopenfilename(
            title="이미지 파일 선택",
            filetypes=filetypes
        )
        
        if image_path:
            self.image_path = image_path
            self.load_and_process_image()
            self.generate_button["state"] = "normal"
            self.status_bar.config(text=f"이미지를 불러왔습니다: {os.path.basename(image_path)}")
    
    def load_and_process_image(self):
        """Load and process image while maintaining aspect ratio"""
        try:
            # Load image
            img = cv2.imread(self.image_path)
            if img is None:
                raise ValueError(f"Cannot load image: {self.image_path}")
                
            # Convert to grayscale
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            
            # Get canvas size
            canvas_size = 800  # Fixed canvas size
            
            # Calculate scaling factor to fit image within canvas while maintaining aspect ratio
            h, w = gray.shape
            scale = min(canvas_size / w, canvas_size / h)
            
            # Calculate new dimensions
            new_w = int(w * scale)
            new_h = int(h * scale)
            
            # Resize image
            resized = cv2.resize(gray, (new_w, new_h))
            
            # Create a blank canvas
            self.original_image = np.ones((canvas_size, canvas_size), dtype=np.uint8) * 255
            
            # Calculate position to center the image
            x_offset = (canvas_size - new_w) // 2
            y_offset = (canvas_size - new_h) // 2
            
            # Place the resized image on the canvas
            self.original_image[y_offset:y_offset+new_h, x_offset:x_offset+new_w] = resized
            
            # Apply edge detection if enabled
            if self.edge_detection_var.get():
                # Blur to reduce noise
                blurred = cv2.GaussianBlur(self.original_image, (5, 5), 0)
                # Edge detection
                self.processed_image = cv2.Canny(blurred, 50, 150)
            else:
                self.processed_image = self.original_image.copy()
            
            # Create empty canvas for string art
            self.canvas = np.ones((canvas_size, canvas_size), dtype=np.uint8) * 255
            
            # Update preview
            self.update_preview_tab()
            
            # Generate pins
            self.generate_pins()
            
            # Update all tabs
            self.update_all_tabs()
            
            return True
            
        except Exception as e:
            self.status_bar.config(text=f"Error: {str(e)}")
            return False
    
    def apply_image_scale(self):
        """이미지 크기 조정 적용"""
        if self.original_image is None:
            return
            
        scale = self.scale_var.get()
        if scale == self.image_scale:
            return
            
        self.image_scale = scale
        
        # 이미지 크기 조정
        h, w = self.original_image.shape
        new_h = int(h * scale)
        new_w = int(w * scale)
        
        # 중앙 정렬을 위한 오프셋 계산
        offset_x = (w - new_w) // 2
        offset_y = (h - new_h) // 2
        
        # 크기 조정된 이미지 생성
        scaled = cv2.resize(self.original_image, (new_w, new_h))
        
        # 원본 크기의 빈 이미지 생성
        result = np.ones((h, w), dtype=np.uint8) * 255
        
        # 크기 조정된 이미지를 중앙에 배치
        result[offset_y:offset_y+new_h, offset_x:offset_x+new_w] = scaled
        
        self.original_image = result
        self.update_preview_tab()
    
    def update_preview_tab(self):
        """Update preview tab with original and processed images"""
        if self.original_image is None:
            return
        
        self.preview_ax.clear()
        
        # Display original image
        self.preview_ax.imshow(self.original_image, cmap='gray')
        self.preview_ax.set_title("Original Image")
        self.preview_ax.axis('off')
        
        # Display user-added pins
        for x, y in self.custom_pins:
            self.preview_ax.plot(x, y, 'bo', markersize=5)
        
        # Display restricted areas
        for x1, y1, x2, y2 in self.restricted_areas:
            rect = patches.Rectangle((x1, y1), x2-x1, y2-y1, 
                                   fill=True, color='red', alpha=0.3)
            self.preview_ax.add_patch(rect)
        
        # Show edge detection result if enabled
        if self.edge_detection_var.get() and self.processed_image is not None:
            small_ax = self.preview_ax.inset_axes([0.65, 0.65, 0.3, 0.3])
            small_ax.imshow(self.processed_image, cmap='gray')
            small_ax.set_title("Edge Detection")
            small_ax.axis('off')
        
        self.preview_canvas.draw()
    
    def update_result_tab(self):
        """Update result tab with string art"""
        if self.canvas is None:
            return
        
        self.result_ax.clear()
        
        # Display string art result
        self.result_ax.imshow(self.canvas, cmap='gray')
        self.result_ax.set_title(f"String Art Result (Pins: {len(self.pins)}, Lines: {len(self.lines)})")
        self.result_ax.axis('off')
        
        # Draw lines with current style settings
        if self.lines:
            color = self.line_color_var.get()
            thickness = self.line_thickness_var.get()
            for start_pin, end_pin in self.lines:
                self.result_ax.plot([self.pins[start_pin][0], self.pins[end_pin][0]],
                                  [self.pins[start_pin][1], self.pins[end_pin][1]],
                                  color=color, linewidth=thickness)
        
        self.result_canvas.draw()
    
    def update_pins_tab(self):
        """Update pin layout tab"""
        if not self.pins:
            return
        
        self.pins_ax.clear()
        
        # Display background image (faded)
        if self.original_image is not None:
            self.pins_ax.imshow(self.original_image, cmap='gray', alpha=0.2)
        
        # Draw pins
        pin_x = [p[0] for p in self.pins]
        pin_y = [p[1] for p in self.pins]
        
        # Draw regular pins in red
        self.pins_ax.plot(pin_x[:self.num_pins], pin_y[:self.num_pins], 'ro', markersize=3)
        
        # Draw custom pins in blue
        if self.custom_pins:
            custom_x = [p[0] for p in self.custom_pins]
            custom_y = [p[1] for p in self.custom_pins]
            self.pins_ax.plot(custom_x, custom_y, 'bo', markersize=5)
        
        # Show pin numbers (every 20 pins)
        for i in range(0, len(self.pins), 20):
            self.pins_ax.text(self.pins[i][0], self.pins[i][1], str(i+1), 
                            fontsize=8, color='blue', 
                            ha='center', va='center')
        
        # Draw pin layout border
        if self.pin_shape == "circle":
            circle = plt.Circle((400, 400), 390, fill=False, color='blue', linestyle='-', alpha=0.7)
            self.pins_ax.add_patch(circle)
        elif self.pin_shape == "square":
            square = patches.Rectangle((10, 10), 780, 780, fill=False, color='blue', linestyle='-', alpha=0.7)
            self.pins_ax.add_patch(square)
        
        self.pins_ax.set_title(f"Pin Layout (Total: {len(self.pins)})")
        self.pins_ax.axis('off')
        
        self.pins_canvas.draw()
    
    def update_guide_tab(self):
        """제작 안내 탭 업데이트"""
        if not self.lines:
            return
        
        # 텍스트 박스 업데이트
        self.guide_text.config(state=tk.NORMAL)
        self.guide_text.delete(1.0, tk.END)
        
        self.guide_text.insert(tk.END, f"스트링 아트 제작 지침\n")
        self.guide_text.insert(tk.END, f"====================\n\n")
        self.guide_text.insert(tk.END, f"핀 개수: {self.num_pins}\n")
        self.guide_text.insert(tk.END, f"선 개수: {len(self.lines)}\n\n")
        self.guide_text.insert(tk.END, "연결 순서:\n")
        
        for i, (start, end) in enumerate(self.lines):
            self.guide_text.insert(tk.END, f"{i+1}. 핀 #{start+1} → 핀 #{end+1}\n")
            
            # 20개 연결마다 구분선 추가
            if (i+1) % 20 == 0:
                self.guide_text.insert(tk.END, f"------------- {i+1}개 완료 -------------\n")
        
        self.guide_text.config(state=tk.DISABLED)
    
    def generate_pins(self):
        """핀 위치 생성"""
        self.pins = []
        self.num_pins = self.pins_var.get()
        
        if self.pin_shape == "circle":
            # 원형 배치
            r = 390  # 반지름 (800x800 이미지 기준)
            center = (400, 400)  # 중심점
            
            for i in range(self.num_pins):
                angle = 2 * np.pi * i / self.num_pins
                x = int(center[0] + r * np.cos(angle))
                y = int(center[1] + r * np.sin(angle))
                self.pins.append((x, y))
        
        elif self.pin_shape == "square":
            # 사각형 배치
            pins_per_side = self.num_pins // 4
            extra_pins = self.num_pins % 4
            
            # 상단 변
            for i in range(pins_per_side + (1 if extra_pins > 0 else 0)):
                x = 10 + int(i * 780 / (pins_per_side - 1)) if pins_per_side > 1 else 400
                y = 10
                self.pins.append((x, y))
            
            # 우측 변
            for i in range(pins_per_side + (1 if extra_pins > 1 else 0)):
                x = 790
                y = 10 + int(i * 780 / (pins_per_side - 1)) if pins_per_side > 1 else 400
                self.pins.append((x, y))
            
            # 하단 변
            for i in range(pins_per_side + (1 if extra_pins > 2 else 0)):
                x = 790 - int(i * 780 / (pins_per_side - 1)) if pins_per_side > 1 else 400
                y = 790
                self.pins.append((x, y))
            
            # 좌측 변
            for i in range(pins_per_side + (1 if extra_pins > 3 else 0)):
                x = 10
                y = 790 - int(i * 780 / (pins_per_side - 1)) if pins_per_side > 1 else 400
                self.pins.append((x, y))
        
        # 사용자 추가 핀 추가
        self.pins.extend(self.custom_pins)
        
        return self.pins
    
    def calculate_line_intensity(self, start_pin, end_pin):
        """두 핀 사이 직선의 어두운 정도 계산"""
        # 두 핀 사이 직선 좌표 생성
        num_points = 100
        xs = np.linspace(start_pin[0], end_pin[0], num_points, dtype=int)
        ys = np.linspace(start_pin[1], end_pin[1], num_points, dtype=int)
        
        # 직선을 따라 각 픽셀에서의 강도 계산
        intensities = []
        for x, y in zip(xs, ys):
            if 0 <= x < self.processed_image.shape[1] and 0 <= y < self.processed_image.shape[0]:
                intensities.append(self.processed_image[y, x])
        
        # 평균 강도 반환
        if intensities:
            return np.mean(intensities)
        else:
            return 0
    
    def generate_string_art(self):
        """스트링 아트 생성"""
        self.lines = []
        current_pin = 0
        min_distance = self.min_pin_distance  # 같은 핀이나 인접한 핀으로의 연결 방지
        max_lines = self.num_lines  # 최대 선 개수
        visited_lines = set()  # 이미 사용된 선 저장
        
        # 빈 캔버스 생성 (흰색 배경)
        self.canvas = np.ones((800, 800), dtype=np.uint8) * 255
        
        # 핀 위치 업데이트
        self.pin_shape = self.pin_shape_var.get()
        self.generate_pins()
        
        for i in range(max_lines):
            best_pin = -1
            best_intensity = -1
            
            # 모든 핀을 검사하여 가장 좋은 다음 핀 찾기
            for next_pin in range(len(self.pins)):
                # 자기 자신이나 인접한 핀은 건너뛰기
                if abs(next_pin - current_pin) < min_distance or \
                   abs(next_pin - current_pin) > len(self.pins) - min_distance:
                    continue
                
                # 이미 사용된 선이면 건너뛰기
                line_key = tuple(sorted([current_pin, next_pin]))
                if line_key in visited_lines:
                    continue
                
                # 제한 영역을 통과하는 선은 건너뛰기
                if self.is_line_in_restricted_area(self.pins[current_pin], self.pins[next_pin]):
                    continue
                
                # 두 핀 사이 직선의 강도 계산
                intensity = self.calculate_line_intensity(self.pins[current_pin], self.pins[next_pin])
                
                if intensity > best_intensity:
                    best_intensity = intensity
                    best_pin = next_pin
            
            # 적합한 핀을 찾았으면 선 추가
            if best_pin != -1:
                self.lines.append((current_pin, best_pin))
                visited_lines.add(tuple(sorted([current_pin, best_pin])))
                
                # 선 그리기
                cv2.line(self.canvas, self.pins[current_pin], self.pins[best_pin], 0, 1)
                
                # 다음 시작점 업데이트
                current_pin = best_pin
                
                # 진행 상황 업데이트 (10% 단위)
                progress_pct = (i + 1) / max_lines * 100
                if int(progress_pct) % 10 == 0:
                    self.progress = progress_pct
                    # 결과 탭 업데이트 (30줄마다)
                    if (i + 1) % 30 == 0:
                        self.update_ui()
            else:
                # 더 이상 선을 추가할 수 없으면 종료
                break
        
        # 최종 업데이트
        self.progress = 100
        self.is_generating = False
        self.update_ui()
        
        return self.canvas, self.lines
    
    def update_ui(self):
        """UI 업데이트 (스레드 안전하게)"""
        if not self.master:
            return
            
        self.master.after(0, self._update_ui_in_main_thread)
    
    def _update_ui_in_main_thread(self):
        """메인 스레드에서 UI 업데이트"""
        # 진행 상황 업데이트
        self.progress_bar["value"] = self.progress
        self.progress_label.config(text=f"진행 상황: {int(self.progress)}%")
        
        # 결과 탭 업데이트
        self.update_result_tab()
        
        # 생성이 완료된 경우
        if self.progress >= 100:
            self.generate_button["state"] = "normal"
            self.save_button["state"] = "normal"
            self.status_bar.config(text=f"스트링 아트 생성 완료: {len(self.lines)}개의 선 생성됨")
            
            # 핀 배치도 업데이트
            self.update_pins_tab()
            
            # 제작 안내 업데이트
            self.update_guide_tab()
            
            # 결과 탭으로 전환
            self.tab_control.select(1)  # 결과 탭 (인덱스 1)
    
    def start_generation_thread(self):
        """스트링 아트 생성 스레드 시작"""
        if self.original_image is None:
            self.status_bar.config(text="먼저 이미지를 불러와주세요.")
            return
        
        if self.is_generating:
            self.status_bar.config(text="이미 생성 중입니다. 완료될 때까지 기다려주세요.")
            return
        
        # 설정 값 업데이트
        self.num_pins = self.pins_var.get()
        self.num_lines = self.lines_var.get()
        self.min_pin_distance = self.min_dist_var.get()
        self.pin_shape = self.pin_shape_var.get()
        
        # UI 상태 업데이트
        self.generate_button["state"] = "disabled"
        self.save_button["state"] = "disabled"
        self.is_generating = True
        self.progress = 0
        self.progress_bar["value"] = 0
        self.progress_label.config(text="생성 준비 중...")
        self.status_bar.config(text="스트링 아트 생성 중...")
        
        # 스레드 시작
        thread = threading.Thread(target=self.generate_string_art)
        thread.daemon = True
        thread.start()
    
    def save_results(self):
        """결과물 저장"""
        if not self.lines or not self.pins:
            self.status_bar.config(text="저장할 결과가 없습니다.")
            return
        
        # 저장 폴더 선택
        folder = filedialog.askdirectory(title="결과를 저장할 폴더 선택")
        if not folder:
            return
        
        try:
            # 폴더 생성 (이미 있는 경우 무시)
            folder_path = os.path.join(folder, "string_art_result")
            os.makedirs(folder_path, exist_ok=True)
            
            # 1. 스트링 아트 결과 이미지 저장
            result_img_path = os.path.join(folder_path, "string_art_result.png")
            cv2.imwrite(result_img_path, self.canvas)
            
            # 2. 핀 배치도 이미지 저장
            fig = plt.Figure(figsize=(10, 10), dpi=150)
            ax = fig.add_subplot(111)
            
            # 핀 배치 그리기
            pin_x = [p[0] for p in self.pins]
            pin_y = [p[1] for p in self.pins]
            ax.plot(pin_x, pin_y, 'ro', markersize=2)
            
            # 핀 번호 표시 (10개 간격)
            for i in range(0, len(self.pins), 10):
                ax.text(self.pins[i][0], self.pins[i][1], str(i+1), 
                       fontsize=8, color='blue', 
                       ha='center', va='center')
            
            # 핀 배치 형태 테두리 그리기
            if self.pin_shape == "circle":
                circle = plt.Circle((400, 400), 390, fill=False, color='blue', linestyle='-', alpha=0.7)
                ax.add_patch(circle)
            elif self.pin_shape == "square":
                square = patches.Rectangle((10, 10), 780, 780, fill=False, color='blue', linestyle='-', alpha=0.7)
                ax.add_patch(square)
            
            ax.set_title(f"핀 배치도 (총 {len(self.pins)}개)")
            ax.axis('off')
            ax.set_xlim(0, 800)
            ax.set_ylim(0, 800)
            
            pins_img_path = os.path.join(folder_path, "pin_layout.png")
            fig.tight_layout()
            fig.savefig(pins_img_path, dpi=300, bbox_inches='tight')
            
            # 3. 제작 지침 저장
            instructions_path = os.path.join(folder_path, "instructions.txt")
            with open(instructions_path, 'w', encoding='utf-8') as f:
                f.write(f"스트링 아트 제작 지침\n")
                f.write(f"====================\n\n")
                f.write(f"핀 개수: {self.num_pins}\n")
                f.write(f"선 개수: {len(self.lines)}\n\n")
                f.write("연결 순서:\n")
                
                for i, (start, end) in enumerate(self.lines):
                    f.write(f"{i+1}. 핀 #{start+1} → 핀 #{end+1}\n")
                    
                    # 20개 연결마다 구분선 추가
                    if (i+1) % 20 == 0:
                        f.write(f"------------- {i+1}개 완료 -------------\n")
            
            # 4. 핀 좌표 및 연결 정보 JSON 저장 (후속 작업을 위함)
            data = {
                "pins": self.pins,
                "lines": self.lines,
                "settings": {
                    "num_pins": self.num_pins,
                    "min_pin_distance": self.min_pin_distance,
                    "num_lines": self.num_lines,
                    "pin_shape": self.pin_shape
                }
            }
            
            json_path = os.path.join(folder_path, "string_art_data.json")
            with open(json_path, 'w') as f:
                json.dump(data, f)
            
            # 5. 단계별 이미지 생성 (선 20개마다 이미지 저장)
            steps_folder = os.path.join(folder_path, "steps")
            os.makedirs(steps_folder, exist_ok=True)
            
            if len(self.lines) > 0:
                # 주요 단계에서만 이미지 저장 (선이 많을 수 있으므로)
                step_counts = min(20, len(self.lines))  # 최대 20개 단계 이미지
                step_size = max(1, len(self.lines) // step_counts)
                
                for step in range(0, len(self.lines), step_size):
                    if step == 0:
                        continue  # 첫 단계는 빈 이미지이므로 건너뜀
                        
                    # 해당 단계까지의 이미지 생성
                    step_canvas = np.ones((800, 800), dtype=np.uint8) * 255
                    
                    for i in range(min(step, len(self.lines))):
                        start_pin, end_pin = self.lines[i]
                        cv2.line(step_canvas, self.pins[start_pin], self.pins[end_pin], 0, 1)
                    
                    # 이미지 저장
                    step_img_path = os.path.join(steps_folder, f"step_{step}.png")
                    cv2.imwrite(step_img_path, step_canvas)
            
            self.status_bar.config(text=f"결과가 저장되었습니다: {folder_path}")
            
        except Exception as e:
            self.status_bar.config(text=f"저장 중 오류 발생: {str(e)}")

    def is_line_in_restricted_area(self, start_pin, end_pin):
        """선이 제한 영역을 통과하는지 확인"""
        for x1, y1, x2, y2 in self.restricted_areas:
            # 선분과 사각형의 교차 여부 확인
            if self.line_intersects_rect(start_pin, end_pin, (x1, y1, x2, y2)):
                return True
        return False

    def line_intersects_rect(self, start, end, rect):
        """선분과 사각형의 교차 여부 확인"""
        x1, y1, x2, y2 = rect
        
        # 선분의 양 끝점
        x3, y3 = start
        x4, y4 = end
        
        # 선분이 사각형 내부에 있는지 확인
        if (x1 <= x3 <= x2 and y1 <= y3 <= y2) or \
           (x1 <= x4 <= x2 and y1 <= y4 <= y2):
            return True
        
        # 선분이 사각형의 변과 교차하는지 확인
        lines = [
            ((x1, y1), (x2, y1)),  # 상단
            ((x2, y1), (x2, y2)),  # 우측
            ((x2, y2), (x1, y2)),  # 하단
            ((x1, y2), (x1, y1))   # 좌측
        ]
        
        for line in lines:
            if self.line_intersects_line((x3, y3), (x4, y4), line[0], line[1]):
                return True
        
        return False

    def line_intersects_line(self, p1, p2, p3, p4):
        """두 선분의 교차 여부 확인"""
        def ccw(A, B, C):
            return (C[1]-A[1]) * (B[0]-A[0]) > (B[1]-A[1]) * (C[0]-A[0])
        
        return ccw(p1, p3, p4) != ccw(p2, p3, p4) and \
               ccw(p1, p2, p3) != ccw(p1, p2, p4)

    def toggle_add_pin_mode(self):
        """Toggle add pin mode"""
        self.add_pin_mode = not self.add_pin_mode
        self.restrict_area_mode = False
        
        if self.add_pin_mode:
            self.add_pin_button.configure(text="Add Pin Mode (Active)")
            self.restrict_area_button.configure(text="Restricted Area Mode")
            self.preview_canvas.mpl_connect('button_press_event', self.on_canvas_click)
            self.preview_canvas.mpl_connect('motion_notify_event', self.on_canvas_motion)
        else:
            self.add_pin_button.configure(text="Add Pin Mode")
            self.preview_canvas.mpl_disconnect('button_press_event')
            self.preview_canvas.mpl_disconnect('motion_notify_event')
            self.update_preview_tab()

    def toggle_restrict_area_mode(self):
        """Toggle restricted area mode"""
        self.restrict_area_mode = not self.restrict_area_mode
        self.add_pin_mode = False
        
        if self.restrict_area_mode:
            self.restrict_area_button.configure(text="Restricted Area Mode (Active)")
            self.add_pin_button.configure(text="Add Pin Mode")
            self.preview_canvas.mpl_connect('button_press_event', self.on_restrict_area_click)
            self.preview_canvas.mpl_connect('motion_notify_event', self.on_canvas_motion)
        else:
            self.restrict_area_button.configure(text="Restricted Area Mode")
            self.preview_canvas.mpl_disconnect('button_press_event')
            self.preview_canvas.mpl_disconnect('motion_notify_event')
            self.update_preview_tab()

    def on_canvas_click(self, event):
        """Handle canvas click events for pin addition"""
        if event.xdata is None or event.ydata is None:
            return
            
        x, y = int(event.xdata), int(event.ydata)
        
        # Add pin
        self.custom_pins.append((x, y))
        
        # Update pin layout
        self.update_pins_tab()
        
        # Update status bar
        self.status_bar.config(text=f"Pin added: ({x}, {y})")

    def on_canvas_motion(self, event):
        """Handle canvas motion events for preview"""
        if event.xdata is None or event.ydata is None:
            return
            
        x, y = int(event.xdata), int(event.ydata)
        
        # Clear previous preview
        self.preview_ax.lines = [line for line in self.preview_ax.lines if not hasattr(line, 'is_preview')]
        self.preview_ax.patches = [patch for patch in self.preview_ax.patches if not hasattr(patch, 'is_preview')]
        
        if self.add_pin_mode:
            # Show pin preview
            preview_pin = self.preview_ax.plot(x, y, 'go', markersize=8, alpha=0.5)[0]
            preview_pin.is_preview = True
        elif self.restrict_area_mode:
            # Show restricted area preview
            preview_rect = patches.Rectangle((x-5, y-5), 10, 10, 
                                           fill=True, color='white', alpha=0.3)
            preview_rect.is_preview = True
            self.preview_ax.add_patch(preview_rect)
        
        self.preview_canvas.draw()

    def on_restrict_area_click(self, event):
        """Handle canvas click events for restricted area addition"""
        if event.xdata is None or event.ydata is None:
            return
            
        x, y = int(event.xdata), int(event.ydata)
        
        # Add restricted area (10x10 pixel area)
        self.restricted_areas.append((x-5, y-5, x+5, y+5))
        
        # Update preview
        self.update_preview_tab()
        
        # Update status bar
        self.status_bar.config(text=f"Restricted area added: ({x}, {y})")

    def update_all_tabs(self):
        """Update all visualization tabs"""
        self.update_preview_tab()
        self.update_result_tab()
        self.update_pins_tab()
        if self.lines:
            self.update_guide_tab()

    def print_results(self):
        """Print the current view (preview, result, or pin layout)"""
        try:
            # Get current tab
            current_tab = self.tab_control.select()
            tab_name = self.tab_control.tab(current_tab, "text")
            
            # Create a temporary figure for printing
            fig = plt.Figure(figsize=(10, 10), dpi=300)
            ax = fig.add_subplot(111)
            
            if tab_name == "Image Preview":
                ax.imshow(self.original_image, cmap='gray')
                ax.set_title("Image Preview")
            elif tab_name == "String Art Result":
                ax.imshow(self.canvas, cmap='gray')
                ax.set_title("String Art Result")
                # Draw lines with current style
                if self.lines:
                    color = self.line_color_var.get()
                    thickness = self.line_thickness_var.get()
                    for start_pin, end_pin in self.lines:
                        ax.plot([self.pins[start_pin][0], self.pins[end_pin][0]],
                               [self.pins[start_pin][1], self.pins[end_pin][1]],
                               color=color, linewidth=thickness)
            elif tab_name == "Pin Layout":
                ax.imshow(self.original_image, cmap='gray', alpha=0.2)
                # Draw pins
                pin_x = [p[0] for p in self.pins]
                pin_y = [p[1] for p in self.pins]
                ax.plot(pin_x, pin_y, 'ro', markersize=3)
                # Draw custom pins
                if self.custom_pins:
                    custom_x = [p[0] for p in self.custom_pins]
                    custom_y = [p[1] for p in self.custom_pins]
                    ax.plot(custom_x, custom_y, 'bo', markersize=5)
                ax.set_title("Pin Layout")
            
            ax.axis('off')
            fig.tight_layout()
            
            # Print the figure
            fig.canvas.print_figure("temp_print.png")
            os.startfile("temp_print.png", "print")
            
            # Clean up temporary file
            def cleanup():
                time.sleep(5)  # Wait for print dialog to close
                try:
                    os.remove("temp_print.png")
                except:
                    pass
            
            threading.Thread(target=cleanup, daemon=True).start()
            
        except Exception as e:
            self.status_bar.config(text=f"Print error: {str(e)}")

def main():
    root = tk.Tk()
    app = StringArtGenerator(root)
    root.mainloop()

if __name__ == "__main__":
    main()