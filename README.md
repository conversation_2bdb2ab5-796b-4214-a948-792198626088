# Photo GPS & Time Grouper

A tool to group photos based on time and GPS information.

## Features

- **Multiple Photo Selection**: Select multiple photos via file dialog or load an entire folder
- **Automatic Grouping**: Group photos by time and/or GPS location
- **Configurable Thresholds**: Set custom time and distance thresholds for grouping
- **Flexible Output**: Choose to copy or move photos to group folders
- **Modern UI**: Clean and intuitive interface

## Requirements

- Python 3.6 or higher
- Required Python packages:
  - tkinter (included with most Python installations)
  - Pillow (PIL Fork)

## Installation

1. Make sure you have Python installed
2. Install required packages:
   ```
   pip install pillow
   ```
3. Download the application files

## Usage

1. Run the application:
   ```
   python photo_gps_time_grouper.py
   ```

2. **Loading Photos**:
   - Click "Load Photos" to select multiple photos
   - Click "Load Folder" to load all photos from a folder (including subfolders)

3. **Configure Settings**:
   - Click "Settings" to open the settings dialog
   - Enable/disable time-based grouping
   - Set time threshold in minutes
   - Enable/disable GPS-based grouping
   - Set distance threshold in kilometers
   - Choose whether to copy or move photos when saving

4. **Process Photos**:
   - Click "Group Photos" to create groups based on your settings
   - The application will switch to the Groups tab to show results

5. **Save Results**:
   - Click "Save to Folder" on any group to save just that group
   - Click "Save All Groups" to save all groups at once
   - Choose a destination folder when prompted

## How Grouping Works

- **Time-based grouping**: Photos are grouped together if they were taken within the specified time threshold of each other
- **GPS-based grouping**: Photos are grouped together if they were taken within the specified distance of each other
- **Combined grouping**: When both options are enabled, photos must satisfy both criteria to be in the same group

## Tips

- For vacation photos, try a time threshold of 30-60 minutes and a distance threshold of 1-5 kilometers
- For event photos, use a smaller time threshold (5-15 minutes) and a smaller distance threshold (0.1-0.5 kilometers)
- Use the "Copy" option first to test grouping before using "Move"
- If you have photos without GPS data, they will be grouped by time only

## Troubleshooting

- If no groups are created, try adjusting the thresholds or check if your photos have the necessary metadata
- If photos are missing time data, they will be added to the end of the sequence
- If photos are missing GPS data, distance-based grouping will be skipped for those photos

## License

This software is provided as-is with no warranty. Feel free to modify and distribute as needed.
