import sys
import os
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import numpy as np
import cv2
from PIL import Image, ImageTk
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from pathlib import Path
import datetime
import threading
from scipy import ndimage
import math

class StringArtGenerator:
    def __init__(self, master):
        self.master = master
        self.master.title("스트링 아트 생성기 (String Art Generator)")
        self.master.geometry("1200x800")
        self.master.minsize(1000, 700)
        
        # 스타일 설정
        self.style = ttk.Style()
        self.style.theme_use('clam')
        
        # 다크 모드 기본 설정
        self.dark_mode = True
        
        # 변수 초기화
        self.original_image = None
        self.edge_image = None
        self.string_art = None
        self.pin_positions = None
        self.pin_connections = []
        self.progress = 0
        self.is_generating = False
        self.user_restricted_areas = []
        self.user_added_pins = []
        
        # 메인 프레임 생성
        self.main_frame = ttk.Frame(self.master, padding="10")
        self.main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 좌측 패널 (설정)
        self.left_panel = ttk.Frame(self.main_frame, width=300)
        self.left_panel.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        
        # 우측 패널 (미리보기)
        self.right_panel = ttk.Frame(self.main_frame)
        self.right_panel.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        # 좌측 패널 위젯 생성
        self.create_left_panel_widgets()
        
        # 우측 패널 위젯 생성
        self.create_right_panel_widgets()
        
        # 상태 바
        self.status_bar = ttk.Frame(self.master)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
        
        self.status_label = ttk.Label(self.status_bar, text="준비됨", anchor=tk.W)
        self.status_label.pack(side=tk.LEFT, padx=5)
        
        self.progress_bar = ttk.Progressbar(self.status_bar, length=200, mode='determinate')
        self.progress_bar.pack(side=tk.RIGHT, padx=5, pady=2)
        
        # 테마 변경 버튼
        self.theme_button = ttk.Button(self.status_bar, text="라이트 모드", command=self.toggle_theme)
        self.theme_button.pack(side=tk.RIGHT, padx=5)
        
        # 테마 업데이트
        self.update_theme()
        
    def update_theme(self):
        if self.dark_mode:
            # 다크 모드
            bg_color = "#2E2E2E"
            fg_color = "#FFFFFF"
            button_bg = "#3D3D3D"
            frame_bg = "#383838"
            self.theme_button.config(text="라이트 모드")
        else:
            # 라이트 모드
            bg_color = "#F0F0F0"
            fg_color = "#000000"
            button_bg = "#E0E0E0"
            frame_bg = "#F5F5F5"
            self.theme_button.config(text="다크 모드")
        
        # 스타일 적용
        self.style.configure('TFrame', background=frame_bg)
        self.style.configure('TLabel', background=frame_bg, foreground=fg_color)
        self.style.configure('TButton', background=button_bg, foreground=fg_color)
        self.style.configure('TCheckbutton', background=frame_bg, foreground=fg_color)
        self.style.configure('TRadiobutton', background=frame_bg, foreground=fg_color)
        self.style.configure('TScale', background=frame_bg)
        self.style.configure('TNotebook', background=bg_color)
        self.style.configure('TNotebook.Tab', background=button_bg, foreground=fg_color)
        
        # 배경색 업데이트
        self.master.configure(background=bg_color)
        
        # 모든 위젯 업데이트
        for widget in self.master.winfo_children():
            try:
                widget.configure(background=bg_color)
                if hasattr(widget, 'winfo_children'):
                    for child in widget.winfo_children():
                        try:
                            child.configure(background=frame_bg)
                        except:
                            pass
            except:
                pass
        
        # Tab 스타일 설정
        self.style.map('TNotebook.Tab', background=[('selected', button_bg)])
        
    def toggle_theme(self):
        self.dark_mode = not self.dark_mode
        self.update_theme()
    
    def create_left_panel_widgets(self):
        # 제목
        title_label = ttk.Label(self.left_panel, text="스트링 아트 생성기", font=("Helvetica", 16, "bold"))
        title_label.pack(pady=(0, 10), fill=tk.X)
        
        # 이미지 불러오기 버튼
        self.load_button = ttk.Button(self.left_panel, text="이미지 불러오기", command=self.load_image)
        self.load_button.pack(fill=tk.X, pady=5)
        
        # 구분선
        separator1 = ttk.Separator(self.left_panel, orient='horizontal')
        separator1.pack(fill=tk.X, pady=10)
        
        # 핀 설정 프레임
        pin_frame = ttk.LabelFrame(self.left_panel, text="핀 설정")
        pin_frame.pack(fill=tk.X, pady=5)
        
        # 핀 형태 선택
        self.pin_shape_var = tk.StringVar(value="원형")
        ttk.Label(pin_frame, text="핀 배치 형태:").pack(anchor=tk.W, padx=5, pady=2)
        
        shape_frame = ttk.Frame(pin_frame)
        shape_frame.pack(fill=tk.X, padx=5, pady=2)
        
        ttk.Radiobutton(shape_frame, text="원형", variable=self.pin_shape_var, value="원형").pack(side=tk.LEFT)
        ttk.Radiobutton(shape_frame, text="사각형", variable=self.pin_shape_var, value="사각형").pack(side=tk.LEFT)
        
        # 핀 개수
        ttk.Label(pin_frame, text="핀 개수:").pack(anchor=tk.W, padx=5, pady=2)
        
        self.pin_count_var = tk.IntVar(value=200)
        pin_count_scale = ttk.Scale(pin_frame, from_=50, to=400, variable=self.pin_count_var, orient=tk.HORIZONTAL)
        pin_count_scale.pack(fill=tk.X, padx=5, pady=2)
        
        pin_count_frame = ttk.Frame(pin_frame)
        pin_count_frame.pack(fill=tk.X, padx=5)
        
        ttk.Label(pin_count_frame, text="50").pack(side=tk.LEFT)
        self.pin_count_label = ttk.Label(pin_count_frame, text="200")
        self.pin_count_label.pack(expand=True)
        ttk.Label(pin_count_frame, text="400").pack(side=tk.RIGHT)
        
        # 핀 개수 변수 트레이싱
        self.pin_count_var.trace_add("write", self.update_pin_count_label)
        
        # 최소 핀 간격
        ttk.Label(pin_frame, text="최소 핀 간격:").pack(anchor=tk.W, padx=5, pady=2)
        
        self.min_pin_gap_var = tk.IntVar(value=20)
        min_gap_scale = ttk.Scale(pin_frame, from_=1, to=100, variable=self.min_pin_gap_var, orient=tk.HORIZONTAL)
        min_gap_scale.pack(fill=tk.X, padx=5, pady=2)
        
        min_gap_frame = ttk.Frame(pin_frame)
        min_gap_frame.pack(fill=tk.X, padx=5)
        
        ttk.Label(min_gap_frame, text="1").pack(side=tk.LEFT)
        self.min_gap_label = ttk.Label(min_gap_frame, text="20")
        self.min_gap_label.pack(expand=True)
        ttk.Label(min_gap_frame, text="100").pack(side=tk.RIGHT)
        
        # 최소 핀 간격 변수 트레이싱
        self.min_pin_gap_var.trace_add("write", self.update_min_gap_label)
        
        # 구분선
        separator2 = ttk.Separator(self.left_panel, orient='horizontal')
        separator2.pack(fill=tk.X, pady=10)
        
        # 실 설정 프레임
        string_frame = ttk.LabelFrame(self.left_panel, text="실 설정")
        string_frame.pack(fill=tk.X, pady=5)
        
        # 실 개수
        ttk.Label(string_frame, text="최대 실 개수:").pack(anchor=tk.W, padx=5, pady=2)
        
        self.string_count_var = tk.IntVar(value=3000)
        string_count_scale = ttk.Scale(string_frame, from_=500, to=10000, variable=self.string_count_var, orient=tk.HORIZONTAL)
        string_count_scale.pack(fill=tk.X, padx=5, pady=2)
        
        string_count_frame = ttk.Frame(string_frame)
        string_count_frame.pack(fill=tk.X, padx=5)
        
        ttk.Label(string_count_frame, text="500").pack(side=tk.LEFT)
        self.string_count_label = ttk.Label(string_count_frame, text="3000")
        self.string_count_label.pack(expand=True)
        ttk.Label(string_count_frame, text="10000").pack(side=tk.RIGHT)
        
        # 실 개수 변수 트레이싱
        self.string_count_var.trace_add("write", self.update_string_count_label)
        
        # 실 검은색 선택
        self.string_color_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(string_frame, text="검은색 실 사용 (흰색 배경)", variable=self.string_color_var).pack(anchor=tk.W, padx=5, pady=5)
        
        # 구분선
        separator3 = ttk.Separator(self.left_panel, orient='horizontal')
        separator3.pack(fill=tk.X, pady=10)
        
        # 추가 설정 프레임
        advanced_frame = ttk.LabelFrame(self.left_panel, text="추가 설정")
        advanced_frame.pack(fill=tk.X, pady=5)
        
        # 이미지 대비 조정
        ttk.Label(advanced_frame, text="이미지 대비:").pack(anchor=tk.W, padx=5, pady=2)
        
        self.image_contrast_var = tk.DoubleVar(value=1.0)
        contrast_scale = ttk.Scale(advanced_frame, from_=0.5, to=2.0, variable=self.image_contrast_var, orient=tk.HORIZONTAL)
        contrast_scale.pack(fill=tk.X, padx=5, pady=2)
        
        contrast_frame = ttk.Frame(advanced_frame)
        contrast_frame.pack(fill=tk.X, padx=5)
        
        ttk.Label(contrast_frame, text="0.5").pack(side=tk.LEFT)
        self.contrast_label = ttk.Label(contrast_frame, text="1.0")
        self.contrast_label.pack(expand=True)
        ttk.Label(contrast_frame, text="2.0").pack(side=tk.RIGHT)
        
        # 이미지 대비 변수 트레이싱
        self.image_contrast_var.trace_add("write", self.update_contrast_label)
        
        # 자동 핀 최적화
        self.auto_optimize_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(advanced_frame, text="자동 핀 최적화", variable=self.auto_optimize_var).pack(anchor=tk.W, padx=5, pady=5)
        
        # 구분선
        separator4 = ttk.Separator(self.left_panel, orient='horizontal')
        separator4.pack(fill=tk.X, pady=10)
        
        # 생성 버튼
        self.generate_button = ttk.Button(self.left_panel, text="스트링 아트 생성", command=self.generate_string_art)
        self.generate_button.pack(fill=tk.X, pady=5)
        self.generate_button.config(state=tk.DISABLED)
        
        # 저장 버튼
        self.save_button = ttk.Button(self.left_panel, text="결과 저장", command=self.save_results)
        self.save_button.pack(fill=tk.X, pady=5)
        self.save_button.config(state=tk.DISABLED)
        
        # 초기화 버튼
        self.reset_button = ttk.Button(self.left_panel, text="초기화", command=self.reset)
        self.reset_button.pack(fill=tk.X, pady=5)
    
    def create_right_panel_widgets(self):
        # 노트북 (탭) 생성
        self.notebook = ttk.Notebook(self.right_panel)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # 이미지 미리보기 탭
        self.preview_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.preview_tab, text="이미지 미리보기")
        
        # 스트링 아트 탭
        self.result_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.result_tab, text="스트링 아트")
        
        # 핀 배치도 탭
        self.pins_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.pins_tab, text="핀 배치도")
        
        # 제작 안내 탭
        self.guide_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.guide_tab, text="제작 안내")
        
        # 이미지 미리보기 탭 설정
        self.preview_frame = ttk.Frame(self.preview_tab)
        self.preview_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        self.original_image_label = ttk.Label(self.preview_frame, text="원본 이미지를 불러와주세요")
        self.original_image_label.pack(fill=tk.BOTH, expand=True)
        
        # 원본 이미지 클릭 이벤트 연결 (제한 구역 설정용)
        self.original_image_label.bind("<Button-1>", self.on_image_click)
        self.original_image_label.bind("<B1-Motion>", self.on_image_drag)
        self.original_image_label.bind("<ButtonRelease-1>", self.on_image_release)
        
        # 영역 선택을 위한 변수
        self.start_x = None
        self.start_y = None
        self.current_rectangle = None
        self.drawing = False
        
        # 스트링 아트 결과 탭 설정
        self.result_frame = ttk.Frame(self.result_tab)
        self.result_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        self.result_label = ttk.Label(self.result_frame, text="스트링 아트 결과가 여기에 표시됩니다")
        self.result_label.pack(fill=tk.BOTH, expand=True)
        
        # 핀 배치도 탭 설정
        self.pins_frame = ttk.Frame(self.pins_tab)
        self.pins_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        self.pins_canvas = tk.Canvas(self.pins_frame, bg="white" if not self.dark_mode else "#333333")
        self.pins_canvas.pack(fill=tk.BOTH, expand=True)
        
        # 핀 추가 버튼
        self.add_pin_button = ttk.Button(self.pins_frame, text="핀 추가 모드", command=self.toggle_add_pin_mode)
        self.add_pin_button.pack(side=tk.BOTTOM, pady=5)
        
        # 핀 추가 모드 변수
        self.add_pin_mode = False
        
        # 핀 캔버스 클릭 이벤트 연결
        self.pins_canvas.bind("<Button-1>", self.on_canvas_click)
        
        # 제작 안내 탭 설정
        self.guide_frame = ttk.Frame(self.guide_tab)
        self.guide_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 스크롤 가능한 텍스트 영역
        self.guide_text = tk.Text(self.guide_frame, wrap=tk.WORD, height=20, width=50)
        self.guide_text.pack(fill=tk.BOTH, expand=True, side=tk.LEFT)
        
        guide_scrollbar = ttk.Scrollbar(self.guide_frame, command=self.guide_text.yview)
        guide_scrollbar.pack(fill=tk.Y, side=tk.RIGHT)
        
        self.guide_text.config(yscrollcommand=guide_scrollbar.set)
        self.guide_text.insert(tk.END, "제작 안내는 스트링 아트 생성 후 표시됩니다.")
        self.guide_text.config(state=tk.DISABLED)
    
    def update_pin_count_label(self, *args):
        self.pin_count_label.config(text=str(self.pin_count_var.get()))
    
    def update_min_gap_label(self, *args):
        self.min_gap_label.config(text=str(self.min_pin_gap_var.get()))
    
    def update_string_count_label(self, *args):
        self.string_count_label.config(text=str(self.string_count_var.get()))
    
    def update_contrast_label(self, *args):
        self.contrast_label.config(text=f"{self.image_contrast_var.get():.1f}")
    
    def toggle_add_pin_mode(self):
        self.add_pin_mode = not self.add_pin_mode
        if self.add_pin_mode:
            self.add_pin_button.config(text="핀 추가 모드 종료")
            self.status_label.config(text="핀 추가 모드: 캔버스를 클릭하여 핀을 추가하세요")
        else:
            self.add_pin_button.config(text="핀 추가 모드")
            self.status_label.config(text="준비됨")
    
    def on_canvas_click(self, event):
        if self.add_pin_mode and self.pin_positions is not None:
            # 핀 추가 모드일 때만 작동
            x, y = event.x, event.y
            
            # 캔버스 상에서 클릭한 위치를 핀 위치로 변환
            self.user_added_pins.append((x, y))
            
            # 핀 그리기
            self.pins_canvas.create_oval(x-3, y-3, x+3, y+3, fill="red", outline="red")
            
            # 핀 번호 표시
            pin_number = len(self.pin_positions) + len(self.user_added_pins)
            self.pins_canvas.create_text(x+5, y-5, text=str(pin_number), fill="red")
            
            self.status_label.config(text=f"핀 추가됨: 총 {pin_number}개")
    
    def on_image_click(self, event):
        if self.original_image is not None:
            self.start_x = event.x
            self.start_y = event.y
            self.drawing = True
            
            # 캔버스 생성 (아직 없는 경우)
            if not hasattr(self, 'overlay_canvas'):
                self.overlay_canvas = tk.Canvas(self.original_image_label, highlightthickness=0)
                self.overlay_canvas.place(x=0, y=0, relwidth=1, relheight=1)
            
            # 사각형 그리기 시작
            self.current_rectangle = self.overlay_canvas.create_rectangle(
                self.start_x, self.start_y, self.start_x, self.start_y,
                outline="red", width=2, dash=(5, 5), fill="red", stipple="gray50"
            )
    
    def on_image_drag(self, event):
        if self.drawing and self.current_rectangle:
            current_x = event.x
            current_y = event.y
            
            # 사각형 업데이트
            self.overlay_canvas.coords(self.current_rectangle, self.start_x, self.start_y, current_x, current_y)
    
    def on_image_release(self, event):
        if self.drawing:
            self.drawing = False
            end_x = event.x
            end_y = event.y
            
            # 최소 크기 확인
            if abs(end_x - self.start_x) > 10 and abs(end_y - self.start_y) > 10:
                # 제한 구역 저장
                x1 = min(self.start_x, end_x)
                y1 = min(self.start_y, end_y)
                x2 = max(self.start_x, end_x)
                y2 = max(self.start_y, end_y)
                
                # 이미지 크기에 맞게 좌표 변환
                if hasattr(self, 'display_image_size'):
                    img_width, img_height = self.display_image_size
                    img_scale_x = self.original_image.shape[1] / img_width
                    img_scale_y = self.original_image.shape[0] / img_height
                    
                    x1 = int(x1 * img_scale_x)
                    y1 = int(y1 * img_scale_y)
                    x2 = int(x2 * img_scale_x)
                    y2 = int(y2 * img_scale_y)
                
                self.user_restricted_areas.append((x1, y1, x2, y2))
                self.status_label.config(text=f"제한 구역 추가됨: 총 {len(self.user_restricted_areas)}개")
            else:
                # 너무 작은 영역이면 제거
                if self.current_rectangle:
                    self.overlay_canvas.delete(self.current_rectangle)
    
    def load_image(self):
        file_path = filedialog.askopenfilename(
            title="이미지 파일 선택",
            filetypes=[("이미지 파일", "*.jpg *.jpeg *.png *.bmp")]
        )
        
        if file_path:
            try:
                # OpenCV로 이미지 로드
                self.original_image = cv2.imread(file_path)
                
                if self.original_image is None:
                    messagebox.showerror("오류", "이미지를 불러올 수 없습니다.")
                    return
                
                # 이미지 정사각형으로 자르기
                height, width = self.original_image.shape[:2]
                size = min(width, height)
                
                # 중앙에서 정사각형 자르기
                start_x = (width - size) // 2
                start_y = (height - size) // 2
                
                self.original_image = self.original_image[start_y:start_y+size, start_x:start_x+size]
                
                # 이미지 표시
                self.display_image(self.original_image, self.original_image_label)
                
                # 엣지 검출
                self.process_image()
                
                # 상태 업데이트
                self.status_label.config(text="이미지가 로드되었습니다. 원하는 설정을 선택한 후 '스트링 아트 생성' 버튼을 클릭하세요.")
                
                # 버튼 활성화
                self.generate_button.config(state=tk.NORMAL)
                
                # 사용자 설정 초기화
                self.user_restricted_areas = []
                self.user_added_pins = []
                
                # 제한 구역 표시를 위한 오버레이 캔버스 초기화
                if hasattr(self, 'overlay_canvas'):
                    self.overlay_canvas.delete("all")
                
            except Exception as e:
                messagebox.showerror("오류", f"이미지를 불러오는 중 오류가 발생했습니다: {str(e)}")
    
    def process_image(self):
        if self.original_image is not None:
            # 그레이스케일 변환
            gray_image = cv2.cvtColor(self.original_image, cv2.COLOR_BGR2GRAY)
            
            # 대비 조정
            contrast = self.image_contrast_var.get()
            adjusted_image = cv2.convertScaleAbs(gray_image, alpha=contrast, beta=0)
            
            # 가장자리 검출
            self.edge_image = cv2.Canny(adjusted_image, 50, 150)
            
            # 필요시 노이즈 제거
            self.edge_image = cv2.GaussianBlur(self.edge_image, (3, 3), 0)
            
            # 가장자리 검출 결과 표시
            edge_image_color = cv2.cvtColor(self.edge_image, cv2.COLOR_GRAY2BGR)
            self.display_image(edge_image_color, self.original_image_label)
    
    def display_image(self, image, label):
        # 이미지를 라벨에 맞게 리사이즈
        label_width = label.winfo_width()
        label_height = label.winfo_height()
        
        if label_width <= 1:  # 위젯이 아직 완전히 렌더링되지 않았을 때
            label_width = 600
            label_height = 600
        
        # 이미지 비율 유지
        img_height, img_width = image.shape[:2]
        ratio = min(label_width / img_width, label_height / img_height)
        
        new_width = int(img_width * ratio)
        new_height = int(img_height * ratio)
        
        # 이미지 리사이즈
        resized_image = cv2.resize(image, (new_width, new_height))
        
        # BGR -> RGB 변환 (OpenCV는 BGR, PIL은 RGB 사용)
        if len(resized_image.shape) == 3:  # 컬러 이미지
            resized_image = cv2.cvtColor(resized_image, cv2.COLOR_BGR2RGB)
        else:  # 그레이스케일 이미지
            resized_image = cv2.cvtColor(resized_image, cv2.COLOR_GRAY2RGB)
        
        # PIL 이미지로 변환
        pil_image = Image.fromarray(resized_image)
        
        # PhotoImage로 변환
        tk_image = ImageTk.PhotoImage(pil_image)
        
        # 라벨에 이미지 설정
        label.config(image=tk_image)
        label.image = tk_image  # 참조 유지
        
        # 표시된 이미지 크기 저장
        self.display_image_size = (new_width, new_height)
    
    def generate_string_art(self):
        if self.original_image is None:
            messagebox.showerror("오류", "먼저 이미지를 불러와주세요.")
            return
        
        if self.is_generating:
            messagebox.showinfo("알림", "이미 스트링 아트를 생성 중입니다. 완료될 때까지 기다려주세요.")
            return
        
        # 진행 상태 초기화
        self.progress = 0
        self.progress_bar["value"] = 0
        self.is_generating = True
        
        # 스트링 아트 생성 스레드 시작
        thread = threading.Thread(target=self.generate_string_art_thread)
        thread.daemon = True
        thread.start()
    
    
    def generate_string_art_thread(self):
        try:
            # 상태 업데이트 함수 정의
            def update_progress(value):
                self.progress_bar["value"] = value
                
            def update_status(text):
                self.status_label.config(text=text)

            # 핀 위치 계산
            self.master.after(0, lambda: update_status("핀 위치 계산 중..."))
            self.calculate_pin_positions()
            
            self.progress = 10
            self.master.after(0, lambda: update_progress(self.progress))
            
            # 자동 핀 최적화
            if self.auto_optimize_var.get():
                self.master.after(0, lambda: update_status("핀 최적화 중..."))
                self.optimize_pins()
            
            # 사용자 추가 핀 통합
            if self.user_added_pins:
                self.integrate_user_pins()
            
            self.progress = 20
            self.master.after(0, lambda: update_progress(self.progress))
            
            # 핀 배치도 그리기
            self.master.after(0, self.draw_pin_layout)
            
            # 스트링 아트 계산
            self.master.after(0, lambda: update_status("스트링 아트 계산 중..."))
            self.calculate_string_art()
            
            # 결과 표시
            self.master.after(0, self.display_string_art)
            
            # 제작 안내 생성 및 완료 처리
            self.progress = 100
            self.master.after(0, lambda: update_progress(self.progress))
            self.master.after(0, lambda: update_status("스트링 아트 생성 완료!"))
            self.master.after(0, lambda: self.save_button.configure(state="normal"))
            
        except Exception as e:
            self.master.after(0, lambda: messagebox.showerror("오류", f"스트링 아트 생성 중 오류가 발생했습니다: {str(e)}"))
            self.master.after(0, lambda: update_status("오류 발생"))
        finally:
            self.is_generating = False
    
    def calculate_pin_positions(self):
        pin_count = self.pin_count_var.get()
        pin_shape = self.pin_shape_var.get()
        
        # 이미지 크기
        height, width = self.edge_image.shape[:2]
        size = min(width, height)
        
        # 약간의 여백 추가
        margin = int(size * 0.05)
        radius = (size - 2 * margin) // 2
        center_x = width // 2
        center_y = height // 2
        
        # 핀 위치 계산
        pin_positions = []
        
        if pin_shape == "원형":
            # 원형 배치
            for i in range(pin_count):
                angle = 2 * np.pi * i / pin_count
                x = int(center_x + radius * np.cos(angle))
                y = int(center_y + radius * np.sin(angle))
                pin_positions.append((x, y))
        else:
            # 사각형 배치
            square_side = 2 * radius
            
            # 각 변에 핀 개수 (전체 핀 개수를 4등분)
            pins_per_side = pin_count // 4
            
            # 위쪽 변
            for i in range(pins_per_side):
                x = int(center_x - radius + square_side * i / (pins_per_side - 1))
                y = int(center_y - radius)
                pin_positions.append((x, y))
            
            # 오른쪽 변
            for i in range(pins_per_side):
                x = int(center_x + radius)
                y = int(center_y - radius + square_side * i / (pins_per_side - 1))
                pin_positions.append((x, y))
            
            # 아래쪽 변
            for i in range(pins_per_side):
                x = int(center_x + radius - square_side * i / (pins_per_side - 1))
                y = int(center_y + radius)
                pin_positions.append((x, y))
            
            # 왼쪽 변
            for i in range(pins_per_side):
                x = int(center_x - radius)
                y = int(center_y + radius - square_side * i / (pins_per_side - 1))
                pin_positions.append((x, y))
        
        self.pin_positions = pin_positions
    
    def optimize_pins(self):
        if self.edge_image is None or self.pin_positions is None:
            return
        
        # 이미지 윤곽선 주변의 활성도 계산
        activity_map = ndimage.gaussian_filter(self.edge_image.astype(float), sigma=5)
        
        # 각 핀 위치에서의 활성도 계산
        pin_activities = []
        for x, y in self.pin_positions:
            # 경계 확인
            if 0 <= x < activity_map.shape[1] and 0 <= y < activity_map.shape[0]:
                activity = activity_map[y, x]
            else:
                activity = 0
            pin_activities.append(activity)
        
        # 활성도가 낮은 핀은 더 높은 활성도 영역으로 이동
        optimized_pins = []
        for i, (x, y) in enumerate(self.pin_positions):
            if pin_activities[i] < np.mean(pin_activities) * 0.5:  # 평균보다 활성도가 낮은 경우
                # 주변 영역에서 더 높은 활성도 찾기
                search_radius = 20
                best_x, best_y = x, y
                best_activity = pin_activities[i]
                
                for dx in range(-search_radius, search_radius+1, 5):
                    for dy in range(-search_radius, search_radius+1, 5):
                        new_x, new_y = x + dx, y + dy
                        
                        # 경계 확인
                        if 0 <= new_x < activity_map.shape[1] and 0 <= new_y < activity_map.shape[0]:
                            new_activity = activity_map[new_y, new_x]
                            
                            if new_activity > best_activity:
                                best_activity = new_activity
                                best_x, best_y = new_x, new_y
                
                optimized_pins.append((best_x, best_y))
            else:
                optimized_pins.append((x, y))
        
        self.pin_positions = optimized_pins
    
    def integrate_user_pins(self):
        if not self.user_added_pins:
            return
        
        # 사용자 추가 핀을 이미지 크기에 맞게 조정
        adjusted_user_pins = []
        
        for x, y in self.user_added_pins:
            # 캔버스 좌표를 이미지 좌표로 변환
            canvas_width = self.pins_canvas.winfo_width()
            canvas_height = self.pins_canvas.winfo_height()
            
            img_width, img_height = self.edge_image.shape[1], self.edge_image.shape[0]
            
            # 비율 계산
            x_ratio = img_width / canvas_width
            y_ratio = img_height / canvas_height
            
            img_x = int(x * x_ratio)
            img_y = int(y * y_ratio)
            
            adjusted_user_pins.append((img_x, img_y))
        
        # 기존 핀 목록에 추가
        self.pin_positions.extend(adjusted_user_pins)
    
    def draw_pin_layout(self):
        # 핀 배치도 탭 캔버스 초기화
        self.pins_canvas.delete("all")
        
        # 캔버스 크기
        canvas_width = self.pins_canvas.winfo_width()
        canvas_height = self.pins_canvas.winfo_height()
        
        if canvas_width <= 1:  # 위젯이 아직 완전히 렌더링되지 않았을 때
            canvas_width = 600
            canvas_height = 600
        
        # 이미지 크기와 캔버스 크기 비율 계산
        img_height, img_width = self.edge_image.shape[:2]
        ratio = min(canvas_width / img_width, canvas_height / img_height)
        
        # 이미지 크기 조정
        display_width = int(img_width * ratio)
        display_height = int(img_height * ratio)
        
        # 배경 이미지 그리기 (흐리게)
        edge_image_rgb = cv2.cvtColor(self.edge_image, cv2.COLOR_GRAY2RGB)
        edge_image_blurred = cv2.GaussianBlur(edge_image_rgb, (9, 9), 0)
        
        # 이미지 리사이즈 및 변환
        edge_image_display = cv2.resize(edge_image_blurred, (display_width, display_height))
        pil_image = Image.fromarray(edge_image_display)
        tk_image = ImageTk.PhotoImage(pil_image)
        
        # 이미지 중앙에 배치
        x_offset = (canvas_width - display_width) // 2
        y_offset = (canvas_height - display_height) // 2
        
        # 배경 이미지 그리기
        self.pins_canvas.create_image(x_offset, y_offset, anchor=tk.NW, image=tk_image)
        self.pins_canvas.image = tk_image  # 참조 유지
        
        # 핀 그리기
        for i, (x, y) in enumerate(self.pin_positions):
            # 핀 위치를 캔버스 좌표로 변환
            canvas_x = int(x * ratio) + x_offset
            canvas_y = int(y * ratio) + y_offset
            
            # 핀 그리기 (작은 원)
            pin_color = "blue" if i < self.pin_count_var.get() else "red"
            self.pins_canvas.create_oval(canvas_x-3, canvas_y-3, canvas_x+3, canvas_y+3, fill=pin_color, outline=pin_color)
            
            # 핀 번호 표시
            if i % 10 == 0:  # 10개마다 번호 표시
                self.pins_canvas.create_text(canvas_x+5, canvas_y-5, text=str(i), fill="black" if not self.dark_mode else "white")
    
    def calculate_string_art(self):
        if self.edge_image is None or self.pin_positions is None:
            return
        
        # 이미지 크기
        height, width = self.edge_image.shape[:2]
        
        # 최대 실 개수
        max_strings = self.string_count_var.get()
        
        # 최소 핀 간격
        min_pin_gap = self.min_pin_gap_var.get()
        
        # 스트링 아트를 그릴 빈 이미지 생성
        background_color = 255 if self.string_color_var.get() else 0
        string_color = 0 if self.string_color_var.get() else 255
        
        string_art = np.ones((height, width), dtype=np.uint8) * background_color
        
        # 각 핀의 좌표 목록
        pin_positions = self.pin_positions
        
        # 핀 연결 정보 저장
        pin_connections = []
        
        # 첫 번째 핀 선택 (임의로 시작)
        current_pin = 0
        
        # 제한 구역 마스크 생성
        restricted_mask = np.zeros((height, width), dtype=np.uint8)
        for x1, y1, x2, y2 in self.user_restricted_areas:
            cv2.rectangle(restricted_mask, (x1, y1), (x2, y2), 255, -1)
        
        for _ in range(max_strings):
            # 진행 상태 업데이트
            self.progress = 20 + int((_ / max_strings) * 70)
            self.master.after(0, lambda p=self.progress: self.progress_bar.configure(value=p))
            
            best_line_value = -1
            best_target_pin = -1
            
            # 현재 핀에서 다른 모든 핀으로의 연결 평가
            for target_pin in range(len(pin_positions)):
                # 같은 핀이나 너무 가까운 핀은 건너뛰기
                if target_pin == current_pin or abs(target_pin - current_pin) < min_pin_gap:
                    continue
                
                # 이미 많이 사용된 핀은 건너뛰기
                pin_usage_count = sum(1 for p1, p2 in pin_connections if p1 == target_pin or p2 == target_pin)
                if pin_usage_count > 5:  # 한 핀당 최대 연결 수 제한
                    continue
                
                # 현재 핀과 대상 핀의 좌표
                x1, y1 = pin_positions[current_pin]
                x2, y2 = pin_positions[target_pin]
                
                # 제한 구역 확인
                if self.check_line_in_restricted_area(x1, y1, x2, y2, restricted_mask):
                    continue
                
                # 선을 그려서 얼마나 많은 엣지를 통과하는지 계산
                line_value = self.evaluate_line(x1, y1, x2, y2, self.edge_image, string_art)
                
                if line_value > best_line_value:
                    best_line_value = line_value
                    best_target_pin = target_pin
            
            # 최적의 연결을 찾지 못한 경우
            if best_target_pin == -1:
                break
            
            # 최적의 선 그리기
            x1, y1 = pin_positions[current_pin]
            x2, y2 = pin_positions[best_target_pin]
            cv2.line(string_art, (x1, y1), (x2, y2), string_color, 1)
            
            # 연결 정보 저장
            pin_connections.append((current_pin, best_target_pin))
            
            # 다음 핀으로 이동
            current_pin = best_target_pin
        
        self.string_art = string_art
        self.pin_connections = pin_connections
    
    def check_line_in_restricted_area(self, x1, y1, x2, y2, restricted_mask):
        """선이 제한 구역을 통과하는지 확인"""
        # 경계 확인
        height, width = restricted_mask.shape[:2]
        if (x1 < 0 or x1 >= width or y1 < 0 or y1 >= height or
            x2 < 0 or x2 >= width or y2 < 0 or y2 >= height):
            return True
        
        # 선을 따라 몇 개의 점을 샘플링하여 제한 구역 확인
        num_samples = 20
        for i in range(num_samples + 1):
            t = i / num_samples
            x = int((1 - t) * x1 + t * x2)
            y = int((1 - t) * y1 + t * y2)
            
            # 경계 확인
            if 0 <= x < width and 0 <= y < height:
                if restricted_mask[y, x] > 0:
                    return True
        
        return False
    
    def evaluate_line(self, x1, y1, x2, y2, edge_image, current_art, decay=0.8):
        """선의 품질을 평가: 엣지를 통과하는 정도와 이미 그려진 선과의 겹침 정도"""
        # 경계 확인
        height, width = edge_image.shape[:2]
        if (x1 < 0 or x1 >= width or y1 < 0 or y1 >= height or
            x2 < 0 or x2 >= width or y2 < 0 or y2 >= height):
            return -1
        
        # 스트링 컬러 (흑/백)
        string_color = 0 if self.string_color_var.get() else 255
        background_color = 255 if self.string_color_var.get() else 0
        
        # 선을 따라 픽셀 값 수집
        line_length = int(np.sqrt((x2 - x1)**2 + (y2 - y1)**2))
        line_value = 0
        overlap_penalty = 0
        
        # 선이 너무 짧으면 건너뛰기
        if line_length < 10:
            return -1
        
        # 선을 따라 픽셀 샘플링
        num_samples = min(line_length, 100)  # 최대 100개 샘플
        
        for i in range(num_samples + 1):
            t = i / num_samples
            x = int((1 - t) * x1 + t * x2)
            y = int((1 - t) * y1 + t * y2)
            
            # 경계 확인
            if 0 <= x < width and 0 <= y < height:
                # 엣지 이미지에서의 값 (높을수록 엣지에 가까움)
                edge_value = edge_image[y, x] / 255.0
                
                # 현재 아트에서의 값 (이미 그려진 선과의 겹침 확인)
                art_value = current_art[y, x]
                
                # 이미 그려진 선과 겹치면 페널티
                if art_value == string_color:
                    overlap_penalty += decay ** i  # 거리에 따라 감소하는 페널티
                
                # 엣지를 통과하면 가산점
                line_value += edge_value
        
        # 최종 점수 계산 (엣지 통과 점수 - 겹침 페널티)
        final_value = line_value - overlap_penalty * 2
        
        return final_value
    
    def display_string_art(self):
        if self.string_art is None:
            return
        
        # 스트링 아트 결과 탭으로 전환
        self.notebook.select(self.result_tab)
        
        # 스트링 아트 이미지 표시
        self.display_image(self.string_art, self.result_label)
    
    def generate_guide(self):
        if self.pin_positions is None or self.pin_connections is None:
            return
        
        # 제작 안내 텍스트 작성
        self.guide_text.config(state=tk.NORMAL)
        self.guide_text.delete(1.0, tk.END)
        
        # 헤더 추가
        self.guide_text.insert(tk.END, "스트링 아트 제작 안내\n\n", "header")
        self.guide_text.insert(tk.END, "다음 단계에 따라 스트링 아트를 제작하세요:\n\n")
        
        # 준비물 안내
        self.guide_text.insert(tk.END, "준비물:\n", "subheader")
        self.guide_text.insert(tk.END, "- 나무판 또는 코르크판\n")
        self.guide_text.insert(tk.END, "- 핀 또는 못 (총 {}개)\n".format(len(self.pin_positions)))
        self.guide_text.insert(tk.END, "- 검은색 실 (또는 선택한 색상)\n")
        self.guide_text.insert(tk.END, "- 망치 (못을 사용하는 경우)\n")
        self.guide_text.insert(tk.END, "- 자\n\n")
        
        # 핀 배치 안내
        self.guide_text.insert(tk.END, "핀 배치:\n", "subheader")
        self.guide_text.insert(tk.END, "1. 나무판이나 코르크판에 원 또는 사각형을 그립니다.\n")
        self.guide_text.insert(tk.END, "2. 핀 배치도를 참고하여 핀 위치에 핀을 고정합니다.\n")
        self.guide_text.insert(tk.END, "3. 각 핀에 번호를 부여합니다 (0부터 시작).\n\n")
        
        # 실 연결 안내
        self.guide_text.insert(tk.END, "실 연결 순서:\n", "subheader")
        
        for i, (pin1, pin2) in enumerate(self.pin_connections[:100]):  # 처음 100개만 표시
            self.guide_text.insert(tk.END, "{}. 핀 {} → 핀 {}\n".format(i+1, pin1, pin2))
        
        if len(self.pin_connections) > 100:
            self.guide_text.insert(tk.END, "... 외 {}개 연결\n".format(len(self.pin_connections) - 100))
        
        # 텍스트 스타일 적용
        self.guide_text.tag_configure("header", font=("Helvetica", 14, "bold"))
        self.guide_text.tag_configure("subheader", font=("Helvetica", 12, "bold"))
        
        # 편집 불가능하게 설정
        self.guide_text.config(state=tk.DISABLED)
    
    def save_results(self):
        if self.string_art is None:
            messagebox.showerror("오류", "저장할 스트링 아트 결과가 없습니다.")
            return
        
        # 저장 폴더 선택
        save_dir = filedialog.askdirectory(title="결과를 저장할 폴더 선택")
        
        if not save_dir:
            return
        
        try:
            # 타임스탬프 생성
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            save_prefix = os.path.join(save_dir, f"string_art_{timestamp}")
            
            # 스트링 아트 이미지 저장
            cv2.imwrite(f"{save_prefix}_result.png", self.string_art)
            
            # 원본 이미지 저장
            cv2.imwrite(f"{save_prefix}_original.png", self.original_image)
            
            # 핀 배치도 저장
            fig, ax = plt.subplots(figsize=(10, 10))
            
            # 핀 그리기
            for i, (x, y) in enumerate(self.pin_positions):
                ax.plot(x, y, 'o', markersize=3, color='blue')
                if i % 10 == 0:  # 10개마다 번호 표시
                    ax.text(x, y, str(i), fontsize=8)
            
            # 이미지 크기 설정
            height, width = self.original_image.shape[:2]
            ax.set_xlim(0, width)
            ax.set_ylim(height, 0)  # y축 반전 (이미지 좌표계)
            
            plt.title("핀 배치도")
            plt.savefig(f"{save_prefix}_pins.png", dpi=300)
            plt.close()
            
            # 제작 안내 텍스트 저장
            guide_text = self.guide_text.get(1.0, tk.END)
            with open(f"{save_prefix}_guide.txt", 'w', encoding='utf-8') as f:
                f.write(guide_text)
            
            # 핀 연결 데이터 저장 (CSV)
            with open(f"{save_prefix}_connections.csv", 'w', encoding='utf-8') as f:
                f.write("연결_번호,시작_핀,도착_핀\n")
                for i, (pin1, pin2) in enumerate(self.pin_connections):
                    f.write(f"{i+1},{pin1},{pin2}\n")
            
            messagebox.showinfo("저장 완료", f"모든 결과가 저장되었습니다:\n{save_prefix}_*.png/txt/csv")
            
        except Exception as e:
            messagebox.showerror("저장 오류", f"결과 저장 중 오류가 발생했습니다: {str(e)}")
    
    def reset(self):
        # 이미지 초기화
        self.original_image = None
        self.edge_image = None
        self.string_art = None
        
        # 핀 정보 초기화
        self.pin_positions = None
        self.pin_connections = []
        
        # 사용자 설정 초기화
        self.user_restricted_areas = []
        self.user_added_pins = []
        
        # 진행 상태 초기화
        self.progress = 0
        self.progress_bar["value"] = 0
        
        # 이미지 라벨 초기화
        self.original_image_label.config(image='', text="원본 이미지를 불러와주세요")
        self.result_label.config(image='', text="스트링 아트 결과가 여기에 표시됩니다")
        
        # 핀 캔버스 초기화
        self.pins_canvas.delete("all")
        
        # 제작 안내 초기화
        self.guide_text.config(state=tk.NORMAL)
        self.guide_text.delete(1.0, tk.END)
        self.guide_text.insert(tk.END, "제작 안내는 스트링 아트 생성 후 표시됩니다.")
        self.guide_text.config(state=tk.DISABLED)
        
        # 오버레이 캔버스 초기화 (있는 경우)
        if hasattr(self, 'overlay_canvas'):
            self.overlay_canvas.delete("all")
        
        # 버튼 상태 업데이트
        self.generate_button.config(state=tk.DISABLED)
        self.save_button.config(state=tk.DISABLED)
        
        # 상태 업데이트
        self.status_label.config(text="초기화 완료. 이미지를 불러와주세요.")

def main():
    root = tk.Tk()
    app = StringArtGenerator(root)
    root.mainloop()

if __name__ == "__main__":
    main()