<!DOCTYPE html>
<html>
<head>
    
    <meta http-equiv="content-type" content="text/html; charset=UTF-8" />
    <script src="https://cdn.jsdelivr.net/npm/leaflet@1.9.3/dist/leaflet.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Leaflet.awesome-markers/2.0.2/leaflet.awesome-markers.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/leaflet@1.9.3/dist/leaflet.css"/>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.2/dist/css/bootstrap.min.css"/>
    <link rel="stylesheet" href="https://netdna.bootstrapcdn.com/bootstrap/3.0.0/css/bootstrap-glyphicons.css"/>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.2.0/css/all.min.css"/>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/Leaflet.awesome-markers/2.0.2/leaflet.awesome-markers.css"/>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/python-visualization/folium/folium/templates/leaflet.awesome.rotate.min.css"/>
    
            <meta name="viewport" content="width=device-width,
                initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
            <style>
                #map_14965def3c247df405504b75f5c59060 {
                    position: relative;
                    width: 100.0%;
                    height: 100.0%;
                    left: 0.0%;
                    top: 0.0%;
                }
                .leaflet-container { font-size: 1rem; }
            </style>

            <style>html, body {
                width: 100%;
                height: 100%;
                margin: 0;
                padding: 0;
            }
            </style>

            <style>#map {
                position:absolute;
                top:0;
                bottom:0;
                right:0;
                left:0;
                }
            </style>

            <script>
                L_NO_TOUCH = false;
                L_DISABLE_3D = false;
            </script>

        
</head>
<body>
    
    
            <div class="folium-map" id="map_14965def3c247df405504b75f5c59060" ></div>
        
</body>
<script>
    
    
            var map_14965def3c247df405504b75f5c59060 = L.map(
                "map_14965def3c247df405504b75f5c59060",
                {
                    center: [36.869844399722226, 10.352544099722222],
                    crs: L.CRS.EPSG3857,
                    ...{
  "zoom": 2,
  "zoomControl": true,
  "preferCanvas": false,
}

                }
            );

            

        
    
            var tile_layer_d7ed2ef506263d8130082fac57791909 = L.tileLayer(
                "https://tile.openstreetmap.org/{z}/{x}/{y}.png",
                {
  "minZoom": 0,
  "maxZoom": 19,
  "maxNativeZoom": 19,
  "noWrap": false,
  "attribution": "\u0026copy; \u003ca href=\"https://www.openstreetmap.org/copyright\"\u003eOpenStreetMap\u003c/a\u003e contributors",
  "subdomains": "abc",
  "detectRetina": false,
  "tms": false,
  "opacity": 1,
}

            );
        
    
            tile_layer_d7ed2ef506263d8130082fac57791909.addTo(map_14965def3c247df405504b75f5c59060);
        
    
            var marker_31f2a5a2e8ce275359034ee11567a3c4 = L.marker(
                [36.869844399722226, 10.352544099722222],
                {
}
            ).addTo(map_14965def3c247df405504b75f5c59060);
        
    
        var popup_33e4982f8e75190778e1b027ea26eb3f = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_1a7507939fb8ac9e5ddbe9e7db4bacf5 = $(`<div id="html_1a7507939fb8ac9e5ddbe9e7db4bacf5" style="width: 100.0%; height: 100.0%;"><br>20250101_074302.jpg</div>`)[0];
                popup_33e4982f8e75190778e1b027ea26eb3f.setContent(html_1a7507939fb8ac9e5ddbe9e7db4bacf5);
            
        

        marker_31f2a5a2e8ce275359034ee11567a3c4.bindPopup(popup_33e4982f8e75190778e1b027ea26eb3f)
        ;

        
    
</script>
</html>