import os
import sys
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import PIL.Image
from PIL import ImageTk
from PIL.ExifTags import TAGS, GPSTAGS
import shutil
import datetime
import math
import json
from pathlib import Path
import threading
import time

class PhotoGrouper(tk.Tk):
    def __init__(self):
        super().__init__()

        # Application settings
        self.title("Photo GPS & Time Grouper")
        self.geometry("1000x700")
        self.minsize(800, 600)

        # Style configuration
        self.style = ttk.Style()
        self.style.theme_use('clam')  # Use a modern theme

        # Configure colors
        self.bg_color = "#f5f5f5"
        self.accent_color = "#4a6ea9"
        self.text_color = "#333333"

        self.style.configure('TFrame', background=self.bg_color)
        self.style.configure('TLabel', background=self.bg_color, foreground=self.text_color)
        self.style.configure('TButton', background=self.accent_color, foreground='white')
        self.style.configure('Accent.TButton', background=self.accent_color, foreground='white')
        self.style.map('Accent.TButton',
                       background=[('active', '#3a5e99'), ('disabled', '#cccccc')])

        self.configure(bg=self.bg_color)

        # Application variables
        self.photo_files = []  # List of photo file paths
        self.photo_data = []   # List of dictionaries with photo metadata
        self.groups = {}       # Dictionary of photo groups

        # Default settings
        self.time_threshold = tk.IntVar(value=30)  # Minutes
        self.distance_threshold = tk.DoubleVar(value=0.5)  # Kilometers
        self.use_time_grouping = tk.BooleanVar(value=True)  # Whether to use time for grouping
        self.use_gps_grouping = tk.BooleanVar(value=True)  # Whether to use GPS for grouping
        self.output_mode = tk.StringVar(value="copy")  # "copy" or "move"

        # Create UI components
        self.create_menu()
        self.create_main_frame()

        # Bind window close event
        self.protocol("WM_DELETE_WINDOW", self.on_close)

    def create_menu(self):
        """Create the application menu"""
        menubar = tk.Menu(self)

        # File menu
        file_menu = tk.Menu(menubar, tearoff=0)
        file_menu.add_command(label="Load Photos", command=self.load_photos)
        file_menu.add_command(label="Load Folder", command=self.load_folder)
        file_menu.add_separator()
        file_menu.add_command(label="Save Settings", command=self.save_settings)
        file_menu.add_command(label="Load Settings", command=self.load_settings)
        file_menu.add_separator()
        file_menu.add_command(label="Exit", command=self.on_close)
        menubar.add_cascade(label="File", menu=file_menu)

        # Help menu
        help_menu = tk.Menu(menubar, tearoff=0)
        help_menu.add_command(label="About", command=self.show_about)
        menubar.add_cascade(label="Help", menu=help_menu)

        self.config(menu=menubar)

    def create_main_frame(self):
        """Create the main application frame and widgets"""
        # Main container
        main_frame = ttk.Frame(self)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Top frame for controls
        top_frame = ttk.Frame(main_frame)
        top_frame.pack(fill=tk.X, pady=(0, 10))

        # Buttons for loading photos
        load_btn = ttk.Button(top_frame, text="Load Photos", command=self.load_photos, style='Accent.TButton')
        load_btn.pack(side=tk.LEFT, padx=(0, 5))

        load_folder_btn = ttk.Button(top_frame, text="Load Folder", command=self.load_folder, style='Accent.TButton')
        load_folder_btn.pack(side=tk.LEFT, padx=5)

        # Settings button
        settings_btn = ttk.Button(top_frame, text="Settings", command=self.show_settings)
        settings_btn.pack(side=tk.LEFT, padx=5)

        # Process button
        self.process_btn = ttk.Button(top_frame, text="Group Photos", command=self.process_photos, state=tk.DISABLED, style='Accent.TButton')
        self.process_btn.pack(side=tk.RIGHT, padx=5)

        # Save All Groups button
        self.save_all_btn = ttk.Button(top_frame, text="Save All Groups", command=self.save_all_groups, state=tk.DISABLED)
        self.save_all_btn.pack(side=tk.RIGHT, padx=5)

        # Clear button
        clear_btn = ttk.Button(top_frame, text="Clear All", command=self.clear_all)
        clear_btn.pack(side=tk.RIGHT, padx=5)

        # Create notebook for tabs
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)

        # Photos tab
        self.photos_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.photos_frame, text="Photos")

        # Create a canvas with scrollbar for photos
        self.photos_canvas_frame = ttk.Frame(self.photos_frame)
        self.photos_canvas_frame.pack(fill=tk.BOTH, expand=True)

        self.photos_canvas = tk.Canvas(self.photos_canvas_frame, bg=self.bg_color)
        self.photos_scrollbar = ttk.Scrollbar(self.photos_canvas_frame, orient=tk.VERTICAL, command=self.photos_canvas.yview)
        self.photos_canvas.configure(yscrollcommand=self.photos_scrollbar.set)

        self.photos_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.photos_canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # Frame inside canvas for photos
        self.photos_inner_frame = ttk.Frame(self.photos_canvas)
        self.photos_canvas_window = self.photos_canvas.create_window((0, 0), window=self.photos_inner_frame, anchor=tk.NW)

        # Configure canvas scrolling
        self.photos_inner_frame.bind("<Configure>", self.on_frame_configure)
        self.photos_canvas.bind("<Configure>", self.on_canvas_configure)

        # Drop zone label
        self.drop_label = ttk.Label(self.photos_inner_frame, text="Drag and drop photos here or use the Load buttons",
                                    font=("Arial", 14), foreground="#999999")
        self.drop_label.pack(pady=100)

        # Groups tab
        self.groups_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.groups_frame, text="Groups")

        # Create a canvas with scrollbar for groups
        self.groups_canvas_frame = ttk.Frame(self.groups_frame)
        self.groups_canvas_frame.pack(fill=tk.BOTH, expand=True)

        self.groups_canvas = tk.Canvas(self.groups_canvas_frame, bg=self.bg_color)
        self.groups_scrollbar = ttk.Scrollbar(self.groups_canvas_frame, orient=tk.VERTICAL, command=self.groups_canvas.yview)
        self.groups_canvas.configure(yscrollcommand=self.groups_scrollbar.set)

        self.groups_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.groups_canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # Frame inside canvas for groups
        self.groups_inner_frame = ttk.Frame(self.groups_canvas)
        self.groups_canvas_window = self.groups_canvas.create_window((0, 0), window=self.groups_inner_frame, anchor=tk.NW)

        # Configure canvas scrolling
        self.groups_inner_frame.bind("<Configure>", lambda e: self.groups_canvas.configure(scrollregion=self.groups_canvas.bbox("all")))

        # Status bar
        self.status_var = tk.StringVar(value="Ready")
        self.status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
        self.status_bar.pack(fill=tk.X, pady=(10, 0))

        # Progress bar
        self.progress_var = tk.DoubleVar(value=0.0)
        self.progress_bar = ttk.Progressbar(main_frame, variable=self.progress_var, maximum=100)
        self.progress_bar.pack(fill=tk.X, pady=(5, 0))

    def on_frame_configure(self, event):
        """Configure the canvas scrolling region when the inner frame size changes"""
        self.photos_canvas.configure(scrollregion=self.photos_canvas.bbox("all"))

    def on_canvas_configure(self, event):
        """Adjust the inner frame width when the canvas size changes"""
        canvas_width = event.width
        self.photos_canvas.itemconfig(self.photos_canvas_window, width=canvas_width)

    def load_photos(self):
        """Open file dialog to select multiple photos"""
        file_paths = filedialog.askopenfilenames(
            title="Select Photos",
            filetypes=[
                ("Image files", "*.jpg *.jpeg *.png *.tif *.tiff *.bmp"),
                ("JPEG", "*.jpg *.jpeg"),
                ("PNG", "*.png"),
                ("All files", "*.*")
            ]
        )

        if file_paths:
            self.add_photos(file_paths)

    def load_folder(self):
        """Open folder dialog to select a folder containing photos"""
        folder_path = filedialog.askdirectory(title="Select Folder Containing Photos")

        if folder_path:
            # Get all image files from the folder
            image_extensions = ('.jpg', '.jpeg', '.png', '.tif', '.tiff', '.bmp')
            file_paths = []

            for root, dirs, files in os.walk(folder_path):
                for file in files:
                    if file.lower().endswith(image_extensions):
                        file_paths.append(os.path.join(root, file))

            if file_paths:
                self.add_photos(file_paths)
            else:
                messagebox.showinfo("No Images", "No image files found in the selected folder.")

    def add_photos(self, file_paths):
        """Add photos to the application and display them"""
        # Clear existing photos if this is the first batch
        if not self.photo_files:
            self.clear_photos_display()

        # Add new files to the list
        new_files = [f for f in file_paths if f not in self.photo_files]
        if not new_files:
            messagebox.showinfo("No New Photos", "All selected photos are already loaded.")
            return

        # Update status
        self.status_var.set(f"Loading {len(new_files)} photos...")
        self.update_idletasks()

        # Start a thread to load photos
        threading.Thread(target=self.load_photos_thread, args=(new_files,), daemon=True).start()

    def load_photos_thread(self, file_paths):
        """Thread function to load photos and extract metadata"""
        total_files = len(file_paths)

        # Hide the drop label
        self.drop_label.pack_forget()

        # Create a frame for the thumbnails if it doesn't exist
        if not hasattr(self, 'thumbnails_frame'):
            self.thumbnails_frame = ttk.Frame(self.photos_inner_frame)
            self.thumbnails_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Keep track of thumbnails
        if not hasattr(self, 'thumbnails'):
            self.thumbnails = []

        # Process each file
        for i, file_path in enumerate(file_paths):
            try:
                # Update progress
                progress = (i / total_files) * 100
                self.progress_var.set(progress)
                self.status_var.set(f"Loading photo {i+1} of {total_files}...")
                self.update_idletasks()

                # Extract metadata
                photo_data = self.extract_photo_metadata(file_path)

                # Add to lists
                self.photo_files.append(file_path)
                self.photo_data.append(photo_data)

                # Create thumbnail
                self.create_thumbnail(file_path, photo_data)

            except Exception as e:
                print(f"Error processing {file_path}: {str(e)}")

        # Update status
        self.status_var.set(f"Loaded {len(self.photo_files)} photos")
        self.progress_var.set(0)

        # Enable the process button if we have photos
        if self.photo_files:
            self.process_btn.config(state=tk.NORMAL)

    def extract_photo_metadata(self, file_path):
        """Extract metadata from a photo file"""
        try:
            # Open the image
            img = PIL.Image.open(file_path)

            # Initialize data dictionary
            photo_data = {
                'filename': os.path.basename(file_path),
                'path': file_path,
                'date_taken': None,
                'gps': {
                    'latitude': None,
                    'longitude': None
                }
            }

            # Extract EXIF data if available
            if hasattr(img, '_getexif') and img._getexif():
                exif_data = img._getexif()

                if exif_data:
                    # Look for date/time information
                    for tag_id, tag_name in TAGS.items():
                        if tag_name == 'DateTimeOriginal' and tag_id in exif_data:
                            date_str = exif_data[tag_id]
                            try:
                                # Parse date string (format: 'YYYY:MM:DD HH:MM:SS')
                                dt = datetime.datetime.strptime(date_str, '%Y:%m:%d %H:%M:%S')
                                photo_data['date_taken'] = dt
                            except ValueError:
                                pass

                        # Look for GPS information
                        if tag_name == 'GPSInfo' and tag_id in exif_data:
                            gps_info = exif_data[tag_id]

                            # Extract latitude
                            if 2 in gps_info:  # GPSLatitude
                                lat_ref = gps_info.get(1, 'N')  # GPSLatitudeRef
                                lat = self.convert_gps_coords(gps_info[2], lat_ref)
                                photo_data['gps']['latitude'] = lat

                            # Extract longitude
                            if 4 in gps_info:  # GPSLongitude
                                lon_ref = gps_info.get(3, 'E')  # GPSLongitudeRef
                                lon = self.convert_gps_coords(gps_info[4], lon_ref)
                                photo_data['gps']['longitude'] = lon

            return photo_data

        except Exception as e:
            print(f"Error extracting metadata from {file_path}: {str(e)}")
            return {
                'filename': os.path.basename(file_path),
                'path': file_path,
                'date_taken': None,
                'gps': {
                    'latitude': None,
                    'longitude': None
                }
            }

    def convert_gps_coords(self, coords, ref):
        """Convert GPS coordinates from EXIF format to decimal degrees"""
        degrees = coords[0]
        minutes = coords[1]
        seconds = coords[2]

        decimal_degrees = degrees + (minutes / 60.0) + (seconds / 3600.0)

        # If reference is South or West, make the coordinate negative
        if ref in ['S', 'W']:
            decimal_degrees = -decimal_degrees

        return decimal_degrees

    def create_thumbnail(self, file_path, photo_data):
        """Create a thumbnail for the photo and add it to the display"""
        try:
            # Create a frame for this thumbnail
            thumb_frame = ttk.Frame(self.thumbnails_frame)
            thumb_frame.pack(side=tk.LEFT, padx=5, pady=5)

            # Load and resize the image
            img = PIL.Image.open(file_path)
            img.thumbnail((100, 100))  # Resize to thumbnail size
            photo_img = ImageTk.PhotoImage(img)

            # Create a label for the image
            img_label = ttk.Label(thumb_frame, image=photo_img)
            img_label.image = photo_img  # Keep a reference to prevent garbage collection
            img_label.pack()

            # Add filename label
            filename = os.path.basename(file_path)
            if len(filename) > 15:
                filename = filename[:12] + "..."
            name_label = ttk.Label(thumb_frame, text=filename)
            name_label.pack()

            # Add date label if available
            if photo_data['date_taken']:
                date_str = photo_data['date_taken'].strftime('%Y-%m-%d %H:%M')
                date_label = ttk.Label(thumb_frame, text=date_str)
                date_label.pack()

            # Add GPS label if available
            if photo_data['gps']['latitude'] is not None and photo_data['gps']['longitude'] is not None:
                lat = photo_data['gps']['latitude']
                lon = photo_data['gps']['longitude']
                gps_label = ttk.Label(thumb_frame, text=f"GPS: {lat:.4f}, {lon:.4f}")
                gps_label.pack()

            # Store the thumbnail reference
            self.thumbnails.append((thumb_frame, photo_img))

        except Exception as e:
            print(f"Error creating thumbnail for {file_path}: {str(e)}")

    def clear_photos_display(self):
        """Clear the photos display"""
        # Clear thumbnails if they exist
        if hasattr(self, 'thumbnails_frame'):
            self.thumbnails_frame.destroy()
            delattr(self, 'thumbnails_frame')

        if hasattr(self, 'thumbnails'):
            delattr(self, 'thumbnails')

        # Show the drop label again
        self.drop_label.pack(pady=100)

    def clear_all(self):
        """Clear all loaded photos and reset the application"""
        if self.photo_files and messagebox.askyesno("Clear All", "Are you sure you want to clear all loaded photos?"):
            self.photo_files = []
            self.photo_data = []
            self.groups = {}
            self.clear_photos_display()
            self.clear_groups_display()
            self.process_btn.config(state=tk.DISABLED)
            self.save_all_btn.config(state=tk.DISABLED)
            self.status_var.set("Ready")
            self.progress_var.set(0)

    def clear_groups_display(self):
        """Clear the groups display"""
        for widget in self.groups_inner_frame.winfo_children():
            widget.destroy()

    def show_settings(self):
        """Show the settings dialog"""
        settings_window = tk.Toplevel(self)
        settings_window.title("Settings")
        settings_window.geometry("400x350")
        settings_window.resizable(False, False)
        settings_window.transient(self)
        settings_window.grab_set()

        # Create a frame for the settings
        frame = ttk.Frame(settings_window, padding=20)
        frame.pack(fill=tk.BOTH, expand=True)

        # Grouping options
        ttk.Label(frame, text="Grouping Options", font=("Arial", 12, "bold")).grid(row=0, column=0, columnspan=2, sticky=tk.W, pady=(0, 10))

        # Time grouping checkbox
        ttk.Checkbutton(frame, text="Group by Time", variable=self.use_time_grouping).grid(row=1, column=0, sticky=tk.W)

        # Time threshold
        ttk.Label(frame, text="Time Threshold (minutes):").grid(row=2, column=0, sticky=tk.W, padx=(20, 0))
        time_entry = ttk.Entry(frame, width=10, textvariable=self.time_threshold)
        time_entry.grid(row=2, column=1, sticky=tk.W)

        # GPS grouping checkbox
        ttk.Checkbutton(frame, text="Group by GPS Location", variable=self.use_gps_grouping).grid(row=3, column=0, sticky=tk.W, pady=(10, 0))

        # Distance threshold
        ttk.Label(frame, text="Distance Threshold (km):").grid(row=4, column=0, sticky=tk.W, padx=(20, 0))
        distance_entry = ttk.Entry(frame, width=10, textvariable=self.distance_threshold)
        distance_entry.grid(row=4, column=1, sticky=tk.W)

        # Output options
        ttk.Label(frame, text="Output Options", font=("Arial", 12, "bold")).grid(row=5, column=0, columnspan=2, sticky=tk.W, pady=(20, 10))

        # Output mode
        ttk.Label(frame, text="When saving to folders:").grid(row=6, column=0, sticky=tk.W)
        ttk.Radiobutton(frame, text="Copy photos", variable=self.output_mode, value="copy").grid(row=7, column=0, sticky=tk.W, padx=(20, 0))
        ttk.Radiobutton(frame, text="Move photos", variable=self.output_mode, value="move").grid(row=8, column=0, sticky=tk.W, padx=(20, 0))

        # Buttons
        button_frame = ttk.Frame(frame)
        button_frame.grid(row=9, column=0, columnspan=2, pady=(20, 0))

        ttk.Button(button_frame, text="OK", command=settings_window.destroy).pack(side=tk.RIGHT, padx=5)
        ttk.Button(button_frame, text="Reset to Defaults", command=self.reset_settings).pack(side=tk.RIGHT, padx=5)

    def reset_settings(self):
        """Reset settings to default values"""
        self.time_threshold.set(30)
        self.distance_threshold.set(0.5)
        self.use_time_grouping.set(True)
        self.use_gps_grouping.set(True)
        self.output_mode.set("copy")

    def save_settings(self):
        """Save settings to a JSON file"""
        file_path = filedialog.asksaveasfilename(
            title="Save Settings",
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )

        if file_path:
            settings = {
                "time_threshold": self.time_threshold.get(),
                "distance_threshold": self.distance_threshold.get(),
                "use_time_grouping": self.use_time_grouping.get(),
                "use_gps_grouping": self.use_gps_grouping.get(),
                "output_mode": self.output_mode.get()
            }

            try:
                with open(file_path, 'w') as f:
                    json.dump(settings, f, indent=4)
                self.status_var.set(f"Settings saved to {file_path}")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to save settings: {str(e)}")

    def load_settings(self):
        """Load settings from a JSON file"""
        file_path = filedialog.askopenfilename(
            title="Load Settings",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )

        if file_path:
            try:
                with open(file_path, 'r') as f:
                    settings = json.load(f)

                # Apply settings
                if "time_threshold" in settings:
                    self.time_threshold.set(settings["time_threshold"])
                if "distance_threshold" in settings:
                    self.distance_threshold.set(settings["distance_threshold"])
                if "use_time_grouping" in settings:
                    self.use_time_grouping.set(settings["use_time_grouping"])
                if "use_gps_grouping" in settings:
                    self.use_gps_grouping.set(settings["use_gps_grouping"])
                if "output_mode" in settings:
                    self.output_mode.set(settings["output_mode"])

                self.status_var.set(f"Settings loaded from {file_path}")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to load settings: {str(e)}")

    def process_photos(self):
        """Process photos and create groups based on time and GPS data"""
        if not self.photo_files:
            messagebox.showinfo("No Photos", "Please load photos first.")
            return

        # Check if we have any grouping criteria enabled
        if not self.use_time_grouping.get() and not self.use_gps_grouping.get():
            messagebox.showinfo("No Grouping Criteria", "Please enable at least one grouping criterion (time or GPS).")
            return

        # Clear existing groups
        self.groups = {}
        self.clear_groups_display()

        # Update status
        self.status_var.set("Processing photos...")
        self.update_idletasks()

        # Start a thread to process photos
        threading.Thread(target=self.process_photos_thread, daemon=True).start()

    def process_photos_thread(self):
        """Thread function to process photos and create groups"""
        try:
            # Get settings
            time_threshold = self.time_threshold.get() * 60  # Convert minutes to seconds
            distance_threshold = self.distance_threshold.get()
            use_time = self.use_time_grouping.get()
            use_gps = self.use_gps_grouping.get()

            # Create groups
            self.create_photo_groups(time_threshold, distance_threshold, use_time, use_gps)

            # Display groups
            self.display_groups()

            # Update status
            self.status_var.set(f"Created {len(self.groups)} groups")
            self.progress_var.set(0)

            # Enable save all button if we have groups
            if self.groups:
                self.save_all_btn.config(state=tk.NORMAL)

            # Switch to groups tab
            self.notebook.select(1)  # Select the Groups tab

        except Exception as e:
            messagebox.showerror("Error", f"An error occurred while processing photos: {str(e)}")
            self.status_var.set("Error processing photos")

    def create_photo_groups(self, time_threshold, distance_threshold, use_time, use_gps):
        """Create groups of photos based on time and GPS data"""
        # Sort photos by date if available
        sorted_photos = sorted(
            [(i, data) for i, data in enumerate(self.photo_data) if data['date_taken'] is not None],
            key=lambda x: x[1]['date_taken']
        )

        # Add photos without date at the end
        sorted_photos.extend([(i, data) for i, data in enumerate(self.photo_data) if data['date_taken'] is None])

        # Initialize groups
        current_group = []
        group_index = 0

        # Process each photo
        for i, (photo_index, photo) in enumerate(sorted_photos):
            # Update progress
            progress = (i / len(sorted_photos)) * 100
            self.progress_var.set(progress)
            self.status_var.set(f"Processing photo {i+1} of {len(sorted_photos)}...")
            self.update_idletasks()

            # If this is the first photo, start a new group
            if not current_group:
                current_group.append(photo_index)
                continue

            # Get the last photo in the current group
            last_photo = self.photo_data[current_group[-1]]

            # Check if the photo belongs to the current group
            belongs_to_group = True

            # Time check
            if use_time and photo['date_taken'] is not None and last_photo['date_taken'] is not None:
                time_diff = (photo['date_taken'] - last_photo['date_taken']).total_seconds()
                if abs(time_diff) > time_threshold:
                    belongs_to_group = False

            # GPS check
            if use_gps and belongs_to_group:
                if (photo['gps']['latitude'] is not None and photo['gps']['longitude'] is not None and
                    last_photo['gps']['latitude'] is not None and last_photo['gps']['longitude'] is not None):
                    distance = self.calculate_distance(
                        photo['gps']['latitude'], photo['gps']['longitude'],
                        last_photo['gps']['latitude'], last_photo['gps']['longitude']
                    )
                    if distance > distance_threshold:
                        belongs_to_group = False

            # Add to current group or start a new one
            if belongs_to_group:
                current_group.append(photo_index)
            else:
                # Save current group
                self.groups[group_index] = current_group
                group_index += 1

                # Start a new group
                current_group = [photo_index]

        # Save the last group if not empty
        if current_group:
            self.groups[group_index] = current_group

    def calculate_distance(self, lat1, lon1, lat2, lon2):
        """Calculate distance between two GPS coordinates in kilometers using Haversine formula"""
        # Convert decimal degrees to radians
        lat1, lon1, lat2, lon2 = map(math.radians, [lat1, lon1, lat2, lon2])

        # Haversine formula
        dlon = lon2 - lon1
        dlat = lat2 - lat1
        a = math.sin(dlat/2)**2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlon/2)**2
        c = 2 * math.asin(math.sqrt(a))
        r = 6371  # Radius of Earth in kilometers

        return c * r

    def display_groups(self):
        """Display the created groups in the Groups tab"""
        # Clear existing display
        self.clear_groups_display()

        if not self.groups:
            ttk.Label(self.groups_inner_frame, text="No groups created. Try adjusting the grouping settings.").pack(pady=20)
            return

        # Create a frame for the groups
        groups_container = ttk.Frame(self.groups_inner_frame)
        groups_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Display each group
        for group_index, photo_indices in self.groups.items():
            # Create a frame for this group
            group_frame = ttk.LabelFrame(groups_container, text=f"Group {group_index+1} ({len(photo_indices)} photos)")
            group_frame.pack(fill=tk.X, expand=True, pady=10)

            # Create a frame for the thumbnails
            thumbs_frame = ttk.Frame(group_frame)
            thumbs_frame.pack(fill=tk.X, padx=10, pady=10)

            # Display up to 5 thumbnails
            for i, photo_index in enumerate(photo_indices[:5]):
                photo_path = self.photo_files[photo_index]

                try:
                    # Create thumbnail
                    img = PIL.Image.open(photo_path)
                    img.thumbnail((80, 80))
                    photo_img = ImageTk.PhotoImage(img)

                    # Create a label for the image
                    img_label = ttk.Label(thumbs_frame, image=photo_img)
                    img_label.image = photo_img  # Keep a reference
                    img_label.pack(side=tk.LEFT, padx=5)

                except Exception as e:
                    print(f"Error creating thumbnail: {str(e)}")

            # Show count if there are more photos
            if len(photo_indices) > 5:
                ttk.Label(thumbs_frame, text=f"+ {len(photo_indices) - 5} more").pack(side=tk.LEFT, padx=5)

            # Add group info
            info_frame = ttk.Frame(group_frame)
            info_frame.pack(fill=tk.X, padx=10, pady=(0, 10))

            # Get time range if available
            date_photos = [self.photo_data[idx] for idx in photo_indices if self.photo_data[idx]['date_taken']]
            if date_photos:
                start_date = min(date_photos, key=lambda x: x['date_taken'])['date_taken']
                end_date = max(date_photos, key=lambda x: x['date_taken'])['date_taken']

                date_str = f"Time: {start_date.strftime('%Y-%m-%d %H:%M')} to {end_date.strftime('%Y-%m-%d %H:%M')}"
                ttk.Label(info_frame, text=date_str).pack(anchor=tk.W)

            # Get location info if available
            gps_photos = [self.photo_data[idx] for idx in photo_indices
                         if self.photo_data[idx]['gps']['latitude'] is not None
                         and self.photo_data[idx]['gps']['longitude'] is not None]

            if gps_photos:
                # Calculate average location
                avg_lat = sum(p['gps']['latitude'] for p in gps_photos) / len(gps_photos)
                avg_lon = sum(p['gps']['longitude'] for p in gps_photos) / len(gps_photos)

                location_str = f"Location: around {avg_lat:.4f}, {avg_lon:.4f}"
                ttk.Label(info_frame, text=location_str).pack(anchor=tk.W)

            # Add save button
            save_btn = ttk.Button(
                info_frame,
                text="Save to Folder",
                command=lambda idx=group_index: self.save_group_to_folder(idx)
            )
            save_btn.pack(anchor=tk.E, pady=(10, 0))

    def save_group_to_folder(self, group_index):
        """Save a group of photos to a folder"""
        if group_index not in self.groups:
            return

        # Get the group
        photo_indices = self.groups[group_index]

        # Ask for destination folder
        folder_path = filedialog.askdirectory(title="Select Destination Folder")
        if not folder_path:
            return

        # Create a subfolder for the group
        group_folder = os.path.join(folder_path, f"Group_{group_index+1}")
        os.makedirs(group_folder, exist_ok=True)

        # Get output mode
        output_mode = self.output_mode.get()

        # Copy or move files
        for photo_index in photo_indices:
            source_path = self.photo_files[photo_index]
            dest_path = os.path.join(group_folder, os.path.basename(source_path))

            try:
                if output_mode == "copy":
                    shutil.copy2(source_path, dest_path)
                else:  # move
                    shutil.move(source_path, dest_path)
                    # Update the file path in our data
                    self.photo_files[photo_index] = dest_path
                    self.photo_data[photo_index]['path'] = dest_path
            except Exception as e:
                messagebox.showerror("Error", f"Failed to {output_mode} file {source_path}: {str(e)}")
                return

        # Show success message
        messagebox.showinfo("Success", f"Group {group_index+1} {output_mode}ed to {group_folder}")

    def save_all_groups(self):
        """Save all groups to folders"""
        if not self.groups:
            messagebox.showinfo("No Groups", "No groups to save.")
            return

        # Ask for destination folder
        folder_path = filedialog.askdirectory(title="Select Destination Folder")
        if not folder_path:
            return

        # Get output mode
        output_mode = self.output_mode.get()

        # Process each group
        for group_index, photo_indices in self.groups.items():
            # Create a subfolder for the group
            group_folder = os.path.join(folder_path, f"Group_{group_index+1}")
            os.makedirs(group_folder, exist_ok=True)

            # Copy or move files
            for photo_index in photo_indices:
                source_path = self.photo_files[photo_index]
                dest_path = os.path.join(group_folder, os.path.basename(source_path))

                try:
                    if output_mode == "copy":
                        shutil.copy2(source_path, dest_path)
                    else:  # move
                        shutil.move(source_path, dest_path)
                        # Update the file path in our data
                        self.photo_files[photo_index] = dest_path
                        self.photo_data[photo_index]['path'] = dest_path
                except Exception as e:
                    messagebox.showerror("Error", f"Failed to {output_mode} file {source_path}: {str(e)}")
                    return

        # Show success message
        messagebox.showinfo("Success", f"All groups {output_mode}ed to {folder_path}")

    def show_about(self):
        """Show the about dialog"""
        about_text = """Photo GPS & Time Grouper

A tool to group photos based on time and GPS information.

Features:
- Load multiple photos via drag or folder selection
- Group photos by time and/or GPS location
- Configurable time and distance thresholds
- Save groups to folders (copy or move)
"""
        messagebox.showinfo("About", about_text)

    def on_close(self):
        """Handle window close event"""
        if messagebox.askokcancel("Quit", "Do you want to quit?"):
            self.destroy()


if __name__ == "__main__":
    app = PhotoGrouper()
    app.mainloop()
